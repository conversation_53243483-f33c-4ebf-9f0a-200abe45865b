// Backend server for Gemini AI integration
// This keeps your API key secure on the server side

const express = require('express');
const cors = require('cors');
const { GoogleGenerativeAI } = require('@google/generative-ai');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3001;

// CORS configuration for your frontend
app.use(cors({
    origin: ['http://localhost:3000', 'http://127.0.0.1:5500'], // Add your frontend URLs
    credentials: true
}));

app.use(express.json());

// Initialize Gemini AI with API key from environment variable
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);

// Conversation history storage (in production, use a database)
const conversationHistory = new Map();

// System instruction for e-commerce context
const SYSTEM_INSTRUCTION = `You are <PERSON><PERSON><PERSON> AI, a helpful e-commerce assistant. You help customers with:
- Product recommendations and information
- Order tracking and status
- Return and refund policies
- General shopping assistance
- Customer support

Keep responses concise, friendly, and focused on e-commerce needs. If you don't know specific information about orders or products, guide users to contact support or check their account.`;

// Context management for sequential thinking
class ConversationContext {
    constructor(sessionId) {
        this.sessionId = sessionId;
        this.history = [];
        this.context = {
            userPreferences: {},
            currentTopic: null,
            lastInteraction: Date.now()
        };
    }

    addMessage(role, content) {
        this.history.push({
            role,
            parts: [{ text: content }],
            timestamp: Date.now()
        });
        
        // Keep only last 10 messages to manage context length
        if (this.history.length > 10) {
            this.history = this.history.slice(-10);
        }
        
        this.context.lastInteraction = Date.now();
    }

    getHistory() {
        return this.history;
    }

    updateContext(key, value) {
        this.context[key] = value;
    }
}

// Generate unique session ID
function generateSessionId() {
    return 'session_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
}

// Clean up old sessions (run every hour)
setInterval(() => {
    const oneHourAgo = Date.now() - (60 * 60 * 1000);
    for (const [sessionId, context] of conversationHistory.entries()) {
        if (context.context.lastInteraction < oneHourAgo) {
            conversationHistory.delete(sessionId);
        }
    }
}, 60 * 60 * 1000);

// Chat endpoint with sequential thinking
app.post('/api/chat', async (req, res) => {
    try {
        const { message, sessionId: clientSessionId } = req.body;
        
        if (!message || typeof message !== 'string') {
            return res.status(400).json({ 
                error: 'Message is required and must be a string' 
            });
        }

        // Get or create session
        let sessionId = clientSessionId;
        if (!sessionId || !conversationHistory.has(sessionId)) {
            sessionId = generateSessionId();
            conversationHistory.set(sessionId, new ConversationContext(sessionId));
        }

        const context = conversationHistory.get(sessionId);
        
        // Add user message to history
        context.addMessage('user', message);

        // Initialize the model with system instruction
        const model = genAI.getGenerativeModel({ 
            model: 'gemini-1.5-flash',
            systemInstruction: SYSTEM_INSTRUCTION,
            generationConfig: {
                temperature: 0.7,
                topP: 0.8,
                topK: 40,
                maxOutputTokens: 500,
            }
        });

        // Create chat with history for context awareness
        const chat = model.startChat({
            history: context.getHistory().slice(0, -1) // Exclude the current message
        });

        // Send message and get response
        const result = await chat.sendMessage(message);
        const response = result.response;
        const responseText = response.text();

        // Add AI response to history
        context.addMessage('model', responseText);

        // Update context based on message content
        if (message.toLowerCase().includes('order') || message.toLowerCase().includes('track')) {
            context.updateContext('currentTopic', 'order_tracking');
        } else if (message.toLowerCase().includes('return') || message.toLowerCase().includes('refund')) {
            context.updateContext('currentTopic', 'returns');
        } else if (message.toLowerCase().includes('product') || message.toLowerCase().includes('recommend')) {
            context.updateContext('currentTopic', 'product_help');
        }

        res.json({
            response: responseText,
            sessionId: sessionId,
            context: {
                topic: context.context.currentTopic,
                messageCount: context.history.length
            }
        });

    } catch (error) {
        console.error('Chat API Error:', error);
        
        // Handle specific Gemini API errors
        if (error.message?.includes('API_KEY')) {
            res.status(500).json({ 
                error: 'API configuration error. Please contact support.' 
            });
        } else if (error.message?.includes('QUOTA_EXCEEDED')) {
            res.status(429).json({ 
                error: 'Service temporarily unavailable. Please try again later.' 
            });
        } else {
            res.status(500).json({ 
                error: 'I apologize, but I encountered an error. Please try again or contact support if the issue persists.' 
            });
        }
    }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({ 
        status: 'healthy', 
        timestamp: new Date().toISOString(),
        activeSessions: conversationHistory.size
    });
});

// Start server
app.listen(PORT, () => {
    console.log(`Gemini AI Backend Server running on port ${PORT}`);
    console.log(`Health check: http://localhost:${PORT}/api/health`);
});

module.exports = app;

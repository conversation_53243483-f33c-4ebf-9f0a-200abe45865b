/* Use Inter font family */
body {
    font-family: 'Inter', sans-serif;
    overflow-x: hidden; /* Prevent horizontal scroll from animation */
}

/* Prevent automatic scrolling to focused elements */
html {
    scroll-behavior: auto;
}

/* Override any smooth scrolling on page load */
html.loading {
    scroll-behavior: auto !important;
}

/* ========================================
   PLATFORM IMPACT SHOWCASE STYLES
   ======================================== */

/* ========================================
   ENHANCED SCROLL-TRIGGERED ANIMATIONS
   ======================================== */

/* Base animation state - elements start hidden with blur */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px) translateZ(0);
    filter: blur(10px);
    transition: all 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    will-change: opacity, transform, filter;
    backface-visibility: hidden;
}

/* Visible state - smooth transition to final appearance */
.animate-on-scroll.visible {
    opacity: 1;
    transform: translateY(0) translateZ(0);
    filter: blur(0px);
}

/* Staggered animation delays for multiple elements */
.animate-on-scroll.delay-100 {
    transition-delay: 0.1s;
}

.animate-on-scroll.delay-200 {
    transition-delay: 0.2s;
}

.animate-on-scroll.delay-300 {
    transition-delay: 0.3s;
}

.animate-on-scroll.delay-400 {
    transition-delay: 0.4s;
}

.animate-on-scroll.delay-500 {
    transition-delay: 0.5s;
}

/* Faster animations for smaller elements */
.animate-on-scroll.fast {
    transition-duration: 0.5s;
}

/* Slower animations for larger sections */
.animate-on-scroll.slow {
    transition-duration: 0.9s;
}

/* Slide-in from left animation variant */
.animate-slide-left {
    opacity: 0;
    transform: translateX(-40px) translateZ(0);
    filter: blur(8px);
    transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    will-change: opacity, transform, filter;
}

.animate-slide-left.visible {
    opacity: 1;
    transform: translateX(0) translateZ(0);
    filter: blur(0px);
}

/* Slide-in from right animation variant */
.animate-slide-right {
    opacity: 0;
    transform: translateX(40px) translateZ(0);
    filter: blur(8px);
    transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    will-change: opacity, transform, filter;
}

.animate-slide-right.visible {
    opacity: 1;
    transform: translateX(0) translateZ(0);
    filter: blur(0px);
}

/* Scale-up animation for special elements */
.animate-scale-up {
    opacity: 0;
    transform: scale(0.9) translateZ(0);
    filter: blur(6px);
    transition: all 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
    will-change: opacity, transform, filter;
}

.animate-scale-up.visible {
    opacity: 1;
    transform: scale(1) translateZ(0);
    filter: blur(0px);
}

/* Section Title Styling */
.section-title {
    font-family: 'Inter', sans-serif;
    font-weight: 700;
    letter-spacing: -0.025em;
}

/* Metric Label and Value Styling */
.metric-label {
    font-weight: 500;
    color: #6b7280;
}

.metric-value {
    color: #3674B5;
}

/* Custom scrollbar for a cleaner look */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}
::-webkit-scrollbar-track {
    background: white;
}
::-webkit-scrollbar-thumb {
    background: #3674B5;
    border-radius: 10px;
}
::-webkit-scrollbar-thumb:hover {
    background: black;
}

/* Conveyor belt animation - Enhanced with smooth scrolling */
.animate-scrolling-logos {
    animation: scroll 60s linear infinite;
    will-change: transform;
}

@keyframes scroll {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-50%);
    }
}

/* Fade effect containers for left and right edges */
.logo-container {
    position: relative;
    overflow: hidden;
}

.logo-container::before,
.logo-container::after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    width: 120px;
    z-index: 10;
    pointer-events: none;
}

.logo-container::before {
    left: 0;
    background: linear-gradient(to right, white 0%, rgba(255, 255, 255, 0.8) 30%, rgba(255, 255, 255, 0.4) 70%, transparent 100%);
}

.logo-container::after {
    right: 0;
    background: linear-gradient(to left, white 0%, rgba(255, 255, 255, 0.8) 30%, rgba(255, 255, 255, 0.4) 70%, transparent 100%);
}

/* Logo styling improvements */
.brand-logo {
    transition: all 0.3s ease;
    filter: grayscale(60%) opacity(0.7);
}

.brand-logo:hover {
    filter: grayscale(0%) opacity(1);
    transform: scale(1.05);
}

/* E-commerce Dashboard Styles */
/* Loading animations */
@keyframes shimmer {
    0% { background-position: -200px 0; }
    100% { background-position: calc(200px + 100%) 0; }
}

/* Sticky Header Animations */
.header-compact {
    padding-top: 0.75rem !important; /* py-3 */
    padding-bottom: 0.75rem !important;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(249, 250, 251, 0.7), rgba(255, 255, 255, 0.8)) !important;
    backdrop-filter: blur(25px) saturate(180%) !important;
    -webkit-backdrop-filter: blur(25px) saturate(180%) !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 2px 16px rgba(0, 0, 0, 0.08), inset 0 1px 0 rgba(255, 255, 255, 0.5) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.4) !important;
    border-image: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent) 1 !important;
}

.header-compact .logo {
    font-size: 1.25rem !important; /* text-xl */
}

.header-compact .search-container input {
    width: 12rem !important; /* w-48 */
    padding-top: 0.375rem !important; /* py-1.5 */
    padding-bottom: 0.375rem !important;
}

.header-compact .nav-item {
    font-size: 0.875rem !important; /* text-sm */
}

/* Enhanced Glass Morphism Effects */
#main-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    pointer-events: none;
    z-index: -1;
}

/* Category Item Hover Effects */
.category-item {
    transition: color 0.3s ease;
}

.category-item:hover {
    color: #3674b5 !important;
}

#main-header::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
    pointer-events: none;
}

/* Glossy search bar enhancement */
.search-container input {
    background: rgba(255, 255, 255, 0.8) !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.6), 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.search-container input:focus {
    background: rgba(255, 255, 255, 0.9) !important;
    border-color: rgba(59, 130, 246, 0.5) !important;
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.8), 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.pulse {
    animation: pulse 2s infinite;
}

/* Advanced hover animations */
.product-card {
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
}

.product-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.product-card:hover .product-image {
    transform: scale(1.05);
}

.product-image {
    transition: transform 0.4s ease;
}

/* Ripple effect */
.ripple {
    position: relative;
    overflow: hidden;
}

.ripple::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.ripple:active::after {
    width: 300px;
    height: 300px;
}

/* Navigation enhancements */
.nav-item {
    position: relative;
    transition: all 0.3s ease;
}

.nav-item::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background: #3674B5;
    transition: width 0.3s ease;
}

.nav-item:hover::after {
    width: 100%;
}

.nav-item:hover {
    color: #3674B5;
    transform: translateY(-1px);
}

/* Cart animation */
.cart-icon {
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Heart icon hover effects */
.heart-container {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid transparent;
    cursor: pointer;
}

.heart-container:hover {
    border: 2px solid #ef4444 !important;
    transform: scale(1.05);
}

.heart-container:active {
    transform: scale(0.95);
}

/* Button enhancements */
.btn-primary {
    background: linear-gradient(135deg, #3674B5, #2563eb);
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, black, #1f2937);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

/* Floating elements */
.floating {
    animation: floating 3s ease-in-out infinite;
}

@keyframes floating {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Gradient overlays */
.gradient-overlay {
    background: linear-gradient(135deg, rgba(54, 116, 181, 0.05), rgba(0, 0, 0, 0.02));
}

/* Interactive rating stars */
.star-rating {
    display: flex;
    gap: 2px;
}

.star {
    color: #d1d5db;
    transition: color 0.2s ease;
    cursor: pointer;
}

.star.filled {
    color: #fbbf24;
}

.star:hover {
    color: #f59e0b;
    transform: scale(1.1);
}

/* Quantity selector */
.quantity-selector {
    display: flex;
    align-items: center;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    overflow: hidden;
}

.quantity-btn {
    background: #f9fafb;
    border: none;
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.quantity-btn:hover {
    background: #3674B5;
    color: white;
}

/* Search enhancement */
.search-container {
    position: relative;
}

.search-container:focus-within .search-icon {
    color: #3674B5;
    transform: scale(1.1);
}

.search-icon {
    transition: all 0.3s ease;
}

/* Social icons hover */
.social-icon {
    transition: all 0.3s ease;
}

.social-icon:hover {
    transform: translateY(-3px) scale(1.1);
    color: #3674B5;
}

/* Payment icons */
.payment-icon {
    transition: all 0.2s ease;
    filter: grayscale(100%);
}

.payment-icon:hover {
    filter: grayscale(0%);
    transform: scale(1.05);
}

/* Smooth scrolling */
.smooth-scroll {
    scroll-behavior: smooth;
}

/* Wishlist heart animation */
.wishlist-heart {
    transition: all 0.3s ease;
}

.wishlist-heart:hover {
    color: #ef4444;
    transform: scale(1.2);
}

.wishlist-heart.active {
    color: #ef4444;
    animation: heartBeat 0.6s ease;
}

@keyframes heartBeat {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.3); }
}

/* Page Header Styles - Enhanced from standalone navigation */
.page-header {
    backdrop-filter: blur(8px) brightness(1.05) contrast(1.02) saturate(1.1);
    -webkit-backdrop-filter: blur(8px) brightness(1.05) contrast(1.02) saturate(1.1);
    background: rgba(255, 255, 255, 0.45);
    border: 1px solid transparent; /* Hidden outline initially */
    border-radius: 50px;
    margin: 24px 64px 0 64px;
    transition: margin 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                backdrop-filter 0.3s ease,
                background 0.3s ease,
                border 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    will-change: margin, border;
}

.page-header.scrolled {
    backdrop-filter: blur(10px) brightness(1.08) contrast(1.05) saturate(1.15);
    -webkit-backdrop-filter: blur(10px) brightness(1.08) contrast(1.05) saturate(1.15);
    background: rgba(255, 255, 255, 0.55);
    border: 1px solid rgba(156, 163, 175, 0.4);
    margin: 24px 400px 0 400px; /* Edges slide all the way to center */
}

.page-nav-item {
    position: relative;
    font-size: 14px; /* Smaller default size to avoid snapping effect */
    transition: font-size 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.page-header.scrolled .page-nav-item {
    font-size: 13px; /* Only 1px smaller for subtle transition */
}

.page-header.scrolled nav {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
}

/* Mobile responsiveness for header compression - Enhanced */
@media (max-width: 768px) {
    .page-header {
        margin: 0; /* Remove margins for flat design */
        border-radius: 0; /* Remove rounded corners */
        border: none; /* Remove border */
        background: rgba(255, 255, 255, 0.85); /* Slightly more opaque for mobile */
        backdrop-filter: blur(12px) brightness(1.05) contrast(1.02) saturate(1.1);
        -webkit-backdrop-filter: blur(12px) brightness(1.05) contrast(1.02) saturate(1.1);
        border-bottom: 1px solid rgba(0, 0, 0, 0.1); /* Subtle bottom border */
    }

    .page-header.scrolled {
        margin: 0; /* Keep flat on scroll */
        border-radius: 0; /* Keep flat on scroll */
        background: rgba(255, 255, 255, 0.9); /* More opaque when scrolled */
        backdrop-filter: blur(16px) brightness(1.08) contrast(1.05) saturate(1.15);
        -webkit-backdrop-filter: blur(16px) brightness(1.08) contrast(1.05) saturate(1.15);
        border-bottom: 1px solid rgba(0, 0, 0, 0.15); /* Slightly more visible border */
    }

    /* Remove pseudo-elements on mobile for cleaner flat design */
    .page-header::before,
    .page-header::after {
        display: none;
    }
}

/* Shared Dropdown Container Styles */
.shared-dropdown-container {
    position: relative;
    display: inline-flex;
    align-items: center;
    gap: 24px;
}

.shared-dropdown {
    position: absolute;
    top: calc(100% + 24px); /* Position at bottom of header with small gap */
    left: 50%;
    transform: translateX(-50%);
    background: white;
    width: 500px; /* Increased width for more horizontal layout */
    height: 380px; /* Increased height to fit all 4 items properly */
    border-radius: 8px;
    z-index: 1000;
    border: 1px solid #e5e7eb;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    overflow: hidden;

    /* Default hidden state with blur fade transitions */
    opacity: 0;
    visibility: hidden;
    filter: blur(4px);
    transform: translateX(-50%) translateY(10px);
    transition: opacity 0.3s ease-out, filter 0.3s ease-out, visibility 0.3s ease-out, transform 0.3s ease-out;
}

.dropdown-slider {
    display: flex;
    width: 200%; /* Two panels side by side */
    height: 100%;
    transform: translateX(0%); /* Default position - Features panel visible */
    transition: transform 0.25s cubic-bezier(0.4, 0.0, 0.2, 1); /* Always smooth, faster transition */
    will-change: transform; /* Optimize for animations */
}

/* Instant positioning for Benefits first hover */
.shared-dropdown-container[style*="--instant-position"] .dropdown-slider {
    transform: translateX(var(--instant-position)) !important;
    transition: none !important;
}

.dropdown-panel {
    width: 50%;
    padding: 20px 24px; /* Restored padding for better content spacing */
    flex-shrink: 0;
    height: 100%; /* Ensure panels fill the container height */
    display: flex;
    flex-direction: column;
    box-sizing: border-box; /* Include padding in height calculation */
}

/* Show dropdown when hovering either link or the dropdown itself */
.features-link:hover ~ .shared-dropdown,
.benefits-link:hover ~ .shared-dropdown,
.shared-dropdown:hover {
    opacity: 1 !important; /* Force full opacity during all states */
    visibility: visible;
    filter: blur(0px);
    transform: translateX(-50%) translateY(0);
}

/* Maintain full opacity during active states */
.shared-dropdown-container.features-active .shared-dropdown,
.shared-dropdown-container.benefits-active .shared-dropdown {
    opacity: 1 !important;
    visibility: visible;
    filter: blur(0px);
    transform: translateX(-50%) translateY(0);
}

/* Only slide when explicitly switching between active states */
.shared-dropdown-container.features-active .dropdown-slider {
    transform: translateX(0%);
}

.shared-dropdown-container.benefits-active .dropdown-slider {
    transform: translateX(-50%);
}

/* Maintain position during fade-out - don't reset to default */
.shared-dropdown-container.fade-out-features .dropdown-slider {
    transform: translateX(0%);
}

.shared-dropdown-container.fade-out-benefits .dropdown-slider {
    transform: translateX(-50%);
}

/* Add small invisible bridge for both links */
.features-link,
.benefits-link {
    position: relative;
}

.features-link::after,
.benefits-link::after {
    content: '';
    position: absolute;
    top: 100%;
    left: -5px;
    right: -5px;
    height: 24px; /* Match the gap to dropdown */
    z-index: 999;
    display: none;
}

.features-link:hover::after,
.benefits-link:hover::after {
    display: block;
}

/* Dropdown content styles */
.dropdown-links {
    display: flex;
    flex-direction: column;
    gap: 0; /* Remove gap to use flex spacing instead */
    height: 100%;
    justify-content: space-evenly; /* Equal spacing between all 4 items */
    padding: 8px 0; /* Add vertical padding to container */
}

.dropdown-link {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 12px 16px; /* Increased padding for better spacing */
    text-decoration: none;
    color: #374151;
    border-radius: 6px;
    transition: all 0.2s ease;
    border: 1px solid transparent;
    flex: 1; /* Each item takes equal space */
    min-height: 70px; /* Minimum height for consistent spacing */
}

.dropdown-link:hover {
    color: #3674B5;
    background: #f8fafc;
    border-color: #e2e8f0;
}

.dropdown-link h3 {
    font-size: 15px; /* Slightly smaller for better fit */
    font-weight: 600;
    margin: 0 0 3px 0; /* Reduced margin */
    color: #111827;
    line-height: 1.3; /* Tighter line height */
}

.dropdown-link:hover h3 {
    color: #3674B5;
}

.dropdown-link p {
    font-size: 13px; /* Slightly smaller for better fit */
    color: #6b7280;
    margin: 0;
    line-height: 1.3; /* Tighter line height for better fit */
}

/* Features text animation styles */
.features-panel .dropdown-link {
    opacity: 0;
    filter: blur(6px);
    transform: translateY(30px);
    transition: opacity 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                filter 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Animate features text when panel is active */
.shared-dropdown-container.features-active .features-panel .dropdown-link {
    opacity: 1;
    filter: blur(0px);
    transform: translateY(0);
}

/* Sequential staggered animation delays - Smart Analytics to Secure Payments */
.features-panel .dropdown-link:nth-child(1) { /* Smart Analytics */
    transition-delay: 0.15s;
}

.features-panel .dropdown-link:nth-child(2) { /* Inventory Management */
    transition-delay: 0.35s;
}

.features-panel .dropdown-link:nth-child(3) { /* Customer Support */
    transition-delay: 0.55s;
}

.features-panel .dropdown-link:nth-child(4) { /* Secure Payments */
    transition-delay: 0.75s;
}

/* Benefits text animation styles */
.benefits-panel .dropdown-link {
    opacity: 0;
    filter: blur(6px);
    transform: translateY(30px);
    transition: opacity 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                filter 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Animate benefits text when panel is active */
.shared-dropdown-container.benefits-active .benefits-panel .dropdown-link {
    opacity: 1;
    filter: blur(0px);
    transform: translateY(0);
}

/* Sequential staggered animation delays - Quick Setup to Customer Intelligence */
.benefits-panel .dropdown-link:nth-child(1) { /* Quick Setup */
    transition-delay: 0.15s;
}

.benefits-panel .dropdown-link:nth-child(2) { /* Smart Inventory */
    transition-delay: 0.35s;
}

.benefits-panel .dropdown-link:nth-child(3) { /* Predictive Analytics */
    transition-delay: 0.55s;
}

.benefits-panel .dropdown-link:nth-child(4) { /* Customer Intelligence */
    transition-delay: 0.75s;
}

/* Dropdown Arrow Styles */
.dropdown-arrow {
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transform-origin: center;
}

/* Arrow animations on hover */
.features-link:hover .dropdown-arrow,
.benefits-link:hover .dropdown-arrow {
    transform: rotate(180deg);
}

/* Arrow animations when dropdown is active */
.shared-dropdown-container.features-active .features-link .dropdown-arrow,
.shared-dropdown-container.benefits-active .benefits-link .dropdown-arrow {
    transform: rotate(180deg);
}

/* Compressed header arrow adjustments */
.page-header.scrolled .dropdown-arrow {
    width: 14px;
    height: 14px;
}

/* Compressed header adjustments */
.page-header.scrolled .shared-dropdown {
    width: 500px; /* Maintain same width as normal state */
    height: 380px; /* Updated height to match normal state */
    top: calc(100% + 24px); /* Maintain same gap in compressed state */
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.15),
        rgba(255, 255, 255, 0.02),
        rgba(255, 255, 255, 0.08)
    );
    border-radius: 50px;
    pointer-events: none;
    z-index: -1;
}

.page-header::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
    border-radius: 50px;
    pointer-events: none;
    z-index: -1;
}

.page-header.scrolled {
    backdrop-filter: blur(10px) brightness(1.08) contrast(1.05) saturate(1.15);
    -webkit-backdrop-filter: blur(10px) brightness(1.08) contrast(1.05) saturate(1.15);
    background: rgba(255, 255, 255, 0.55);
    border: 1px solid rgba(156, 163, 175, 0.4);
    margin: 24px 400px 0 400px; /* Edges slide all the way to center */
}

/* Mobile Menu Styles */
.mobile-menu {
    position: fixed;
    top: 64px; /* Below the header */
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    z-index: 40;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                opacity 0.3s ease;
    opacity: 0;
}

/* Ensure mobile menu works with flat header on mobile */
@media (max-width: 768px) {
    .mobile-menu {
        top: 64px; /* Adjust for flat header height */
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px) saturate(180%);
        -webkit-backdrop-filter: blur(20px) saturate(180%);
    }
}

.mobile-menu.open {
    max-height: calc(100vh - 64px);
    opacity: 1;
}

.mobile-menu-content {
    padding: 20px;
    max-height: calc(100vh - 104px);
    overflow-y: auto;
}

.mobile-nav {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.mobile-nav-item {
    display: block;
    padding: 12px 16px;
    color: #374151;
    font-weight: 500;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.mobile-nav-item:hover {
    background: #f8fafc;
    color: #3674B5;
    border-color: #e2e8f0;
}

/* Mobile Dropdown Sections */
.mobile-dropdown-section {
    margin: 4px 0;
}

.mobile-dropdown-toggle {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background: none;
    border: 1px solid transparent;
    border-radius: 8px;
    color: #374151;
    font-weight: 500;
    text-align: left;
    cursor: pointer;
    transition: all 0.2s ease;
}

.mobile-dropdown-toggle:hover {
    background: #f8fafc;
    color: #3674B5;
    border-color: #e2e8f0;
}

.mobile-dropdown-toggle.active .mobile-dropdown-arrow {
    transform: rotate(180deg);
}

.mobile-dropdown-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    background: #f8fafc;
    border-radius: 8px;
    margin-top: 4px;
}

.mobile-dropdown-content.open {
    max-height: 400px;
}

.mobile-dropdown-item {
    display: block;
    padding: 12px 20px;
    text-decoration: none;
    color: #374151;
    border-bottom: 1px solid #e5e7eb;
    transition: all 0.2s ease;
}

.mobile-dropdown-item:last-child {
    border-bottom: none;
}

.mobile-dropdown-item:hover {
    background: #ffffff;
    color: #3674B5;
}

.mobile-dropdown-item h4 {
    font-size: 14px;
    font-weight: 600;
    margin: 0 0 4px 0;
    color: inherit;
}

.mobile-dropdown-item p {
    font-size: 12px;
    color: #6b7280;
    margin: 0;
    line-height: 1.3;
}

.mobile-dropdown-item:hover p {
    color: #4b5563;
}

/* Mobile CTA Button */
.mobile-cta {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e5e7eb;
}

/* ========================================
   MOBILE-SPECIFIC PRICING SECTION REDESIGN
   ======================================== */

/* Mobile Pricing Section - Complete Redesign for Mobile Only */
@media (max-width: 768px) {
    /* Mobile Pricing Container */
    #pricing-section {
        padding: 40px 16px;
    }

    #pricing-section .container {
        max-width: 100%;
        margin: 0;
    }

    /* Mobile Pricing Header */
    #pricing-section .text-center.mb-16 {
        margin-bottom: 32px;
    }

    #pricing-section .section-title {
        font-size: 2rem;
        line-height: 1.2;
        margin-bottom: 16px;
    }

    #pricing-section .text-lg.text-gray-600 {
        font-size: 1rem;
        padding: 0 8px;
    }

    /* Mobile Pricing Grid - Stack Vertically */
    #pricing-section .grid {
        display: flex;
        flex-direction: column;
        gap: 20px;
        align-items: center;
    }

    /* Mobile Pricing Cards - Horizontal Layout */
    .pricing-card {
        aspect-ratio: unset !important;
        min-height: unset !important;
        max-width: 100% !important;
        width: 100% !important;
        padding: 20px !important;
        border-radius: 16px !important;
        display: flex !important;
        flex-direction: row !important;
        align-items: center !important;
        justify-content: space-between !important;
        position: relative !important;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
    }

    /* Mobile Card Content Layout */
    .pricing-card > * {
        flex-shrink: 0;
    }

    /* Mobile Plan Name and Price Section */
    .pricing-card h3 {
        text-align: left !important;
        margin-bottom: 4px !important;
        font-size: 0.875rem !important;
    }

    .pricing-card .mt-4.flex.items-baseline.justify-center {
        margin-top: 0 !important;
        justify-content: flex-start !important;
        margin-bottom: 0 !important;
    }

    .pricing-card .mt-4.flex.items-baseline.justify-center span:first-child {
        font-size: 2rem !important;
        font-weight: 700 !important;
    }

    .pricing-card .mt-4.flex.items-baseline.justify-center span:last-child {
        font-size: 0.875rem !important;
        margin-left: 4px !important;
    }

    .pricing-card .mt-1.text-sm.text-gray-500.text-center {
        text-align: left !important;
        margin-top: 2px !important;
        font-size: 0.75rem !important;
    }

    /* Hide flex-grow spacer on mobile */
    .pricing-card .flex-grow {
        display: none !important;
    }

    /* Mobile Features List - Compact */
    .pricing-card ul {
        display: none !important; /* Hide detailed features on mobile */
    }

    /* Mobile Separator */
    .pricing-card hr {
        display: none !important;
    }

    /* Mobile Subscribe Button */
    .pricing-card .subscribe-btn,
    .pricing-card .button {
        margin-top: 0 !important;
        padding: 12px 24px !important;
        font-size: 0.875rem !important;
        border-radius: 8px !important;
        white-space: nowrap !important;
        min-width: 100px !important;
    }

    /* Mobile Description Text */
    .pricing-card p:last-child {
        display: none !important; /* Hide description on mobile */
    }

    /* Mobile Pro Card Special Styling */
    .pricing-card-pro {
        border-color: #3674B5 !important;
        border-width: 2px !important;
        background: linear-gradient(135deg, #f8faff 0%, #ffffff 100%) !important;
    }

    /* Mobile Popular Badge - Repositioned */
    .pricing-card-pro .popular-badge {
        position: absolute !important;
        top: -8px !important;
        right: 16px !important;
        left: unset !important;
        transform: none !important;
        font-size: 0.75rem !important;
        padding: 4px 12px !important;
        border-radius: 12px !important;
    }

    /* Mobile Card Layout Structure */
    .pricing-card {
        display: grid !important;
        grid-template-columns: 1fr auto !important;
        grid-template-rows: auto auto !important;
        gap: 8px 16px !important;
        align-items: center !important;
    }

    /* Mobile Left Column - Plan Info */
    .pricing-card > h3,
    .pricing-card > .mt-4.flex.items-baseline.justify-center,
    .pricing-card > .mt-1.text-sm.text-gray-500.text-center {
        grid-column: 1 !important;
    }

    /* Mobile Right Column - Button */
    .pricing-card > .subscribe-btn,
    .pricing-card > .button {
        grid-column: 2 !important;
        grid-row: 1 / -1 !important;
        align-self: center !important;
    }

    /* Mobile Key Features - Show 2-3 key features inline */
    .pricing-card::after {
        content: attr(data-features);
        grid-column: 1 !important;
        font-size: 0.75rem !important;
        color: #6b7280 !important;
        line-height: 1.3 !important;
        margin-top: 4px !important;
    }

    /* Mobile Specific Feature Text */
    .pricing-card-basic::after {
        content: "1 User • 5GB Storage • Basic Support";
    }

    .pricing-card-pro::after {
        content: "5 Users • 50GB Storage • Priority Support";
    }

    .pricing-card-enterprise::after {
        content: "Unlimited Users • 500GB • Premium Support";
    }

    /* Mobile Animation Adjustments */
    .pricing-card.animate-slide-left,
    .pricing-card.animate-slide-right,
    .pricing-card.animate-scale-up {
        animation: mobileCardSlideIn 0.6s ease-out forwards !important;
    }

    @keyframes mobileCardSlideIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Disable desktop pricing animations on mobile */
    .pricing-card.animate-closer,
    .pricing-card.animate-grow {
        animation: none !important;
        transform: none !important;
    }
}

/* ========================================
   MOBILE-SPECIFIC FAQ SECTION REDESIGN
   ======================================== */

/* Mobile FAQ Section - Complete Redesign for Mobile Only */
@media (max-width: 768px) {
    /* Mobile FAQ Container */
    #faq-section {
        padding: 40px 16px;
        background: linear-gradient(135deg, #f8faff 0%, #ffffff 100%);
    }

    #faq-section .container {
        max-width: 100%;
        margin: 0;
    }

    /* Mobile FAQ Grid - Stack Vertically */
    #faq-section .grid {
        display: flex !important;
        flex-direction: column !important;
        gap: 24px !important;
    }

    /* Mobile FAQ Header Section */
    #faq-section .md\\:col-span-2 {
        order: 1;
        text-align: center;
    }

    #faq-section .max-w-xs {
        max-width: 100% !important;
        margin: 0 auto;
    }

    #faq-section .section-title {
        font-size: 2rem !important;
        line-height: 1.2 !important;
        margin-bottom: 12px !important;
        text-align: center !important;
    }

    #faq-section .section-title br {
        display: none; /* Remove line break on mobile */
    }

    /* Show description on mobile */
    #faq-section .mt-1.hidden.md\\:block {
        display: block !important;
        text-align: center !important;
        font-size: 0.95rem !important;
        color: #6b7280 !important;
        margin-top: 8px !important;
    }

    /* Mobile FAQ Accordion Section */
    #faq-section .md\\:col-span-3 {
        order: 2;
    }

    /* Mobile Accordion Container */
    .hs-accordion-group {
        background: white !important;
        border-radius: 16px !important;
        padding: 8px !important;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
        border: 1px solid #e5e7eb !important;
        divide-y: none !important;
    }

    /* Mobile Individual FAQ Items */
    .hs-accordion {
        background: #f9fafb !important;
        border-radius: 12px !important;
        margin-bottom: 8px !important;
        padding: 16px !important;
        border: 1px solid #f3f4f6 !important;
        transition: all 0.3s ease !important;
    }

    .hs-accordion:last-child {
        margin-bottom: 0 !important;
    }

    .hs-accordion.active,
    .hs-accordion:hover {
        background: #ffffff !important;
        border-color: #3674B5 !important;
        box-shadow: 0 2px 4px rgba(54, 116, 181, 0.1) !important;
    }

    /* Mobile FAQ Question Button */
    .hs-accordion-toggle {
        padding: 0 !important;
        font-size: 1rem !important;
        font-weight: 600 !important;
        color: #1f2937 !important;
        line-height: 1.4 !important;
        text-align: left !important;
        gap: 12px !important;
    }

    .hs-accordion.active .hs-accordion-toggle {
        color: #3674B5 !important;
    }

    /* Mobile FAQ Icons */
    .hs-accordion-toggle svg {
        width: 20px !important;
        height: 20px !important;
        flex-shrink: 0 !important;
        color: #6b7280 !important;
        transition: all 0.3s ease !important;
    }

    .hs-accordion.active .hs-accordion-toggle svg {
        color: #3674B5 !important;
        transform: rotate(180deg) !important;
    }

    /* Mobile FAQ Answer Content */
    .hs-accordion-content {
        margin-top: 12px !important;
        padding-top: 12px !important;
        border-top: 1px solid #e5e7eb !important;
    }

    .hs-accordion-content p {
        font-size: 0.9rem !important;
        line-height: 1.5 !important;
        color: #4b5563 !important;
        margin: 0 !important;
    }

    /* Mobile FAQ Spacing Adjustments */
    .hs-accordion.pt-6 {
        padding-top: 16px !important;
    }

    .hs-accordion.pb-3 {
        padding-bottom: 16px !important;
    }

    /* Mobile FAQ Animation */
    .hs-accordion {
        animation: mobileFaqSlideIn 0.5s ease-out forwards;
    }

    .hs-accordion:nth-child(1) { animation-delay: 0.1s; }
    .hs-accordion:nth-child(2) { animation-delay: 0.2s; }
    .hs-accordion:nth-child(3) { animation-delay: 0.3s; }
    .hs-accordion:nth-child(4) { animation-delay: 0.4s; }
    .hs-accordion:nth-child(5) { animation-delay: 0.5s; }
    .hs-accordion:nth-child(6) { animation-delay: 0.6s; }

    @keyframes mobileFaqSlideIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Mobile FAQ Header Animation */
    #faq-section .animate-slide-left {
        animation: mobileFaqHeaderSlide 0.8s ease-out forwards;
    }

    @keyframes mobileFaqHeaderSlide {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Disable desktop FAQ animations on mobile */
    #faq-section .animate-slide-right {
        animation: none !important;
    }
}

/* Mobile responsiveness for header compression */
@media (max-width: 768px) {
    .page-header {
        margin: 16px 16px 0 16px;
    }

    .page-header.scrolled {
        margin: 16px 140px 0 140px; /* Edges slide to center on mobile */
    }
}

.page-nav-item {
    position: relative;
}

/* AI Text Special Effects */
.ai-text {
    font-weight: bold;
}

/* Maintain AI colors on hover */
a:hover .ai-text span:first-child {
    color: #3674B5 !important; /* Blue A */
}

a:hover .ai-text span:last-child {
    color: #ffffff !important; /* White I on hover */
}

/* Compact Inventory Dashboard Animations */
.pulse-dot {
    animation: pulse-gentle 2s infinite;
}

@keyframes pulse-gentle {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.1);
    }
}

.stock-bar-container:hover .stock-fill {
    filter: brightness(1.15);
    transition: filter 0.3s ease;
}

.group:hover .stock-fill {
    animation: shimmer-stock 1.2s ease-in-out;
}

@keyframes shimmer-stock {
    0% {
        filter: brightness(1);
    }
    50% {
        filter: brightness(1.3);
    }
    100% {
        filter: brightness(1);
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    .pulse-dot {
        animation: none;
    }

    .stock-fill {
        transition: none;
    }

    .group:hover .stock-fill {
        animation: none;
        filter: none;
    }
}

/* ========================================
   MIGRATED CSS FROM TESTING.HTML
   ======================================== */

/* ========================================
   GLOBAL TYPOGRAPHY & FONT SETTINGS
   ======================================== */

/* Enhanced Global Font Family - Overrides previous body font settings */
body, html {
    font-family: 'DM Sans', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    font-feature-settings: 'kern' 1, 'liga' 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'DM Sans', 'Inter', sans-serif;
    font-weight: 700;
    letter-spacing: -0.01em;
}

p, span, div {
    font-family: 'DM Sans', 'Inter', sans-serif;
}

/* ========================================
   TEXT EFFECTS & ANIMATIONS
   ======================================== */

/* Shine Text Effect with Cross-browser Support */
.shine-text {
    background: #3674B5 -webkit-gradient(linear, left top, right top, from(#3674B5), to(#3674B5), color-stop(0.5, #fff)) 0 0 no-repeat;
    -webkit-background-size: 200px;
    background-size: 200px;
    color: #3674B5;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    -webkit-animation-name: shine;
    -webkit-animation-duration: 5s;
    -webkit-animation-iteration-count: infinite;
    -webkit-animation-timing-function: linear;
    animation-name: shine;
    animation-duration: 5s;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
    text-shadow: 0 0px 0px rgba(255, 255, 255, 0.5);
}

/* Sweet Title Styling for Modern Typography */
.sweet-title {
    font-family: 'DM Sans', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    color: #3674B5;
    font-weight: 800;
    text-transform: none;
    line-height: 1.1em;
    text-align: center;
    letter-spacing: -0.02em;
    text-shadow: none;
}

.sweet-title span {
    display: block;
    position: relative;
}

.sweet-title span:before {
    content: none;
}

.sweet-title span:nth-child(1) {
    padding-right: 0;
}

.sweet-title span:nth-child(2) {
    padding-left: 0;
}

/* Shine Animation Keyframes */
@-webkit-keyframes shine {
    0%, 40% {
        background-position: -200px;
    }
    60% {
        background-position: -200px;
    }
    100% {
        background-position: 200px;
    }
}

@keyframes shine {
    0%, 40% {
        background-position: -200px;
    }
    60% {
        background-position: -200px;
    }
    100% {
        background-position: 200px;
    }
}

/* Character-by-Character Text Animation */
.text-animate-container {
    display: inline-block;
}

.text-animate-char {
    display: inline-block;
    opacity: 0;
    filter: blur(10px);
    animation: blurInChar 0.8s ease-out forwards;
}

@keyframes blurInChar {
    to {
        opacity: 1;
        filter: blur(0px);
    }
}

/* Stagger Animation Delays for Characters */
.text-animate-char:nth-child(1) { animation-delay: 0.1s; }
.text-animate-char:nth-child(2) { animation-delay: 0.15s; }
.text-animate-char:nth-child(3) { animation-delay: 0.2s; }
.text-animate-char:nth-child(4) { animation-delay: 0.25s; }
.text-animate-char:nth-child(5) { animation-delay: 0.3s; }
.text-animate-char:nth-child(6) { animation-delay: 0.35s; }
.text-animate-char:nth-child(7) { animation-delay: 0.4s; }
.text-animate-char:nth-child(8) { animation-delay: 0.45s; }
.text-animate-char:nth-child(9) { animation-delay: 0.5s; }
.text-animate-char:nth-child(10) { animation-delay: 0.55s; }
.text-animate-char:nth-child(11) { animation-delay: 0.6s; }
.text-animate-char:nth-child(12) { animation-delay: 0.65s; }

/* ========================================
   BENTO GRID COMPONENTS & HOVER EFFECTS
   ======================================== */

/* Icon Container Animations */
.icon-container {
    transition: all 0.3s ease;
}

.icon-container:hover {
    transform: scale(1.05);
}

.icon-container:hover svg {
    color: #3674B5;
}

/* Bento Grid Hover Blur Effect */
.bento-grid {
    transition: all 0.3s ease;
}

.bento-card {
    transition: all 0.4s ease;
}

.bento-grid:hover .bento-card:not(:hover) {
    filter: blur(4px);
    transform: scale(0.98);
    opacity: 0.7;
}

.bento-grid:hover .bento-card:hover {
    transform: scale(1.02);
    z-index: 10;
    position: relative;
}

/* ========================================
   INVENTORY DASHBOARD ANIMATIONS
   ======================================== */

/* Inventory Progress Bar Animations */
.inventory-progress-fill {
    width: 1%; /* Start with 1% so color is visible */
    transition: width 2.5s cubic-bezier(0.4, 0, 0.2, 1);
    min-height: 100%;
    display: block;
}

/* Target colors for each category */
.inventory-progress-fill.electronics {
    background: #3674B5 !important;
}

.inventory-progress-fill.clothing {
    background: #f59e0b !important;
}

.inventory-progress-fill.home {
    background: #ef4444 !important;
}

/* Pulse animation for status dots */
.inventory-pulse-dot {
    animation: inventory-pulse-gentle 2s infinite;
}

@keyframes inventory-pulse-gentle {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.1);
    }
}

/* Number counter styling */
.inventory-counter {
    font-variant-numeric: tabular-nums;
}

/* ========================================
   AI CHAT INTERFACE ANIMATIONS
   ======================================== */

/* Chat Container Styling */
.chat-container {
    scroll-behavior: smooth;
}

/* Modern Scrollbar Styling for Chat */
.chat-container::-webkit-scrollbar {
    width: 4px;
}

.chat-container::-webkit-scrollbar-track {
    background: transparent;
}

.chat-container::-webkit-scrollbar-thumb {
    background: rgba(54, 116, 181, 0.3);
    border-radius: 2px;
    transition: background 0.2s ease;
}

.chat-container::-webkit-scrollbar-thumb:hover {
    background: rgba(54, 116, 181, 0.5);
}

/* Firefox scrollbar styling */
.chat-container {
    scrollbar-width: thin;
    scrollbar-color: rgba(54, 116, 181, 0.3) transparent;
}

/* Chat Header Glass Effect */
.chat-header {
    background: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(16px) saturate(180%);
    -webkit-backdrop-filter: blur(16px) saturate(180%);
    border: 1px solid rgba(156, 163, 175, 0.4);
    border-bottom: 1px solid rgba(156, 163, 175, 0.3);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Message and Animation Styling */
.message-bubble {
    opacity: 0;
    transform: translateY(20px);
    animation: messageSlideIn 0.5s ease-out forwards;
}

.typing-indicator {
    opacity: 0;
    animation: fadeIn 0.3s ease-out forwards;
}

.thinking-animation {
    opacity: 0;
    animation: fadeIn 0.3s ease-out forwards;
}

.ai-avatar {
    animation: avatarPulse 2s ease-in-out infinite;
}

.thinking-dots {
    animation: thinkingDots 1.5s ease-in-out infinite;
}

/* Chat Keyframe Animations */
@keyframes messageSlideIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    to {
        opacity: 1;
    }
}

@keyframes avatarPulse {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(54, 116, 181, 0.4);
    }
    50% {
        box-shadow: 0 0 0 4px rgba(54, 116, 181, 0);
    }
}

@keyframes thinkingDots {
    0%, 20% {
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 0;
    }
}

/* Thinking stages animation */
.thinking-stage {
    opacity: 0;
    transform: scale(0.9);
    transition: all 0.3s ease-out;
}

.thinking-stage.active {
    opacity: 1;
    transform: scale(1);
}

/* Message timestamp fade */
.message-timestamp {
    opacity: 0;
    animation: fadeIn 0.5s ease-out 0.3s forwards;
}

/* ========================================
   UTILITY CLASSES FOR INLINE STYLES
   ======================================== */

/* Brand Color Utilities */
.bg-brand-blue {
    background-color: #3674B5;
}

.text-brand-blue {
    color: #3674B5;
}

.border-brand-blue {
    border-color: #3674B5;
}

/* Font Family Utilities */
.font-dm-sans {
    font-family: 'DM Sans', 'Inter', sans-serif;
}

.font-dm-sans-medium {
    font-family: 'DM Sans', 'Inter', sans-serif;
    font-weight: 500;
}

.font-dm-sans-bold {
    font-family: 'DM Sans', 'Inter', sans-serif;
    font-weight: 700;
    letter-spacing: -0.01em;
}

.font-dm-sans-extrabold {
    font-family: 'DM Sans', 'Inter', sans-serif;
    font-weight: 800;
    letter-spacing: -0.02em;
}

/* Typography Utilities */
.text-content {
    font-family: 'DM Sans', 'Inter', sans-serif;
    font-weight: 400;
    line-height: 1.6;
}

/* Backdrop Filter Utilities */
.backdrop-blur-enhanced {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

/* Gradient Utilities */
.gradient-ai-avatar {
    background: linear-gradient(135deg, #3674B5 0%, #4A90E2 100%);
}

.gradient-brand-message {
    background: #3674B5;
}

/* Animation Delay Utilities */
.animation-delay-200 {
    animation-delay: 0.2s;
}

.animation-delay-400 {
    animation-delay: 0.4s;
}

/* ========================================
   DASHBOARD DISCLAIMER OVERLAY STYLES
   ======================================== */

/* Disclaimer Overlay Container - Regular blur effect */
.dashboard-disclaimer-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.75);
    backdrop-filter: blur(4px) saturate(120%);
    -webkit-backdrop-filter: blur(4px) saturate(120%);
    border-radius: 8px; /* Match the inner container border radius */
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 1;
    visibility: visible;
    transition: opacity 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                visibility 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                backdrop-filter 0.6s ease;
    font-family: 'Inter', sans-serif;
}

/* Hidden state for disclaimer overlay */
.dashboard-disclaimer-overlay.hidden {
    opacity: 0;
    visibility: hidden;
    backdrop-filter: blur(0px) saturate(100%);
    -webkit-backdrop-filter: blur(0px) saturate(100%);
    pointer-events: none;
}

/* Disclaimer Content Container - Rectangular with fade animation */
.disclaimer-content {
    background: white;
    border: 1px solid #d1d5db; /* Thin gray outline */
    border-radius: 12px; /* Slightly rounded corners */
    padding: 32px 40px; /* Increased padding */
    width: 420px; /* Wider rectangular width */
    height: 240px; /* Shorter height for rectangle */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    /* Removed all shadows */
    /* Fade in animation */
    opacity: 0;
    transform: translateY(20px);
    animation: disclaimerFadeIn 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.3s forwards;
    transition: opacity 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Fade in animation for disclaimer container */
@keyframes disclaimerFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Disclaimer Icon Styling - No animation */
.disclaimer-icon {
    margin-bottom: 12px; /* Reduced margin */
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Disclaimer Title - No animation */
.disclaimer-title {
    font-family: 'Inter', sans-serif;
    font-size: 18px; /* Smaller font size */
    font-weight: 700;
    color: #3674B5;
    margin-bottom: 8px; /* Reduced margin */
    letter-spacing: -0.02em;
}

/* Disclaimer Text - No animation */
.disclaimer-text {
    font-family: 'Inter', sans-serif;
    font-size: 13px; /* Smaller font size */
    font-weight: 400;
    color: #6b7280;
    line-height: 1.4; /* Tighter line height */
    margin-bottom: 16px; /* Reduced margin */
}

/* Disclaimer button now uses subscribe-btn class */
/* Ensure disclaimer button has proper styling from the start */
#disclaimerContinueBtn {
    border: 1px solid #d1d5db !important;
    outline: none !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08),
                0 1px 2px rgba(0, 0, 0, 0.06) !important;
}

#disclaimerContinueBtn:focus {
    border: 1px solid #d1d5db !important;
    outline: none !important;
}

/* Old disclaimer button styles removed - now uses subscribe-btn */

/* Confirmation Message Styles - With background blur */
.confirmation-message {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.75);
    backdrop-filter: blur(4px) saturate(120%);
    -webkit-backdrop-filter: blur(4px) saturate(120%);
    border-radius: 8px; /* Match the inner container border radius */
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                visibility 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                backdrop-filter 0.5s ease;
    pointer-events: none;
    font-family: 'Inter', sans-serif;
}

.confirmation-message.show {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
}

/* Hidden state for confirmation message */
.confirmation-message.hidden {
    opacity: 0;
    visibility: hidden;
    backdrop-filter: blur(0px) saturate(100%);
    -webkit-backdrop-filter: blur(0px) saturate(100%);
    pointer-events: none;
}

.confirmation-content {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 20px 24px;
    display: flex;
    align-items: center;
    font-family: 'Inter', sans-serif;
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1),
                0 4px 8px rgba(0, 0, 0, 0.06);
    transform: scale(0.95);
    transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.confirmation-message.show .confirmation-content {
    transform: scale(1);
}

/* Old individual element animations removed - only container animates now */

/* Ensure proper positioning within showcase container */
#showcase {
    position: relative;
}

/* Ensure the inner dashboard container can contain the overlay */
#showcase .bg-white.rounded-lg.border.border-gray-400 {
    position: relative;
}

/* Brand color utilities for disclaimer */
.text-brand-blue {
    color: #3674B5;
}

/* Enhanced button focus states for accessibility */
.disclaimer-button:focus {
    outline: 2px solid #3674B5;
    outline-offset: 2px;
}

.disclaimer-button:focus:not(:focus-visible) {
    outline: none;
}

.disclaimer-button:focus-visible {
    outline: 2px solid #3674B5;
    outline-offset: 2px;
}

/* ========================================
   ENHANCED ACCESSIBILITY SUPPORT
   ======================================== */

/* Comprehensive Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    /* Disable scroll-triggered animations for reduced motion */
    .animate-on-scroll,
    .animate-slide-left,
    .animate-slide-right,
    .animate-scale-up {
        opacity: 1 !important;
        transform: none !important;
        filter: none !important;
        transition: none !important;
        will-change: auto !important;
    }

    .animate-on-scroll.visible,
    .animate-slide-left.visible,
    .animate-slide-right.visible,
    .animate-scale-up.visible {
        opacity: 1 !important;
        transform: none !important;
        filter: none !important;
    }

    /* Disable inventory animations */
    .inventory-pulse-dot {
        animation: none;
    }

    .inventory-progress-fill {
        transition: none;
    }

    /* Disable text animations */
    .text-animate-char {
        opacity: 1;
        filter: blur(0px);
        animation: none;
    }

    /* Disable chat animations */
    .message-bubble,
    .typing-indicator,
    .thinking-animation,
    .ai-avatar,
    .thinking-dots,
    .thinking-stage,
    .message-timestamp {
        animation: none;
        transition: none;
        opacity: 1;
        transform: none;
    }

    .chat-container {
        scroll-behavior: auto;
    }

    /* Disable bento grid hover effects for reduced motion */
    .bento-grid:hover .bento-card:not(:hover) {
        filter: none;
        transform: none;
        opacity: 1;
    }

    .bento-grid:hover .bento-card:hover {
        transform: none;
    }

    .bento-card {
        transition: none;
    }

    /* Disable shine animation */
    .shine-text {
        -webkit-animation: none;
        animation: none;
    }

    /* Disable disclaimer animations for reduced motion */
    .disclaimer-icon,
    .disclaimer-title,
    .disclaimer-text,
    .disclaimer-button {
        animation: none !important;
        opacity: 1 !important;
        transform: none !important;
    }

    .dashboard-disclaimer-overlay,
    .confirmation-message {
        transition: none !important;
    }

    .disclaimer-content,
    .confirmation-content {
        transition: none !important;
        transform: none !important;
    }
}

/* ===== FEATURE SLIDESHOW COMPONENT ===== */
/* Inter font family for all slideshow text elements */
.feature-slideshow-container,
.feature-slideshow-container * {
    font-family: 'Inter', sans-serif !important;
}

/* Specific font declarations for slideshow elements */
.feature-slideshow-container h1,
.feature-slideshow-container h2,
.feature-slideshow-container h3,
.feature-slideshow-container p {
    font-family: 'Inter', sans-serif !important;
}

/* Feature item text styling with Inter font */
.feature-title {
    font-family: 'Inter', sans-serif !important;
}

.feature-description {
    font-family: 'Inter', sans-serif !important;
}

/* Style for the progress bar fill to have a smooth transition */
.progress-fill {
    transition: height 5s linear; /* Duration should match the JS slideDuration */
    will-change: height;
    transform: translateZ(0); /* Force hardware acceleration */
    background-color: #3674B5 !important; /* Brand color for progress bar fill */
}

/* Animation for feature items - compact inactive state with progress bar transitions */
.feature-item {
    transition: height 0.4s cubic-bezier(0.32, 0, 0.67, 0),
               padding 0.4s cubic-bezier(0.32, 0, 0.67, 0),
               transform 0.35s cubic-bezier(0.16, 1, 0.3, 1);
    overflow: hidden;
    will-change: height, padding, transform;
    transform: translateZ(0) translateY(0); /* Force hardware acceleration */
    backface-visibility: hidden; /* Prevent flickering */
    transform-origin: top center; /* Anchor collapse animation to top */
    cursor: pointer; /* Indicate clickable items */
    height: 60px; /* More compact collapsed height */
    display: flex;
    align-items: flex-start;
    padding: 12px 4px; /* Reduced padding for compact appearance */
}

/* Subtle hover effect for non-active items */
.feature-item:not(.active):hover {
    transform: translateZ(0) translateY(0) scale(0.99); /* Gentle hover feedback */
}

/* Compact inactive state - visually compressed */
.feature-item:not(.active) {
    height: 60px; /* Compact collapsed height */
    padding: 12px 4px; /* Reduced padding for tighter spacing */
    transform: translateZ(0) translateY(0) scale(0.985); /* Very subtle scale reduction */
}

/* Active state - expanded with normal spacing */
.feature-item.active {
    height: 140px; /* Expanded height for active item */
    padding: 16px 4px; /* Normal padding for active state */
    transform: translateZ(0) translateY(0) scale(1); /* Normal scale and position */
}

/* Professional collapse animation class - compact sizing */
.feature-item.collapsing {
    transition: height 0.45s cubic-bezier(0.25, 0, 0.3, 1),
               padding 0.45s cubic-bezier(0.25, 0, 0.3, 1),
               transform 0.4s cubic-bezier(0.16, 1, 0.3, 1),
               opacity 0.3s cubic-bezier(0.4, 0, 1, 1);
    transform: translateZ(0) translateY(0) scale(0.98); /* Professional scale without floating */
    height: 60px; /* Collapse to compact height */
    padding: 12px 4px; /* Compact padding during collapse */
}

/* Smooth transitions for text elements - optimized */
.feature-title {
    transition: color 0.25s ease-out;
    will-change: color;
    transform: translateZ(0);
}

.feature-description {
    transition: opacity 0.3s cubic-bezier(0.4, 0, 1, 1),
               transform 0.35s cubic-bezier(0.16, 1, 0.3, 1);
    will-change: opacity, transform;
    transform: translateZ(0) translateY(0);
}

/* Professional minimalistic description collapse animation (no floating) */
.feature-description.collapsing {
    opacity: 0;
    transform: translateZ(0) translateY(0); /* Clean fade without floating */
    transition: opacity 0.2s cubic-bezier(0.4, 0, 1, 1),
               transform 0.3s cubic-bezier(0.25, 0, 0.3, 1);
}

/* Smooth icon transitions - optimized */
.feature-item svg {
    transition: color 0.25s ease-out;
    will-change: color;
    transform: translateZ(0);
}

/* Progress container smooth transitions - optimized with responsive thickness */
.progress-container {
    transition: width 0.4s cubic-bezier(0.32, 0, 0.67, 0);
    will-change: width;
    transform: translateZ(0);
}

/* Thin progress bar for inactive items - compact appearance */
.feature-item:not(.active) .progress-container {
    width: 1px; /* Thinner progress bar for compact inactive state */
}

/* Normal thickness progress bar for active items */
.feature-item.active .progress-container {
    width: 2px; /* Normal thickness for active state */
}

/* Progress bar during collapse animation */
.feature-item.collapsing .progress-container {
    width: 1px; /* Thin during collapse transition */
}

/* Placeholder text transition animation - smooth and minimalistic */
#feature-placeholder {
    transition: opacity 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    will-change: opacity;
}

/* Placeholder text styling - Inter font for consistency */
#feature-placeholder p {
    font-family: 'Inter', sans-serif;
    letter-spacing: 0.05em;
    color: #9CA3AF; /* Gray-400 */
    font-weight: 300;
    font-size: 1.125rem; /* text-lg */
}

/* Skeleton animation optimization - hidden by default */
#image-skeleton {
    transition: opacity 0.2s ease-out;
    will-change: opacity;
    transform: translateZ(0);
}

#image-skeleton.hidden {
    opacity: 0;
    pointer-events: none;
}

/* Image container stability - prevent layout shifts */
.image-container {
    contain: layout style;
    min-height: 16rem; /* Ensure minimum height */
}

/* Soft, subtle shadow effect - bottom-focused with gentle diffusion */
.soft-shadow {
    box-shadow:
        0 4px 6px -1px rgba(0, 0, 0, 0.05),     /* Subtle top shadow */
        0 8px 16px -4px rgba(0, 0, 0, 0.08),    /* Main bottom shadow */
        0 16px 24px -8px rgba(0, 0, 0, 0.04),   /* Extended bottom diffusion */
        0 2px 4px -1px rgba(0, 0, 0, 0.03);     /* Close ambient shadow */
}

@media (min-width: 640px) {
    .image-container {
        min-height: 20rem;
    }
}

@media (min-width: 768px) {
    .image-container {
        min-height: 24rem;
    }
}

/* ========================================
   PRICING SECTION STYLES
   ======================================== */

/* Pricing card animations */
.pricing-card {
    transition: transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transform: translateX(0);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

.pricing-card-basic.animate-closer {
    animation: moveBasicCloser 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.8s forwards;
}

.pricing-card-enterprise.animate-closer {
    animation: moveEnterpriseCloser 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.8s forwards;
}

.pricing-card-pro.animate-grow {
    animation: growProCard 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.8s forwards;
}

@keyframes moveBasicCloser {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(45px);
    }
}

@keyframes moveEnterpriseCloser {
    0% {
        transform: translateX(0) translateZ(0);
    }
    100% {
        transform: translateX(-45px) translateZ(-50px);
    }
}

@keyframes growProCard {
    0% {
        transform: scale(1);
    }
    100% {
        transform: scale(1.05);
    }
}

/* Z-index layering for cards */
.pricing-card-basic {
    z-index: 3;
}

.pricing-card-pro {
    z-index: 5;
}

.pricing-card-enterprise {
    z-index: 2;
}

.pricing-card-enterprise.animate-closer {
    transform-origin: center center;
}

/* Animated Badge Styles */
.popular-badge {
    position: relative;
    margin: 0;
    padding: 0.8em 1em;
    outline: none;
    text-decoration: none;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    border: none;
    text-transform: uppercase;
    background-color: #3674B5;
    border-radius: 10px;
    color: #fff;
    font-weight: 300;
    font-size: 12px;
    font-family: inherit;
    z-index: 0;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.02, 0.01, 0.47, 1);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

.popular-badge:hover {
    animation: sh0 0.5s ease-in-out both;
}

@keyframes sh0 {
    0% {
        transform: rotate(0deg) translate3d(0, 0, 0);
    }
    25% {
        transform: rotate(7deg) translate3d(0, 0, 0);
    }
    50% {
        transform: rotate(-7deg) translate3d(0, 0, 0);
    }
    75% {
        transform: rotate(1deg) translate3d(0, 0, 0);
    }
    100% {
        transform: rotate(0deg) translate3d(0, 0, 0);
    }
}

.popular-badge:hover span {
    animation: storm 0.7s ease-in-out both;
    animation-delay: 0.06s;
}

.popular-badge::before,
.popular-badge::after {
    content: '';
    position: absolute;
    right: 0;
    bottom: 0;
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: #fff;
    opacity: 0;
    transition: transform 0.15s cubic-bezier(0.02, 0.01, 0.47, 1), opacity 0.15s cubic-bezier(0.02, 0.01, 0.47, 1);
    z-index: -1;
    transform: translate(100%, -25%) translate3d(0, 0, 0);
}

.popular-badge:hover::before,
.popular-badge:hover::after {
    opacity: 0.15;
    transition: transform 0.2s cubic-bezier(0.02, 0.01, 0.47, 1), opacity 0.2s cubic-bezier(0.02, 0.01, 0.47, 1);
}

.popular-badge:hover::before {
    transform: translate3d(50%, 0, 0) scale(0.9);
}

.popular-badge:hover::after {
    transform: translate(50%, 0) scale(1.1);
}

@keyframes storm {
    0% {
        transform: translate3d(0, 0, 0) translateZ(0);
    }
    25% {
        transform: translate3d(4px, 0, 0) translateZ(0);
    }
    50% {
        transform: translate3d(-3px, 0, 0) translateZ(0);
    }
    75% {
        transform: translate3d(2px, 0, 0) translateZ(0);
    }
    100% {
        transform: translate3d(0, 0, 0) translateZ(0);
    }
}

/* Animated Subscribe Button Styles */
.button {
    --h-button: 48px;
    --w-button: 102px;
    --round: 0.75rem;
    cursor: pointer;
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    transition: all 0.25s ease;
    background: radial-gradient(
        65.28% 65.28% at 50% 100%,
        rgba(54, 116, 181, 0.8) 0%,
        rgba(54, 116, 181, 0) 100%
    ),
    linear-gradient(0deg, #3674B5, #3674B5);
    border-radius: var(--round);
    border: none;
    outline: none;
    padding: 12px 18px;
    width: 100%;
    text-decoration: none;
    color: white;
    font-weight: 500;
    font-size: 16px;
}

.button::before,
.button::after {
    content: "";
    position: absolute;
    inset: var(--space);
    transition: all 0.5s ease-in-out;
    border-radius: calc(var(--round) - var(--space));
    z-index: 0;
}

.button::before {
    --space: 1px;
    background: linear-gradient(
        177.95deg,
        rgba(255, 255, 255, 0.19) 0%,
        rgba(255, 255, 255, 0) 100%
    );
}

.button::after {
    --space: 2px;
    background: radial-gradient(
        65.28% 65.28% at 50% 100%,
        rgba(54, 116, 181, 0.8) 0%,
        rgba(54, 116, 181, 0) 100%
    ),
    linear-gradient(0deg, #3674B5, #3674B5);
}

.button:active {
    transform: scale(0.95);
}

.fold {
    z-index: 1;
    position: absolute;
    top: 0;
    right: 0;
    height: 1rem;
    width: 1rem;
    display: inline-block;
    transition: all 0.5s ease-in-out;
    background: radial-gradient(
        100% 75% at 55%,
        rgba(54, 116, 181, 0.8) 0%,
        rgba(54, 116, 181, 0) 100%
    );
    box-shadow: 0 0 3px black;
    border-bottom-left-radius: 0.5rem;
    border-top-right-radius: var(--round);
}

.fold::after {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    width: 150%;
    height: 150%;
    transform: rotate(45deg) translateX(0%) translateY(-18px);
    background-color: #e8e8e8;
    pointer-events: none;
}

.button:hover .fold {
    margin-top: -1rem;
    margin-right: -1rem;
}

.points_wrapper {
    overflow: hidden;
    width: 100%;
    height: 100%;
    pointer-events: none;
    position: absolute;
    z-index: 1;
}

.points_wrapper .point {
    bottom: -10px;
    position: absolute;
    animation: floating-points infinite ease-in-out;
    pointer-events: none;
    width: 2px;
    height: 2px;
    background-color: #fff;
    border-radius: 9999px;
}

@keyframes floating-points {
    0% {
        transform: translateY(0);
    }
    85% {
        opacity: 0;
    }
    100% {
        transform: translateY(-55px);
        opacity: 0;
    }
}

.points_wrapper .point:nth-child(1) {
    left: 10%;
    opacity: 1;
    animation-duration: 2.35s;
    animation-delay: 0.2s;
}

.points_wrapper .point:nth-child(2) {
    left: 30%;
    opacity: 0.7;
    animation-duration: 2.5s;
    animation-delay: 0.5s;
}

.points_wrapper .point:nth-child(3) {
    left: 25%;
    opacity: 0.8;
    animation-duration: 2.2s;
    animation-delay: 0.1s;
}

.points_wrapper .point:nth-child(4) {
    left: 44%;
    opacity: 0.6;
    animation-duration: 2.05s;
}

.points_wrapper .point:nth-child(5) {
    left: 50%;
    opacity: 1;
    animation-duration: 1.9s;
}

.points_wrapper .point:nth-child(6) {
    left: 75%;
    opacity: 0.5;
    animation-duration: 1.5s;
    animation-delay: 1.5s;
}

.points_wrapper .point:nth-child(7) {
    left: 88%;
    opacity: 0.9;
    animation-duration: 2.2s;
    animation-delay: 0.2s;
}

.points_wrapper .point:nth-child(8) {
    left: 58%;
    opacity: 0.8;
    animation-duration: 2.25s;
    animation-delay: 0.2s;
}

.points_wrapper .point:nth-child(9) {
    left: 98%;
    opacity: 0.6;
    animation-duration: 2.6s;
    animation-delay: 0.1s;
}

.points_wrapper .point:nth-child(10) {
    left: 65%;
    opacity: 1;
    animation-duration: 2.5s;
    animation-delay: 0.2s;
}

.inner {
    z-index: 2;
    gap: 6px;
    position: relative;
    width: 100%;
    color: white;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: 500;
    line-height: 1.5;
    transition: color 0.2s ease-in-out;
}

/* Basic and Enterprise Button Hover Effects */
.subscribe-btn {
    border-radius: 8px;
    height: 40px;
    border: 1px solid #d1d5db !important; /* Force thin gray border */
    background: white;
    color: #333;
    padding: 15px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08),
                0 1px 2px rgba(0, 0, 0, 0.06);
    font-weight: bold;
    font-family: 'Inter', sans-serif;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 300ms cubic-bezier(0.25, 0.1, 0.25, 1);
    width: 100%;
    text-decoration: none;
    outline: none !important; /* Remove browser default outline */
}

.subscribe-btn:hover {
    background: #3674B5;
    color: white;
    border-color: #3674B5;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(54, 116, 181, 0.15),
                0 2px 4px rgba(54, 116, 181, 0.1);
}

.subscribe-btn:active {
    scale: 0.95;
    background: #2a5a94;
    color: white;
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1),
                0 1px 1px rgba(0, 0, 0, 0.06);
}

/* ========================================
   INTERACTIVE TESTIMONIALS SECTION STYLES
   ======================================== */

/* Smooth animations for stats */
.stat-number {
    transition: transform 0.3s ease, color 0.3s ease;
}

.stat-card:hover .stat-number {
    transform: scale(1.05);
    color: #3674B5;
}

/* Testimonial card hover effect */
.testimonial-card {
    transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.testimonial-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(54, 116, 181, 0.1);
}

/* Quote icon animation */
.quote-icon {
    transition: transform 0.3s ease;
}

.testimonial-card:hover .quote-icon {
    transform: scale(1.1) rotate(-2deg);
}

/* Avatar hover effect */
.avatar {
    transition: transform 0.3s ease;
}

.testimonial-card:hover .avatar {
    transform: scale(1.1);
}

/* ========================================
   FAQ ACCORDION SECTION STYLES
   ======================================== */

/* Accordion base styles */
.hs-accordion-toggle {
    font-family: 'Inter', sans-serif;
    transition: all 0.3s ease;
}

.hs-accordion-toggle:hover {
    color: #3674B5 !important;
}

.hs-accordion-toggle:focus {
    outline: none;
    color: #3674B5 !important;
}

/* Accordion content animation */
.hs-accordion-content {
    transition: height 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
}

/* Accordion arrow rotation */
.hs-accordion-toggle svg {
    transition: transform 0.3s ease;
}

.hs-accordion.active .hs-accordion-toggle svg.hs-accordion-active\:hidden {
    transform: rotate(180deg);
}

/* FAQ section spacing */
#faq-section {
    background: linear-gradient(135deg, #f1f5f9 0%, #f8fafc 100%);
}

/* Built for Success section background */
#built-for-success {
    background: linear-gradient(135deg, #f1f5f9 0%, #f8fafc 100%) !important;
}

/* ========================================
   ENHANCED MINIMALISTIC FOOTER STYLES
   ======================================== */

/* Footer base styling */
footer {
    font-family: 'Inter', sans-serif;
    background: white;
    border-top: 1px solid #f3f4f6;
}

/* Footer brand link styling */
footer a[href="#hero"] {
    font-family: 'Inter', sans-serif;
    font-weight: 700;
    transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

footer a[href="#hero"]:hover {
    color: #3674B5;
}

/* Footer navigation links */
footer ul li a {
    font-family: 'Inter', sans-serif;
    font-weight: 400;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

footer ul li a:hover {
    color: #3674B5;
    transform: translateX(2px);
}

/* Footer section headings */
footer h3 {
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    letter-spacing: 0.05em;
}

/* Footer text elements */
footer p,
footer span {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
}

/* Social media icons hover effects */
footer a[aria-label] svg {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

footer a[aria-label]:hover svg {
    transform: translateY(-2px);
    filter: drop-shadow(0 4px 8px rgba(54, 116, 181, 0.3));
}

/* Footer bottom section */
footer .border-t {
    border-color: #f3f4f6;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    footer {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    footer .grid {
        gap: 2rem;
    }

    footer .lg\\:col-span-1 {
        text-align: center;
    }

    footer .flex.items-center.space-x-4 {
        justify-content: center;
    }
}

/* Dark mode support */
.dark footer {
    background: #1f2937;
    border-top: 1px solid #374151;
}

.dark footer h3 {
    color: #f9fafb;
}

.dark footer p,
.dark footer span {
    color: #d1d5db;
}

.dark footer ul li a {
    color: #9ca3af;
}

.dark footer ul li a:hover {
    color: #3674B5;
}

.dark footer .border-t {
    border-color: #374151;
}

/* ========================================
   HERO SECTION UTILITIES
   ======================================== */

/* Custom gradient text utility */
.bg-linear-to-tl {
    background: linear-gradient(to top left, var(--tw-gradient-stops));
}

/* Ensure proper gradient text rendering */
.bg-clip-text {
    -webkit-background-clip: text;
    background-clip: text;
}

/* Custom shadow utility */
.shadow-2xs {
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

/* Focus outline hidden utility */
.focus\:outline-hidden:focus {
    outline: none;
}

/* Hero Primary Button - Uiverse.io Design */
.hero-primary-btn {
    min-width: 120px;
    position: relative;
    cursor: pointer;
    padding: 12px 17px;
    border: 0;
    border-radius: 7px;
    box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.1);
    background: radial-gradient(ellipse at bottom, #3674B5 0%, #1e3a5f 45%);
    color: rgba(255, 255, 255, 0.9);
    transition: all 0.6s cubic-bezier(0.15, 0.83, 0.66, 1);
}

.hero-primary-btn::before {
    content: "";
    width: 70%;
    height: 1px;
    position: absolute;
    bottom: 0;
    left: 15%;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 50%, rgba(255, 255, 255, 0) 100%);
    opacity: 0.3;
    transition: all 0.6s cubic-bezier(0.15, 0.83, 0.66, 1);
}

.hero-primary-btn:hover {
    color: rgb(255, 255, 255);
    transform: scale(1.05) translateY(-2px);
}

.hero-primary-btn:hover::before {
    opacity: 1;
}

/* ========================================
   CTA SECTION STYLES
   ======================================== */

/* CTA Background Pattern with Fade Effect */
.cta-background {
    position: relative;
}

.cta-background .cta-top-fade {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 200px;
    background: linear-gradient(to bottom, #ffffff 0%, rgba(255, 255, 255, 0.95) 12%, rgba(255, 255, 255, 0.88) 25%, rgba(255, 255, 255, 0.78) 38%, rgba(255, 255, 255, 0.65) 50%, rgba(255, 255, 255, 0.5) 62%, rgba(255, 255, 255, 0.35) 75%, rgba(255, 255, 255, 0.2) 85%, rgba(255, 255, 255, 0.08) 95%, transparent 100%);
    z-index: -1;
    pointer-events: none;
}

.cta-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    opacity: 0.7;
    background:
        radial-gradient(
            circle,
            transparent 20%,
            #ffffff 20%,
            #ffffff 80%,
            transparent 80%,
            transparent
        ),
        radial-gradient(
            circle,
            transparent 20%,
            #ffffff 20%,
            #ffffff 80%,
            transparent 80%,
            transparent
        ) 12.5px 12.5px,
        linear-gradient(rgba(54, 116, 181, 0.3) 1px, transparent 1px) 0 -0.5px,
        linear-gradient(90deg, rgba(54, 116, 181, 0.3) 1px, #ffffff 1px) -0.5px 0;
    background-size:
        25px 25px,
        25px 25px,
        12.5px 12.5px,
        12.5px 12.5px;
    z-index: -1;
}

.cta-background::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 200px;
    background: linear-gradient(to top, #f8fafc 0%, rgba(248, 250, 252, 0.95) 12%, rgba(248, 250, 252, 0.88) 25%, rgba(248, 250, 252, 0.78) 38%, rgba(248, 250, 252, 0.65) 50%, rgba(248, 250, 252, 0.5) 62%, rgba(248, 250, 252, 0.35) 75%, rgba(248, 250, 252, 0.2) 85%, rgba(248, 250, 252, 0.08) 95%, transparent 100%);
    z-index: -1;
    pointer-events: none;
}

/* CTA Ripple Effect */
.cta-ripple-effect {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.4);
    transform: scale(0);
    animation: ctaRippleAnimation 0.6s linear;
    pointer-events: none;
}

@keyframes ctaRippleAnimation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* ========================================
   HYBRID BUTTON STYLES (CTA SECTION)
   ======================================== */

/* Hybrid Button - Wave Animation + Sophisticated Styling */
.hybrid-btn {
    overflow: hidden;
    position: relative;
    min-width: 180px;
    padding: 12px 24px;
    height: 56px;
    border: 0;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    z-index: 10;
    transition: all 1000ms ease;
    margin: 0 auto;
    display: block;
    box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.1);
    background: radial-gradient(ellipse at bottom, #3674B5 0%, #1e3a5f 45%);
    color: rgba(255, 255, 255, 0.8);
    transition: all 1s cubic-bezier(0.15, 0.83, 0.66, 1);
}

.hybrid-btn:hover {
    color: rgb(255, 255, 255) !important;
    transform: scale(1.05) translateY(-2px) !important;
}



{"name": "sentio-ai-chatbot-backend", "version": "1.0.0", "description": "Backend server for Sentio AI chatbot with Gemini AI integration", "main": "chatbot-backend.js", "scripts": {"start": "node chatbot-backend.js", "dev": "nodemon chatbot-backend.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["chatbot", "gemini-ai", "e-commerce", "ai-assistant"], "author": "Your Name", "license": "MIT", "dependencies": {"@google/generative-ai": "^0.21.0", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2"}, "devDependencies": {"nodemon": "^3.1.0"}, "engines": {"node": ">=18.0.0"}}
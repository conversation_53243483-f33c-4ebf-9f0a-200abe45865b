# Sentio AI Chatbot with Gemini AI Integration

## 🚀 Simple Setup Guide

### 1. Get Your Gemini API Key
1. Go to [Google AI Studio](https://aistudio.google.com/app/apikey)
2. Create a new API key
3. Copy the key (starts with `AIzaSy...`)

### 2. Install Dependencies
```bash
npm install
```

### 3. Setup Environment
1. Copy `.env.example` to `.env`
2. Add your API key:
```env
GEMINI_API_KEY=AIzaSyBwFmbxQOLyDyMzw_-rJ1qYfyvO2dQ_OvI
PORT=3001
```

### 4. Start the Backend
```bash
npm start
```

### 5. Open Your Chatbot
Open `chatbot-standalone.html` in your browser. Done! 🎉

## 🧠 What You Get

### AI Features:
- **Smart Responses**: Real Gemini AI instead of template responses
- **Context Memory**: Remembers your conversation
- **Sequential Thinking**: AI can reason through complex questions
- **Fallback Safety**: Uses basic responses if AI is down

### Security:
- ✅ API key stays on your server (never exposed to users)
- ✅ Simple CORS protection
- ✅ Automatic error handling

## 🔧 How It Works

```
Your HTML Page ←→ Node.js Server ←→ Gemini AI
```

1. User types message in your chatbot
2. Frontend sends to your backend server
3. Server calls Gemini AI safely
4. AI response comes back to user

## 🚨 Troubleshooting

**Backend won't start?**
- Make sure you have Node.js installed
- Check your API key is correct in `.env`

**Chatbot shows "AI unavailable"?**
- Make sure backend server is running (`npm start`)
- Check browser console for errors

**Getting CORS errors?**
- Make sure you're opening the HTML file properly (not just double-clicking)
- Try using a local server like Live Server in VS Code

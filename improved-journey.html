<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Improved Journey On Coding</title>

    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        :root {
            --brand-blue: #3674B5;
        }

        /* Text Sharpening and Quality Enhancement */
        * {
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        /* Ensure no blur effects on text during hover states */
        .achievement-card *,
        .achievement-card *:hover,
        .achievement-item *,
        .achievement-item *:hover,
        .discovery-card-3d *,
        .discovery-card-3d *:hover,
        [data-card="technical-wins"] *,
        [data-card="technical-wins"] *:hover,
        [data-card="personal-growth"] *,
        [data-card="personal-growth"] *:hover {
            filter: none !important;
            -webkit-filter: none !important;
            backdrop-filter: none !important;
            -webkit-backdrop-filter: none !important;
        }

        /* Override any blur animations on text elements */
        h1, h2, h3, h4, h5, h6, p, span, div {
            filter: none !important;
            -webkit-filter: none !important;
        }

        /* Prevent transition-all from affecting filter properties */
        .achievement-item.transition-all,
        .achievement-item[class*="transition-all"],
        .achievement-card .achievement-item {
            transition: background-color 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease, color 0.3s ease !important;
            filter: none !important;
            -webkit-filter: none !important;
        }

        /* Force no filter on all achievement item children */
        .achievement-item > *,
        .achievement-item > * > *,
        .achievement-item h3,
        .achievement-item p {
            filter: none !important;
            -webkit-filter: none !important;
            transition: color 0.3s ease, background-color 0.3s ease, transform 0.3s ease !important;
        }

        /* Specific protection for Discoveries and Realizations achievement items */
        [data-card="discoveries"] .achievement-item,
        [data-card="realizations"] .achievement-item {
            transition: background-color 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease !important;
            filter: none !important;
            -webkit-filter: none !important;
        }

        [data-card="discoveries"] .achievement-item *,
        [data-card="realizations"] .achievement-item *,
        [data-card="discoveries"] .achievement-item h3,
        [data-card="realizations"] .achievement-item h3,
        [data-card="discoveries"] .achievement-item p,
        [data-card="realizations"] .achievement-item p {
            filter: none !important;
            -webkit-filter: none !important;
            transition: color 0.3s ease, background-color 0.3s ease, transform 0.3s ease !important;
        }

        /* Ensure crisp text rendering on all interactive elements */
        .achievement-card:hover *,
        .achievement-item:hover *,
        .discovery-card-3d:hover *,
        [data-card="technical-wins"]:hover *,
        [data-card="personal-growth"]:hover *,
        [data-card="technical-wins"] .achievement-item:hover *,
        [data-card="personal-growth"] .achievement-item:hover *,
        [data-item]:hover *,
        [data-item="technical-1"]:hover *,
        [data-item="technical-2"]:hover *,
        [data-item="technical-3"]:hover *,
        [data-item="technical-4"]:hover *,
        [data-item="personal-1"]:hover *,
        [data-item="personal-2"]:hover *,
        [data-item="personal-3"]:hover *,
        [data-item="personal-4"]:hover * {
            text-rendering: optimizeLegibility !important;
            -webkit-font-smoothing: antialiased !important;
            -moz-osx-font-smoothing: grayscale !important;
            filter: none !important;
            -webkit-filter: none !important;
        }

        .font-inter { font-family: 'Inter', sans-serif; }
        .text-brand { color: var(--brand-blue); }
        .bg-brand { background-color: var(--brand-blue); }
        .border-brand { border-color: var(--brand-blue); }

        /* Advanced Typography System */
        .typography-hero {
            font-weight: 800;
            font-size: clamp(2.5rem, 5vw, 4rem);
            line-height: 1.1;
            letter-spacing: -0.02em;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .typography-section-header {
            font-weight: 700;
            font-size: clamp(1.75rem, 3vw, 2.25rem);
            line-height: 1.2;
            letter-spacing: -0.01em;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .typography-subsection {
            font-weight: 600;
            font-size: clamp(1.125rem, 2vw, 1.25rem);
            line-height: 1.3;
            letter-spacing: -0.005em;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .typography-body-large {
            font-weight: 400;
            font-size: clamp(1.125rem, 1.5vw, 1.25rem);
            line-height: 1.6;
            color: #4b5563;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .typography-body {
            font-weight: 400;
            font-size: clamp(0.875rem, 1.2vw, 1rem);
            line-height: 1.7;
            color: #6b7280;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .typography-caption {
            font-weight: 500;
            font-size: clamp(0.75rem, 1vw, 0.875rem);
            line-height: 1.5;
            color: #9ca3af;
            letter-spacing: 0.025em;
        }

        /* Drop cap styling for breakthrough moments */
        .drop-cap::first-letter {
            float: left;
            font-size: 4.5em;
            line-height: 0.8;
            padding-right: 8px;
            padding-top: 4px;
            font-weight: 800;
            color: var(--brand-blue);
        }

        /* Enhanced visual hierarchy */
        .visual-emphasis {
            font-weight: 600;
            color: #1f2937;
        }

        .subtle-emphasis {
            font-weight: 500;
            color: #374151;
        }

        /* Modern Breakthrough Moment Styles */
        .breakthrough-moment-modern {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: 1px solid #e2e8f0;
            border-radius: 20px;
            padding: 2.5rem;
            margin: 2rem 0;
            position: relative;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }



        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            100% { background-position: 100% 50%; }
        }

        .breakthrough-moment-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 40px rgba(54, 116, 181, 0.1);
            border-color: #cbd5e1;
        }

        .breakthrough-title-modern {
            font-weight: 700;
            font-size: clamp(1.25rem, 2.5vw, 1.5rem);
            color: #1e293b;
            margin-bottom: 1.5rem;
            line-height: 1.3;
        }

        .breakthrough-quote {
            padding: 3rem 2.5rem 2.5rem;
            margin: 2rem 0;
            text-align: center;
            position: relative;
        }

        .quote-icon {
            position: absolute;
            top: -1rem;
            left: 50%;
            transform: translateX(-50%);
            width: 50px;
            height: 50px;
            opacity: 0.7;
        }

        .quote-text {
            font-size: clamp(1.125rem, 2vw, 1.375rem);
            line-height: 1.6;
            color: #374151;
            margin-bottom: 1.5rem;
            font-weight: 500;
        }

        .quote-text .highlight {
            color: var(--brand-blue);
            font-weight: 600;
        }

        .quote-text .quote-highlight {
            color: var(--brand-blue);
            font-weight: 600;
        }

        .quote-label {
            font-size: 0.875rem;
            font-weight: 600;
            color: #9ca3af;
            text-transform: uppercase;
            letter-spacing: 0.1em;
        }

        /* Quote Animation Styles */
        .quote-container {
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                       transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .quote-container.animate {
            opacity: 1;
            transform: translateY(0);
        }

        .quote-text {
            opacity: 0;
            filter: blur(2px);
            transform: translateY(10px);
            transition: opacity 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                       filter 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                       transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .quote-text.animate {
            opacity: 1;
            filter: blur(0px);
            transform: translateY(0);
        }

        .quote-highlight {
            opacity: 0;
            filter: blur(3px);
            transform: scale(0.95);
            transition: opacity 0.5s cubic-bezier(0.34, 1.56, 0.64, 1),
                       filter 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                       transform 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
        }

        .quote-highlight.animate {
            opacity: 1;
            filter: blur(0px);
            transform: scale(1);
        }

        .quote-attribution {
            opacity: 0;
            transform: translateY(15px);
            transition: opacity 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                       transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .quote-attribution.animate {
            opacity: 1;
            transform: translateY(0);
        }

        /* Animated Quote Text */
        .animated-quote {
            opacity: 0;
            animation: fadeInUp 0.8s ease-out forwards;
        }

        .animated-quote.delay-1 { animation-delay: 0.2s; }
        .animated-quote.delay-2 { animation-delay: 0.4s; }
        .animated-quote.delay-3 { animation-delay: 0.6s; }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Skills Grid Modern */
        .skills-grid-modern {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            background: white;
            border-radius: 16px;
            padding: 2rem;
            border: 1px solid #f1f5f9;
        }

        /* Animated Counter Styles */
        .animated-counter {
            font-weight: 900;
            transition: color 0.3s ease;
        }

        .counter-animating {
            color: var(--brand-blue);
        }

        /* Character Blur-In Animation Styles */
        .blur-in-title {
            display: inline-block;
        }

        .blur-char {
            display: inline-block;
            filter: blur(8px);
            opacity: 0;
            transform: translateY(10px);
            transition: all 0.6s ease-out;
        }

        .blur-char.animate-in {
            filter: blur(0px);
            opacity: 1;
            transform: translateY(0);
        }

        /* Preserve spaces in the animation */
        .blur-char.space {
            width: 0.3em;
        }

        /* Word Slide-Up Animation Styles */
        .slide-up-text {
            display: inline-block;
        }

        .slide-word {
            display: inline-block;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease-out;
            margin-right: 0.25em;
        }

        .slide-word.animate-in {
            opacity: 1;
            transform: translateY(0);
        }

        /* Preserve line breaks and spacing */
        .slide-word:last-child {
            margin-right: 0;
        }

        /* Animated Counter Styles */
        .animated-counter {
            font-weight: 700;
            color: var(--brand-blue);
            transition: all 0.3s ease;
        }

        .animated-counter.counting {
            transform: scale(1.05);
        }

        .skills-column-modern {
            position: relative;
        }

        .skills-column-modern h4 {
            font-weight: 600;
            font-size: 1.125rem;
            color: #1e293b;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #f1f5f9;
        }

        .skills-list-modern {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .skills-list-modern li {
            padding: 0.5rem 0;
            color: #64748b;
            font-size: 0.9rem;
            position: relative;
            padding-left: 1.5rem;
            transition: color 0.3s ease;
        }

        .skills-list-modern li::before {
            content: '→';
            position: absolute;
            left: 0;
            color: var(--brand-blue);
            font-weight: 600;
            transition: transform 0.3s ease;
        }

        .skills-list-modern li:hover {
            color: #475569;
        }

        .skills-list-modern li:hover::before {
            transform: translateX(4px);
        }
        
        /* Custom animations */
        @keyframes slideInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes progressFill {
            from { width: 0%; }
            to { width: var(--progress-width); }
        }
        
        .animate-slide-in { animation: slideInUp 0.6s ease-out; }
        .animate-progress { animation: progressFill 2s ease-out; }

        /* Smooth Butter Animations for Questions Section */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes scaleIn {
            from {
                opacity: 0;
                transform: scale(0.8);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        .animate-fade-in-up {
            animation: fadeInUp 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        }
        .animate-scale-in {
            animation: scaleIn 0.6s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
        }
        .animate-slide-in-right {
            animation: slideInRight 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        }
        .animate-fade-in {
            animation: fadeIn 0.5s ease-out forwards;
        }



        /* Code syntax highlighting */
        .code-block {
            background: #1e1e1e;
            color: #d4d4d4;
            border-radius: 8px;
            padding: 16px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .code-keyword { color: #569cd6; }
        .code-string { color: #ce9178; }
        .code-comment { color: #6a9955; font-style: italic; }
        
        /* Windows browser tab simulation */
        .windows-tab {
            background: #e5e7eb;
            border-right: 1px solid #d1d5db;
            padding: 10px 16px;
            font-size: 13px;
            color: #374151;
            cursor: pointer;
            max-width: 220px;
            min-width: 140px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            position: relative;
            border-radius: 8px 8px 0 0;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border-bottom: 2px solid transparent;
        }

        .windows-tab.active {
            background: white;
            color: #111827;
            font-weight: 500;
            border-bottom: 2px solid #0078d4;
            transform: translateY(1px);
        }

        .windows-tab:hover:not(.active) {
            background: #d1d5db;
            transform: translateY(-1px);
        }

        .windows-tab .opacity-0 {
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .windows-tab:hover .opacity-0 {
            opacity: 1;
        }

        /* Windows browser controls */
        .nav-btn:hover {
            background-color: #f3f4f6;
            transform: scale(1.05);
        }

        .window-control:hover:not(:last-child) {
            background-color: #f3f4f6;
        }

        .window-control:last-child:hover {
            background-color: #dc2626;
            color: white;
        }

        .address-bar:focus-within {
            border-color: #0078d4;
            box-shadow: 0 0 0 1px #0078d4;
        }

        /* Text-Fitted Box Reveal Animation */
        .text-reveal {
            position: relative;
            display: inline-block;
            overflow: hidden;
        }

        .text-reveal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #2563eb;
            transform: translateX(0);
            transition: transform 0.8s cubic-bezier(0.77, 0, 0.175, 1);
            z-index: 2;
        }

        .text-reveal.animate-in .text-reveal-overlay {
            transform: translateX(100%);
        }

        /* Block-level text reveals */
        .block-text-reveal {
            position: relative;
            overflow: hidden;
            display: block;
            width: fit-content;
            margin-bottom: 1rem;
        }

        .block-text-reveal .text-reveal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #2563eb;
            transform: translateX(0);
            transition: transform 0.8s cubic-bezier(0.77, 0, 0.175, 1);
            z-index: 2;
        }

        .block-text-reveal.animate-in .text-reveal-overlay {
            transform: translateX(100%);
        }

        /* Staggered delays - removed for instant start */
        .text-reveal:nth-child(1) .text-reveal-overlay,
        .block-text-reveal:nth-child(1) .text-reveal-overlay {
            transition-delay: 0s;
        }

        .text-reveal:nth-child(2) .text-reveal-overlay,
        .block-text-reveal:nth-child(2) .text-reveal-overlay {
            transition-delay: 0s;
        }

        .text-reveal:nth-child(3) .text-reveal-overlay,
        .block-text-reveal:nth-child(3) .text-reveal-overlay {
            transition-delay: 0s;
        }

        .text-reveal:nth-child(4) .text-reveal-overlay,
        .block-text-reveal:nth-child(4) .text-reveal-overlay {
            transition-delay: 0s;
        }

        /* Ensure text content is positioned correctly */
        .text-reveal-content {
            position: relative;
            z-index: 1;
            display: block;
        }

        /* Text slide-up animation after box reveal */
        .text-reveal-content.slide-up {
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.5s ease-out, transform 0.5s ease-out;
        }

        .text-reveal-content.slide-up.animate-in {
            opacity: 1;
            transform: translateY(0);
        }

        /* Search icon blur-in animation */
        .search-icon-blur {
            opacity: 0;
            filter: blur(8px);
            transform: scale(0.8);
            transition: opacity 0.6s ease-out, filter 0.6s ease-out, transform 0.6s ease-out;
        }

        .search-icon-blur.animate-in {
            opacity: 1;
            filter: blur(0px);
            transform: scale(1);
        }

        /* Custom box reveal colors for different sections */
        #building .text-reveal-overlay,
        #building .block-text-reveal .text-reveal-overlay {
            background: #16a34a; /* Green color for Building section */
        }

        #testing .text-reveal-overlay,
        #testing .block-text-reveal .text-reveal-overlay {
            background: #9333ea; /* Purple color for Testing section */
        }
        
        /* Hover effects */
        .hover-lift {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .hover-lift:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 40px rgba(54, 116, 181, 0.15);
        }
        
        /* Progress indicators */
        .progress-bar {
            height: 6px;
            background: #e5e7eb;
            border-radius: 3px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--brand-blue), #4f8fd9);
            border-radius: 3px;
            transition: width 0.8s ease;
        }
        
        /* Emotional moment styling */
        .breakthrough-moment {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border-left: 4px solid #f59e0b;
            position: relative;
            overflow: hidden;
        }
        
        .breakthrough-moment::before {
            content: '💡';
            position: absolute;
            top: 16px;
            right: 16px;
            font-size: 24px;
            opacity: 0.7;
        }
        
        /* Expandable content */
        .expandable-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.5s ease;
        }
        
        .expandable-content.expanded {
            max-height: 500px;
        }
        
        /* Visual comparison */
        .before-after {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }

        .before, .after {
            padding: 16px;
            border-radius: 8px;
            text-align: center;
        }

        .before {
            background: #fef2f2;
            border: 2px solid #fca5a5;
        }

        .after {
            background: #f0fdf4;
            border: 2px solid #86efac;
        }

        /* Mobile Navigation Styles */
        .mobile-nav {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 25px;
            padding: 8px 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            z-index: 1000;
            display: none;
        }

        .mobile-nav.visible {
            display: flex;
        }

        .nav-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #d1d5db;
            margin: 0 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-dot.active {
            background: var(--brand-blue);
            transform: scale(1.2);
        }

        .nav-dot:hover {
            background: var(--brand-blue);
            opacity: 0.7;
        }



        /* Enhanced Image Placeholder Styles */
        .image-placeholder {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border: 2px dashed #cbd5e1;
            border-radius: 16px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            padding: 2rem;
        }

        .image-placeholder:hover {
            border-color: var(--brand-blue);
            background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
            transform: translateY(-4px);
            box-shadow: 0 20px 40px rgba(54, 116, 181, 0.15);
        }

        .image-placeholder-16-9 {
            aspect-ratio: 16/9;
            min-height: 240px;
        }

        .image-placeholder-4-3 {
            aspect-ratio: 4/3;
            min-height: 220px;
        }

        .image-placeholder-icon {
            width: 64px;
            height: 64px;
            background: linear-gradient(135deg, var(--brand-blue), #4f8fd9);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 16px;
            box-shadow: 0 8px 16px rgba(54, 116, 181, 0.2);
            transition: transform 0.3s ease;
        }

        .image-placeholder:hover .image-placeholder-icon {
            transform: scale(1.05);
        }

        .image-placeholder-title {
            font-weight: 600;
            font-size: 1.125rem;
            color: #1f2937;
            margin-bottom: 8px;
            text-align: center;
        }

        .image-placeholder-caption {
            font-size: 0.875rem;
            color: #6b7280;
            font-style: italic;
            text-align: center;
            line-height: 1.5;
            max-width: 280px;
        }

        /* Enhanced section header styling */
        .section-header-enhanced {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .section-icon-enhanced {
            width: 4rem;
            height: 4rem;
            border-radius: 1.25rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 800;
            font-size: 1.5rem;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .section-icon-enhanced:hover {
            transform: scale(1.05);
        }

        .section-content-enhanced {
            flex: 1;
        }

        .section-title-enhanced {
            font-weight: 800;
            font-size: clamp(1.875rem, 4vw, 2.5rem);
            line-height: 1.1;
            letter-spacing: -0.02em;
            color: #111827;
            margin-bottom: 0.75rem;
        }

        .section-subtitle-enhanced {
            font-weight: 400;
            font-size: clamp(1.125rem, 2vw, 1.375rem);
            line-height: 1.5;
            color: #4b5563;
        }

        /* Loading state animation */
        .image-placeholder::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* Minimalistic Discovery Cards with Smooth Auto-Animation */
        .discovery-card-3d {
            position: relative;
            will-change: transform, opacity;
            animation: minimalFloat 6s ease-in-out infinite;
            pointer-events: none !important; /* Completely disable all interactions */
            transition: none !important; /* Remove all hover transitions */
            cursor: default !important; /* Remove pointer cursor */
        }

        @keyframes minimalFloat {
            0%, 16.66%, 100% {
                transform: translateY(0px);
                opacity: 1;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            }
            8.33% {
                transform: translateY(-6px);
                opacity: 0.95;
                box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
            }
        }

        /* Sequential timing for smooth wave effect */
        .discovery-card-3d:nth-child(1) {
            animation-delay: 0s;
        }
        .discovery-card-3d:nth-child(2) {
            animation-delay: 2s;
        }
        .discovery-card-3d:nth-child(3) {
            animation-delay: 4s;
        }

        /* Minimalistic background glow */
        .discovery-card-3d::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(135deg,
                rgba(59, 130, 246, 0.1) 0%,
                rgba(16, 185, 129, 0.1) 50%,
                rgba(245, 158, 11, 0.1) 100%);
            border-radius: 1rem;
            pointer-events: none;
            opacity: 0;
            z-index: -1;
            animation: minimalGlow 6s ease-in-out infinite;
        }

        @keyframes minimalGlow {
            0%, 16.66%, 100% { opacity: 0; }
            8.33% { opacity: 0.6; }
        }

        .discovery-card-3d:nth-child(1)::before {
            animation-delay: 0s;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(59, 130, 246, 0.05) 100%);
        }
        .discovery-card-3d:nth-child(2)::before {
            animation-delay: 2s;
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.15) 0%, rgba(16, 185, 129, 0.05) 100%);
        }
        .discovery-card-3d:nth-child(3)::before {
            animation-delay: 4s;
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.15) 0%, rgba(245, 158, 11, 0.05) 100%);
        }

        /* Icon smooth scale animation */
        .discovery-card-3d .w-10 {
            position: relative;
            z-index: 2;
            animation: minimalIconScale 6s ease-in-out infinite;
        }

        /* Achievement Cards In Animation - Slower, more deliberate */
        .achievement-card {
            opacity: 0;
            transform: translateY(50px) scale(0.9);
            transition: all 1.2s cubic-bezier(0.16, 1, 0.3, 1);
            will-change: transform, opacity;
        }

        .achievement-card.animate-in {
            opacity: 1;
            transform: translateY(0) scale(1);
        }

        /* Achievement Card Content Animation - Delayed and slower */
        .achievement-card-content {
            opacity: 0;
            transform: translateY(30px) scale(0.95);
            transition: all 1.0s cubic-bezier(0.16, 1, 0.3, 1);
            will-change: transform, opacity;
        }

        .achievement-card-content.animate-in {
            opacity: 1;
            transform: translateY(0) scale(1);
        }

        /* Achievement Card Items Animation - Much slower reveal */
        .achievement-item {
            opacity: 0;
            transform: translateX(-40px) scale(0.92);
            transition: all 0.8s cubic-bezier(0.16, 1, 0.3, 1);
            will-change: transform, opacity;
        }

        .achievement-item.animate-in {
            opacity: 1;
            transform: translateX(0) scale(1);
        }

        /* Enhanced hover effects for animated cards */
        .achievement-card.animate-in:hover {
            transform: translateY(-2px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
            filter: none !important;
            -webkit-filter: none !important;
        }

        /* Ensure text stays sharp during hover */
        .achievement-card.animate-in:hover *,
        .achievement-card:hover *,
        .achievement-item:hover *,
        [data-card="technical-wins"]:hover *,
        [data-card="personal-growth"]:hover *,
        [data-card="technical-wins"] .achievement-item:hover *,
        [data-card="personal-growth"] .achievement-item:hover * {
            filter: none !important;
            -webkit-filter: none !important;
            text-rendering: optimizeLegibility !important;
            -webkit-font-smoothing: antialiased !important;
            -moz-osx-font-smoothing: grayscale !important;
        }

        /* Specific text sharpening for Technical Wins and Personal Growth cards */
        [data-card="technical-wins"],
        [data-card="personal-growth"],
        [data-card="discoveries"],
        [data-card="realizations"] {
            filter: none !important;
            -webkit-filter: none !important;
            transition: box-shadow 0.3s ease, transform 0.3s ease, border-color 0.3s ease !important;
        }

        [data-card="technical-wins"] *,
        [data-card="personal-growth"] *,
        [data-card="discoveries"] *,
        [data-card="realizations"] *,
        [data-card="technical-wins"]:hover *,
        [data-card="personal-growth"]:hover *,
        [data-card="discoveries"]:hover *,
        [data-card="realizations"]:hover * {
            filter: none !important;
            -webkit-filter: none !important;
            backdrop-filter: none !important;
            -webkit-backdrop-filter: none !important;
            text-rendering: optimizeLegibility !important;
            -webkit-font-smoothing: antialiased !important;
            -moz-osx-font-smoothing: grayscale !important;
        }

        /* Ensure card hover states don't apply blur */
        [data-card="technical-wins"]:hover,
        [data-card="personal-growth"]:hover,
        [data-card="discoveries"]:hover,
        [data-card="realizations"]:hover {
            filter: none !important;
            -webkit-filter: none !important;
            backdrop-filter: none !important;
            -webkit-backdrop-filter: none !important;
        }

        /* Fix transition-all causing filter blur persistence */
        .achievement-item {
            transition: background-color 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease !important;
            filter: none !important;
            -webkit-filter: none !important;
        }

        .achievement-item * {
            transition: color 0.3s ease, background-color 0.3s ease, transform 0.3s ease !important;
            filter: none !important;
            -webkit-filter: none !important;
        }

        /* Ensure no filter transitions on achievement items */
        .achievement-item,
        .achievement-item *,
        .achievement-item:hover,
        .achievement-item:hover *,
        .achievement-item:focus,
        .achievement-item:focus *,
        .achievement-item:active,
        .achievement-item:active * {
            filter: none !important;
            -webkit-filter: none !important;
            backdrop-filter: none !important;
            -webkit-backdrop-filter: none !important;
        }

        /* Ultimate override for all achievement item states */
        .achievement-item[class*="transition"],
        .achievement-item[class*="hover"],
        .achievement-item[class*="group"] {
            filter: none !important;
            -webkit-filter: none !important;
        }

        .achievement-item[class*="transition"] *,
        .achievement-item[class*="hover"] *,
        .achievement-item[class*="group"] * {
            filter: none !important;
            -webkit-filter: none !important;
        }

        /* Fix achievement card transition-all causing blur persistence */
        .achievement-card {
            transition: box-shadow 0.3s ease, transform 0.3s ease, border-color 0.3s ease !important;
            filter: none !important;
            -webkit-filter: none !important;
        }

        .achievement-card * {
            filter: none !important;
            -webkit-filter: none !important;
            backdrop-filter: none !important;
            -webkit-backdrop-filter: none !important;
        }

        /* Comprehensive achievement card hover state protection */
        .achievement-card:hover,
        .achievement-card:focus,
        .achievement-card:active {
            filter: none !important;
            -webkit-filter: none !important;
            backdrop-filter: none !important;
            -webkit-backdrop-filter: none !important;
        }

        .achievement-card:hover *,
        .achievement-card:focus *,
        .achievement-card:active * {
            filter: none !important;
            -webkit-filter: none !important;
            backdrop-filter: none !important;
            -webkit-backdrop-filter: none !important;
        }

        /* Override transition-all on achievement cards */
        .achievement-card[class*="transition-all"],
        .achievement-card.transition-all,
        .achievement-card[class*="hover:shadow"],
        .achievement-card[class*="hover:-translate"] {
            transition: box-shadow 0.3s ease, transform 0.3s ease, border-color 0.3s ease !important;
            filter: none !important;
            -webkit-filter: none !important;
        }

        /* Ultimate card protection - catch all possible selectors */
        div[data-card]:hover,
        div[data-card]:focus,
        div[data-card]:active,
        div[data-card][class*="achievement-card"]:hover,
        div[class*="achievement-card"]:hover {
            filter: none !important;
            -webkit-filter: none !important;
            backdrop-filter: none !important;
            -webkit-backdrop-filter: none !important;
        }

        div[data-card]:hover *,
        div[data-card]:focus *,
        div[data-card]:active *,
        div[data-card][class*="achievement-card"]:hover *,
        div[class*="achievement-card"]:hover * {
            filter: none !important;
            -webkit-filter: none !important;
            backdrop-filter: none !important;
            -webkit-backdrop-filter: none !important;
        }

        /* Extra protection for Discoveries and Realizations cards specifically */
        div[data-card="discoveries"],
        div[data-card="realizations"],
        div[data-card="discoveries"]:hover,
        div[data-card="realizations"]:hover,
        div[data-card="discoveries"]:focus,
        div[data-card="realizations"]:focus {
            transition: box-shadow 0.3s ease, transform 0.3s ease, border-color 0.3s ease !important;
            filter: none !important;
            -webkit-filter: none !important;
            backdrop-filter: none !important;
            -webkit-backdrop-filter: none !important;
        }

        /* Discoveries and Realizations card content protection */
        div[data-card="discoveries"] *,
        div[data-card="realizations"] *,
        div[data-card="discoveries"]:hover *,
        div[data-card="realizations"]:hover *,
        div[data-card="discoveries"] .achievement-item,
        div[data-card="realizations"] .achievement-item,
        div[data-card="discoveries"] .achievement-item:hover,
        div[data-card="realizations"] .achievement-item:hover,
        div[data-card="discoveries"] .achievement-item *,
        div[data-card="realizations"] .achievement-item *,
        div[data-card="discoveries"] .achievement-item:hover *,
        div[data-card="realizations"] .achievement-item:hover * {
            filter: none !important;
            -webkit-filter: none !important;
            backdrop-filter: none !important;
            -webkit-backdrop-filter: none !important;
            text-rendering: optimizeLegibility !important;
            -webkit-font-smoothing: antialiased !important;
            -moz-osx-font-smoothing: grayscale !important;
        }

        /* Browser Mockup Animation Styles */
        .browser-window {
            opacity: 0;
            transform: translateY(30px) scale(0.95);
            transition: opacity 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                       transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .browser-window.animate-in {
            opacity: 1;
            transform: translateY(0) scale(1);
        }

        /* Browser Tab Staggered Animation */
        .browser-tab {
            opacity: 0;
            transform: translateY(-10px);
            transition: opacity 0.5s ease-out, transform 0.5s ease-out;
        }

        .browser-tab.animate-in {
            opacity: 1;
            transform: translateY(0);
        }

        /* Search Bar Typing Animation */
        .search-typing {
            overflow: hidden;
            border-right: 2px solid #3B82F6;
            white-space: nowrap;
            animation: typing 3s steps(40, end), blink-caret 0.75s step-end infinite;
        }

        @keyframes typing {
            from { width: 0; }
            to { width: 100%; }
        }

        @keyframes blink-caret {
            from, to { border-color: transparent; }
            50% { border-color: #3B82F6; }
        }

        /* Browser Header Animation */
        .browser-header {
            opacity: 0;
            transform: translateY(-20px);
            transition: opacity 0.6s ease-out, transform 0.6s ease-out;
        }

        .browser-header.animate-in {
            opacity: 1;
            transform: translateY(0);
        }

        /* Quote Fade-In with Delay */
        .browser-quote {
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.8s ease-out, transform 0.8s ease-out;
        }

        .browser-quote.animate-in {
            opacity: 1;
            transform: translateY(0);
        }

        /* Fire Icon Animation */
        .fire-icon {
            opacity: 0;
            transform: scale(0.5) rotate(-10deg);
            transition: opacity 0.6s ease-out, transform 0.6s ease-out;
        }

        .fire-icon.animate-in {
            opacity: 1;
            transform: scale(1) rotate(0deg);
        }

        /* Research Header Animation */
        .research-header {
            opacity: 0;
            transform: translateY(-20px) scale(0.95);
            transition: opacity 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                       transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .research-header.animate-in {
            opacity: 1;
            transform: translateY(0) scale(1);
        }

        /* Research Header Icon Animation */
        .research-icon {
            opacity: 0;
            transform: scale(0.8) rotate(-5deg);
            transition: opacity 0.6s ease-out, transform 0.6s ease-out;
        }

        .research-icon.animate-in {
            opacity: 1;
            transform: scale(1) rotate(0deg);
        }

        /* Research Header Text Animation */
        .research-text {
            opacity: 0;
            transform: translateX(-10px);
            transition: opacity 0.7s ease-out, transform 0.7s ease-out;
        }

        .research-text.animate-in {
            opacity: 1;
            transform: translateX(0);
        }

        /* Smooth icon animations within cards */
        .achievement-card .w-10 {
            transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
        }

        .achievement-card.animate-in .w-10 {
            animation: iconFloat 3s ease-in-out infinite;
        }

        @keyframes iconFloat {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-3px);
            }
        }

        /* Staggered animation delays for smoother sequence */
        .achievement-card[data-card="technical-wins"] {
            animation-delay: 0s;
        }

        .achievement-card[data-card="personal-growth"] {
            animation-delay: 0.2s;
        }

        /* Enhanced card border animations */
        .achievement-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 16px;
            background: linear-gradient(135deg, rgba(54, 116, 181, 0.1), rgba(139, 92, 246, 0.1));
            opacity: 0;
            transition: opacity 0.4s cubic-bezier(0.16, 1, 0.3, 1);
            pointer-events: none;
        }

        .achievement-card.animate-in:hover::before {
            opacity: 1;
        }

        /* Modern staggered word animation */
        .achievement-item h3,
        .achievement-item p {
            overflow: hidden;
        }

        .text-word {
            display: inline-block;
            opacity: 0;
            transform: translateY(25px) scale(0.95) rotateX(15deg);
            transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
            transform-origin: center bottom;
            perspective: 1000px;
        }

        .text-word.animate-in {
            opacity: 1;
            transform: translateY(0) scale(1) rotateX(0deg);
        }

        /* Enhanced word spacing for better visual flow */
        .text-word:not(:last-child) {
            margin-right: 0.25em;
        }

        /* Special styling for titles vs descriptions */
        .achievement-item h3 .text-word {
            font-weight: inherit;
            color: inherit;
        }

        .achievement-item p .text-word {
            color: inherit;
            font-size: inherit;
        }

        /* Icon animations - slower reveal */
        .achievement-item .w-10 {
            opacity: 0;
            transform: scale(0.8) rotate(-5deg);
            transition: all 0.7s cubic-bezier(0.16, 1, 0.3, 1);
        }

        .achievement-item.animate-in .w-10 {
            opacity: 1;
            transform: scale(1) rotate(0deg);
            transition-delay: 0.1s; /* Icon appears first, then text */
        }

        /* Card content container slower reveal */
        .achievement-card-content > div {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.8s cubic-bezier(0.16, 1, 0.3, 1);
        }

        .achievement-card-content.animate-in > div {
            opacity: 1;
            transform: translateY(0);
        }

        /* Header title with modern word animation */
        .achievement-card h2 {
            overflow: hidden;
        }

        .achievement-card h2 .text-word {
            font-weight: inherit;
            font-size: inherit;
            color: inherit;
        }

        /* Performance optimizations */
        .achievement-card,
        .achievement-item,
        .achievement-card-content {
            backface-visibility: hidden;
            perspective: 1000px;
        }

        @keyframes minimalIconScale {
            0%, 16.66%, 100% {
                transform: scale(1);
            }
            8.33% {
                transform: scale(1.03);
            }
        }

        .discovery-card-3d:nth-child(1) .w-10 { animation-delay: 0s; }
        .discovery-card-3d:nth-child(2) .w-10 { animation-delay: 2s; }
        .discovery-card-3d:nth-child(3) .w-10 { animation-delay: 4s; }

        /* Text subtle fade animation */
        .discovery-card-3d .flex-1 {
            position: relative;
            z-index: 2;
            animation: minimalTextFade 6s ease-in-out infinite;
        }

        @keyframes minimalTextFade {
            0%, 16.66%, 100% {
                opacity: 1;
                transform: translateY(0px);
            }
            8.33% {
                opacity: 0.9;
                transform: translateY(-1px);
            }
        }

        .discovery-card-3d:nth-child(1) .flex-1 { animation-delay: 0s; }
        .discovery-card-3d:nth-child(2) .flex-1 { animation-delay: 2s; }
        .discovery-card-3d:nth-child(3) .flex-1 { animation-delay: 4s; }

        /* Clean minimalistic background */
        .discovery-card-3d {
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(0, 0, 0, 0.05);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }

        /* SVG icon minimal enhancement */
        .discovery-card-3d .w-10 svg {
            animation: minimalSvgPulse 6s ease-in-out infinite;
        }

        @keyframes minimalSvgPulse {
            0%, 16.66%, 100% {
                opacity: 1;
            }
            8.33% {
                opacity: 0.8;
            }
        }

        .discovery-card-3d:nth-child(1) .w-10 svg { animation-delay: 0s; }
        .discovery-card-3d:nth-child(2) .w-10 svg { animation-delay: 2s; }
        .discovery-card-3d:nth-child(3) .w-10 svg { animation-delay: 4s; }

        /* Enhanced Hero Title with Wave Animation */
        .hero-title {
            font-size: clamp(3rem, 8vw, 6rem);
            font-weight: 900;
            line-height: 1.1;
            letter-spacing: -0.02em;
            margin-bottom: 1.5rem;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            background: linear-gradient(90deg,
                #1f2937 0%,
                #1f2937 15%,
                #3674B5 35%,
                #4f8fd9 50%,
                #3674B5 65%,
                #1f2937 85%,
                #1f2937 100%);
            background-size: 400% 100%;
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: continuousWaveTextAnimation 8s linear infinite;
        }

        @keyframes continuousWaveTextAnimation {
            0% {
                background-position: 0% 50%;
            }
            100% {
                background-position: 400% 50%;
            }
        }

        /* Modern Entry Cards Styling */
        .entry-cards-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 2rem;
            max-width: 1200px;
            margin: 0 auto 4rem auto;
            padding: 0 1rem;
        }

        .entry-card {
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid rgba(0, 0, 0, 0.08);
            border-radius: 20px;
            padding: 2.5rem;
            position: relative;
            overflow: hidden;
            transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
            opacity: 0;
            transform: translateY(60px) scale(0.9);
            will-change: transform, opacity;
        }

        .entry-card.animate-in {
            opacity: 1;
            transform: translateY(0) scale(1);
        }

        .entry-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            border-color: rgba(0, 0, 0, 0.12);
        }

        .entry-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--card-color), var(--card-color-light));
            border-radius: 20px 20px 0 0;
        }

        .entry-card[data-phase="01"] {
            --card-color: #3b82f6;
            --card-color-light: #60a5fa;
            animation-delay: 0s;
        }

        .entry-card[data-phase="02"] {
            --card-color: #10b981;
            --card-color-light: #34d399;
            animation-delay: 0.2s;
        }

        .entry-card[data-phase="03"] {
            --card-color: #8b5cf6;
            --card-color-light: #a78bfa;
            animation-delay: 0.4s;
        }

        .entry-card-header {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .entry-card-number {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--card-color), var(--card-color-light));
            color: white;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: 800;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
            opacity: 0;
            transform: scale(0.8) rotate(-10deg);
            transition: all 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);
        }

        .entry-card.animate-in .entry-card-number {
            opacity: 1;
            transform: scale(1) rotate(0deg);
            transition-delay: 0.1s;
        }

        .entry-card-icon {
            width: 50px;
            height: 50px;
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid var(--card-color);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transform: scale(0.7) translateY(10px);
            transition: all 0.7s cubic-bezier(0.16, 1, 0.3, 1);
        }

        .entry-card.animate-in .entry-card-icon {
            opacity: 1;
            transform: scale(1) translateY(0);
            transition-delay: 0.2s;
        }

        .entry-card-icon svg {
            width: 24px;
            height: 24px;
            color: var(--card-color);
        }

        .entry-card-content {
            flex: 1;
        }

        .entry-card-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 0.75rem;
            line-height: 1.2;
            opacity: 0;
            transform: translateX(-20px);
            transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
        }

        .entry-card.animate-in .entry-card-title {
            opacity: 1;
            transform: translateX(0);
            transition-delay: 0.3s;
        }

        .entry-card-description {
            font-size: 1rem;
            color: #64748b;
            line-height: 1.6;
            opacity: 0;
            transform: translateY(15px);
            transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
        }

        .entry-card.animate-in .entry-card-description {
            opacity: 1;
            transform: translateY(0);
            transition-delay: 0.4s;
        }

        /* Entry Cards Section Header */
        .entry-cards-section-header {
            text-align: center;
            margin-bottom: 3rem;
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.8s cubic-bezier(0.16, 1, 0.3, 1);
        }

        .entry-cards-section-header.animate-in {
            opacity: 1;
            transform: translateY(0);
        }

        .entry-cards-section-title {
            font-size: clamp(2rem, 4vw, 2.75rem);
            font-weight: 800;
            color: #111827;
            margin-bottom: 1rem;
            line-height: 1.1;
            letter-spacing: -0.02em;
        }

        .entry-cards-section-subtitle {
            font-size: clamp(1.125rem, 2vw, 1.25rem);
            color: #6b7280;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.5;
        }

        /* Enhanced Search Showcase Styling */
        .search-showcase-card {
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .search-showcase-card:hover {
            border-color: rgba(59, 130, 246, 0.3);
            box-shadow: 0 20px 40px rgba(59, 130, 246, 0.1);
        }

        .code-showcase {
            background: linear-gradient(145deg, #1a1a1a 0%, #2d2d2d 100%);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .code-showcase::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 50% 0%, rgba(59, 130, 246, 0.1) 0%, transparent 50%);
            pointer-events: none;
        }

        /* Breakthrough Moment Enhanced Styling */
        .breakthrough-moment-enhanced {
            background: linear-gradient(135deg,
                rgba(59, 130, 246, 0.05) 0%,
                rgba(99, 102, 241, 0.05) 50%,
                rgba(59, 130, 246, 0.05) 100%);
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .quote-container-enhanced {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        /* Achievement Cards Enhanced Styling */
        .achievement-card {
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid rgba(59, 130, 246, 0.15);
            position: relative;
            overflow: hidden;
        }

        .achievement-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg,
                rgba(59, 130, 246, 0.02) 0%,
                transparent 50%,
                rgba(99, 102, 241, 0.02) 100%);
            opacity: 0;
            transition: opacity 0.5s ease;
            pointer-events: none;
        }

        .achievement-card:hover::before {
            opacity: 1;
        }

        .achievement-card:hover {
            border-color: rgba(59, 130, 246, 0.3);
            box-shadow:
                0 25px 50px rgba(59, 130, 246, 0.15),
                0 15px 35px rgba(0, 0, 0, 0.1);
        }

        /* Achievement Items Enhanced Styling */
        .achievement-item {
            position: relative;
            overflow: hidden;
        }

        .achievement-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: linear-gradient(to bottom,
                rgba(59, 130, 246, 0.8) 0%,
                rgba(99, 102, 241, 0.8) 100%);
            transform: scaleY(0);
            transition: transform 0.3s ease;
            transform-origin: bottom;
        }

        /* Realizations card - amber/yellow theme */
        [data-card="realizations"] .achievement-item::before {
            background: linear-gradient(to bottom,
                rgba(245, 158, 11, 0.8) 0%,
                rgba(251, 191, 36, 0.8) 100%);
        }

        /* Personal Growth card - purple theme */
        [data-card="personal-growth"] .achievement-item::before {
            background: linear-gradient(to bottom,
                rgba(147, 51, 234, 0.8) 0%,
                rgba(168, 85, 247, 0.8) 100%);
        }

        /* Soft Skills card - red theme */
        [data-card="soft-skills"] .achievement-item::before {
            background: linear-gradient(to bottom,
                rgba(239, 68, 68, 0.8) 0%,
                rgba(220, 38, 38, 0.8) 100%);
        }

        /* Technical Skills card - indigo theme */
        [data-card="technical-skills"] .achievement-item::before {
            background: linear-gradient(to bottom,
                rgba(99, 102, 241, 0.8) 0%,
                rgba(79, 70, 229, 0.8) 100%);
        }

        .achievement-item:hover::before {
            transform: scaleY(1);
        }

        /* Completely disable all hover interactions and states */
        .discovery-card-3d:hover,
        .discovery-card-3d:focus,
        .discovery-card-3d:active,
        .discovery-card-3d.group:hover {
            transform: none !important;
            background: rgba(255, 255, 255, 0.8) !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
            border-color: rgba(0, 0, 0, 0.05) !important;
            pointer-events: none !important;
            cursor: default !important;
        }

        /* Disable hover on all child elements */
        .discovery-card-3d *,
        .discovery-card-3d *:hover,
        .discovery-card-3d *:focus,
        .discovery-card-3d *:active {
            pointer-events: none !important;
            cursor: default !important;
            transition: none !important;
        }

        /* Override any Tailwind hover classes */
        .discovery-card-3d.hover\:bg-gray-100:hover {
            background: rgba(255, 255, 255, 0.8) !important;
        }

        /* Fluid Motion Animations for Features */
        @keyframes fluidFloat {
            0%, 100% {
                transform: translateY(0px) scale(1);
                filter: none;
            }
            25% {
                transform: translateY(-8px) scale(1.02);
                filter: none;
            }
            50% {
                transform: translateY(-12px) scale(1.04);
                filter: none;
            }
            75% {
                transform: translateY(-6px) scale(1.01);
                filter: none;
            }
        }

        @keyframes breathingGlow {
            0%, 100% {
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
                opacity: 1;
            }
            50% {
                box-shadow: 0 8px 40px rgba(0, 0, 0, 0.15);
                opacity: 0.95;
            }
        }

        @keyframes waveUndulation {
            0% { transform: translateY(0px) translateX(0px); }
            20% { transform: translateY(-4px) translateX(2px); }
            40% { transform: translateY(-8px) translateX(-1px); }
            60% { transform: translateY(-6px) translateX(3px); }
            80% { transform: translateY(-2px) translateX(-2px); }
            100% { transform: translateY(0px) translateX(0px); }
        }

        .feature-fluid-motion {
            animation:
                fluidFloat 3s ease-in-out infinite,
                breathingGlow 2s ease-in-out infinite,
                waveUndulation 4s ease-in-out infinite;
            animation-fill-mode: both;
            transform-origin: center center;
            transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        /* Color-specific glow effects */
        .glow-blue { box-shadow: 0 0 20px rgba(59, 130, 246, 0.3), 0 4px 20px rgba(0, 0, 0, 0.1); }
        .glow-red { box-shadow: 0 0 20px rgba(239, 68, 68, 0.3), 0 4px 20px rgba(0, 0, 0, 0.1); }
        .glow-orange { box-shadow: 0 0 20px rgba(249, 115, 22, 0.3), 0 4px 20px rgba(0, 0, 0, 0.1); }
        .glow-yellow { box-shadow: 0 0 20px rgba(245, 158, 11, 0.3), 0 4px 20px rgba(0, 0, 0, 0.1); }
        .glow-purple { box-shadow: 0 0 20px rgba(147, 51, 234, 0.3), 0 4px 20px rgba(0, 0, 0, 0.1); }
        .glow-pink { box-shadow: 0 0 20px rgba(236, 72, 153, 0.3), 0 4px 20px rgba(0, 0, 0, 0.1); }

        /* Soft Landing Transition */
        .feature-soft-landing {
            transition:
                transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                box-shadow 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                opacity 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                filter 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            animation: softLanding 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        }

        @keyframes softLanding {
            0% {
                transform: translateY(-8px) scale(1.02);
                filter: none;
                opacity: 0.95;
            }
            30% {
                transform: translateY(-4px) scale(1.01);
                filter: none;
                opacity: 0.97;
            }
            60% {
                transform: translateY(-1px) scale(1.005);
                filter: none;
                opacity: 0.99;
            }
            100% {
                transform: translateY(0px) scale(1);
                filter: none;
                opacity: 1;
            }
        }

        /* Override fluid motion when landing */
        .feature-soft-landing.feature-fluid-motion {
            animation: softLanding 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        }

        /* Enhanced grid layouts */
        .asymmetric-grid {
            display: grid;
            grid-template-columns: 1fr 1.2fr;
            gap: 2rem;
            align-items: start;
        }

        .content-image-grid {
            display: grid;
            grid-template-columns: 1fr 0.8fr;
            gap: 1.5rem;
            align-items: center;
        }

        /* Responsive improvements */
        @media (max-width: 768px) {
            .mobile-nav {
                display: flex;
            }

            .before-after {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .asymmetric-grid,
            .content-image-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .code-block {
                font-size: 12px;
                padding: 12px;
                overflow-x: auto;
            }

            .max-w-6xl {
                max-width: 100%;
                padding: 16px;
            }

            .typography-hero {
                font-size: 2.5rem;
            }

            .drop-cap::first-letter {
                font-size: 3.5em;
            }
        }

        /* Challenge and Learning Item Animations */
        .challenge-item, .learning-item {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .challenge-item.animate-on-scroll, .learning-item.animate-on-scroll {
            opacity: 1;
            transform: translateY(0);
        }

        /* Staggered animation delays for smooth sequential appearance */
        .challenge-item:nth-child(1) { transition-delay: 0.1s; }
        .challenge-item:nth-child(2) { transition-delay: 0.2s; }
        .challenge-item:nth-child(3) { transition-delay: 0.3s; }

        .learning-item:nth-child(1) { transition-delay: 0.1s; }
        .learning-item:nth-child(2) { transition-delay: 0.2s; }
        .learning-item:nth-child(3) { transition-delay: 0.3s; }

        /* Hover effects for the items */
        .challenge-item:hover, .learning-item:hover {
            transform: translateY(-2px);
            transition-duration: 0.3s;
        }

        /* Scroll-Triggered Fade In by Line Animation */
        @keyframes fadeInByLine {
            0% {
                opacity: 0;
                transform: translateY(20px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Card container animation */
        .scroll-fade-container {
            opacity: 0;
            transform: translateY(30px);
            transition: opacity 0.6s ease-out, transform 0.6s ease-out;
            will-change: transform, opacity;
        }

        .scroll-fade-container.animate-container {
            opacity: 1;
            transform: translateY(0);
        }

        /* Initially hidden elements */
        .scroll-fade-element {
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.8s ease-out, transform 0.8s ease-out;
        }

        /* Triggered animation classes */
        .scroll-fade-element.animate {
            opacity: 1;
            transform: translateY(0);
        }

        .scroll-fade-element.animate.delay-1 {
            transition-delay: 0.2s;
        }

        .scroll-fade-element.animate.delay-2 {
            transition-delay: 0.4s;
        }

        .scroll-fade-element.animate.delay-3 {
            transition-delay: 0.6s;
        }

        /* Code Block Entry Animation */
        .code-block-animate {
            opacity: 0;
            transform: translateY(40px) scale(0.95);
            transition: opacity 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                       transform 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            will-change: transform, opacity;
        }

        .code-block-animate.animate {
            opacity: 1;
            transform: translateY(0) scale(1);
        }

        /* Advanced Text Animations */
        .word-slide-up {
            display: inline-block;
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.6s ease-out, transform 0.6s ease-out;
        }

        .word-slide-up.animate {
            opacity: 1;
            transform: translateY(0);
        }

        .highlight-reveal {
            opacity: 0;
            transform: scale(0.95);
            transition: opacity 0.5s ease-out, transform 0.5s ease-out;
        }

        .highlight-reveal.animate {
            opacity: 1;
            transform: scale(1);
        }

        .blur-in {
            opacity: 0;
            filter: blur(8px);
            transition: opacity 0.8s ease-out, filter 0.8s ease-out;
        }

        .blur-in.animate {
            opacity: 1;
            filter: blur(0px);
        }

        /* Text animation container */
        .text-animation-container {
            will-change: transform, opacity;
        }

        /* Quote Card Animation */
        .quote-card-animated {
            opacity: 0;
            transform: translateY(40px) scale(0.95);
            transition: opacity 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                       transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .quote-card-animated.animate {
            opacity: 1;
            transform: translateY(0) scale(1);
        }

        /* Enhanced quote card styling */
        .quote-card-animated {
            position: relative;
            overflow: hidden;
        }

        .quote-card-animated::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.8s ease-out;
        }

        .quote-card-animated.animate::before {
            left: 100%;
        }

        /* Title Animation */
        .title-animated {
            opacity: 0;
            transform: translateY(30px);
            transition: opacity 0.7s ease-out, transform 0.7s ease-out;
        }

        .title-animated.animate {
            opacity: 1;
            transform: translateY(0);
        }

        /* Progressive Reveal Icon Animation */
        .icon-progressive-reveal {
            opacity: 0;
            transform: translateY(-30px) scale(0.3);
            transition: opacity 0.8s cubic-bezier(0.34, 1.56, 0.64, 1),
                       transform 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);
        }

        .icon-progressive-reveal.animate {
            opacity: 1;
            transform: translateY(0) scale(1);
        }

        /* Icon container enhancement */
        .icon-container-enhanced {
            position: relative;
            overflow: visible;
        }

        .icon-container-enhanced::before {
            content: '';
            position: absolute;
            top: -4px;
            left: -4px;
            right: -4px;
            bottom: -4px;
            background: radial-gradient(circle, rgba(59, 130, 246, 0.15) 0%, transparent 70%);
            border-radius: inherit;
            opacity: 0;
            transition: opacity 0.6s ease-out;
        }

        .icon-container-enhanced.glow::before {
            opacity: 1;
        }

        /* Enhanced icon hover effect */
        .icon-container-enhanced:hover {
            transform: scale(1.05);
            transition: transform 0.3s ease-out;
        }

        .icon-container-enhanced:hover::before {
            opacity: 0.8;
        }

        /* Tooltip animations - initially hidden */
        .tooltip-card {
            opacity: 0;
            transform: translateX(-20px) scale(0.9);
            transition: none;
        }

        .tooltip-card.animate {
            animation: tooltipSlideIn 0.5s ease-out forwards;
        }

        .tooltip-text {
            opacity: 0;
        }

        .tooltip-text.animate {
            animation: textFadeIn 0.4s ease-out 0.5s forwards;
        }

        .tooltip-arrow {
            opacity: 0;
        }

        .tooltip-arrow.animate {
            animation: arrowSlideIn 0.3s ease-out 0.9s forwards;
        }

        /* Hide tooltip and disable progressive unlock on mobile devices */
        @media (max-width: 1023px) {
            .tooltip-card {
                display: none !important;
            }
        }

        /* Enhanced System Card Tooltip */
        .enhanced-tooltip-card {
            opacity: 0;
            transform: translateX(20px) scale(0.9);
            transition: none;
        }

        .enhanced-tooltip-card.animate {
            animation: enhancedTooltipSlideIn 0.5s ease-out forwards;
        }

        .enhanced-tooltip-text {
            opacity: 0;
        }

        .enhanced-tooltip-text.animate {
            animation: textFadeIn 0.4s ease-out 0.5s forwards;
        }

        .enhanced-tooltip-arrow {
            opacity: 0;
            transform: translateX(-10px);
        }

        .enhanced-tooltip-arrow.animate {
            animation: enhancedArrowSlideIn 0.3s ease-out 0.9s forwards;
        }

        @keyframes enhancedArrowSlideIn {
            from {
                opacity: 0;
                transform: translateX(-10px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes enhancedTooltipSlideIn {
            from {
                opacity: 0;
                transform: translateX(20px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateX(0) scale(1);
            }
        }

        /* Hide enhanced tooltip on mobile */
        @media (max-width: 1023px) {
            .enhanced-tooltip-card {
                display: none !important;
            }
        }

        /* JavaScript Code Block Tooltip */
        .js-tooltip-card {
            opacity: 0;
            transform: translateY(20px) scale(0.9);
            transition: none;
        }

        .js-tooltip-card.animate {
            animation: jsTooltipSlideIn 0.5s ease-out forwards;
        }

        .js-tooltip-text {
            opacity: 0;
        }

        .js-tooltip-text.animate {
            animation: textFadeIn 0.4s ease-out 0.5s forwards;
        }

        .js-tooltip-arrow {
            opacity: 0;
            transform: translateX(-10px);
        }

        .js-tooltip-arrow.animate {
            animation: jsArrowSlideIn 0.3s ease-out 0.9s forwards;
        }

        @keyframes jsTooltipSlideIn {
            from {
                opacity: 0;
                transform: translateY(20px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes jsArrowSlideIn {
            from {
                opacity: 0;
                transform: translateX(-10px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* Hide JS tooltip on mobile */
        @media (max-width: 1023px) {
            .js-tooltip-card {
                display: none !important;
            }
        }

        /* Tooltip slide out animation */
        .tooltip-card.fade-out {
            animation: tooltipSlideOut 1.2s ease-out forwards;
        }

        @keyframes tooltipSlideOut {
            from {
                opacity: 1;
                transform: translateX(0) scale(1);
            }
            to {
                opacity: 0;
                transform: translateX(-100px) scale(0.9);
            }
        }

        /* Blur effect for SaaS card content */
        #saas-card .space-y-4 {
            filter: blur(4px) !important;
            transition: filter 0.3s ease !important;
        }

        #saas-card .space-y-4 * {
            filter: none !important;
        }

        #saas-card:hover .space-y-4 {
            filter: blur(0px) !important;
        }

        /* Blur effect for Questions card content */
        #questions-card .space-y-4 {
            filter: blur(4px) !important;
            transition: filter 0.3s ease !important;
        }

        #questions-card .space-y-4 * {
            filter: none !important;
        }

        #questions-card:hover .space-y-4 {
            filter: blur(0px) !important;
        }

        /* Blur effect for Discoveries card content */
        #discoveries-card .space-y-3 {
            filter: blur(4px) !important;
            transition: filter 0.3s ease !important;
        }

        #discoveries-card .space-y-3 * {
            filter: none !important;
        }

        #discoveries-card:hover .space-y-3 {
            filter: blur(0px) !important;
        }

        /* Blur effect for Patterns card content */
        #patterns-card .space-y-4 {
            filter: blur(4px) !important;
            transition: filter 0.3s ease !important;
        }

        #patterns-card .space-y-4 * {
            filter: none !important;
        }

        #patterns-card:hover .space-y-4 {
            filter: blur(0px) !important;
        }

        /* Permanent individual reveal classes */
        #saas-card.revealed .space-y-4,
        #questions-card.revealed .space-y-4,
        #discoveries-card.revealed .space-y-3,
        #patterns-card.revealed .space-y-4 {
            filter: blur(0px) !important;
        }

        /* Locked cards styling - prevent interaction until patterns card is hovered */
        .card-locked {
            pointer-events: none !important;
            opacity: 0.6 !important;
            cursor: not-allowed !important;
        }

        .card-locked:hover {
            transform: none !important;
            box-shadow: none !important;
        }

        /* Disable blur effects and card locking on mobile devices */
        @media (max-width: 1023px) {
            #saas-card .space-y-4,
            #questions-card .space-y-4,
            #discoveries-card .space-y-3,
            #patterns-card .space-y-4 {
                filter: none !important;
            }

            .card-locked {
                pointer-events: auto !important;
                opacity: 1 !important;
                cursor: auto !important;
            }
        }



        @keyframes tooltipSlideIn {
            from {
                opacity: 0;
                transform: translateX(-20px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateX(0) scale(1);
            }
        }

        @keyframes textFadeIn {
            from {
                opacity: 0;
                transform: translateY(5px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes arrowSlideIn {
            from {
                opacity: 0;
                transform: translateX(-10px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* Code Evolution Journey Animations */
        .code-evolution-container {
            opacity: 0;
            transform: translateY(50px);
            transition: all 1.2s cubic-bezier(0.16, 1, 0.3, 1);
        }

        .code-evolution-container.animate {
            opacity: 1;
            transform: translateY(0);
        }

        .code-card {
            opacity: 0;
            transform: translateY(40px) scale(0.92);
            transition: all 1.0s cubic-bezier(0.16, 1, 0.3, 1);
        }

        .code-card.animate {
            opacity: 1;
            transform: translateY(0) scale(1);
        }

        .code-card-content {
            opacity: 0;
            transform: translateY(25px);
            transition: all 0.8s cubic-bezier(0.16, 1, 0.3, 1);
        }

        .code-card-content.animate {
            opacity: 1;
            transform: translateY(0);
        }

        .code-header {
            opacity: 0;
            transform: translateX(-30px);
            transition: all 0.7s cubic-bezier(0.16, 1, 0.3, 1);
        }

        .code-header.animate {
            opacity: 1;
            transform: translateX(0);
        }

        .code-block {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.8s cubic-bezier(0.16, 1, 0.3, 1);
        }

        .code-block.animate {
            opacity: 1;
            transform: translateY(0);
        }

        .code-footer {
            opacity: 0;
            transform: translateY(15px);
            transition: all 0.7s cubic-bezier(0.16, 1, 0.3, 1);
        }

        .code-footer.animate {
            opacity: 1;
            transform: translateY(0);
        }

        /* Feature Cards Animation */
        .feature-card {
            opacity: 0;
            transform: translateY(40px) scale(0.95);
            transition: all 1.0s cubic-bezier(0.16, 1, 0.3, 1);
        }

        .feature-card.animate {
            opacity: 1;
            transform: translateY(0) scale(1);
        }

        /* Struggle Tracker Card Animation */
        .struggle-card {
            opacity: 0;
            transform: translateY(40px) scale(0.95);
            transition: all 1.2s cubic-bezier(0.16, 1, 0.3, 1);
        }

        .struggle-card.animate {
            opacity: 1;
            transform: translateY(0) scale(1);
        }

        /* Statistics Cards Animations */
        @keyframes cardSlideIn {
            0% {
                opacity: 0;
                transform: translateY(30px) scale(0.95);
            }
            100% {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes iconBounceIn {
            0% {
                opacity: 0;
                transform: scale(0) rotate(-10deg);
            }
            60% {
                opacity: 1;
                transform: scale(1.1) rotate(5deg);
            }
            100% {
                opacity: 1;
                transform: scale(1) rotate(0deg);
            }
        }

        @keyframes textSlideUp {
            0% {
                opacity: 0;
                transform: translateY(15px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes numberCountUp {
            0% {
                opacity: 0;
                transform: translateY(20px) scale(0.8);
            }
            100% {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* Animation Classes */
        .stat-card-animate {
            opacity: 0;
            transform: translateY(30px) scale(0.95);
        }

        .stat-card-animate.animate-in {
            animation: cardSlideIn 0.8s cubic-bezier(0.16, 1, 0.3, 1) forwards;
        }

        .stat-card-animate.animate-in:nth-child(1) {
            animation-delay: 0ms;
        }

        .stat-card-animate.animate-in:nth-child(2) {
            animation-delay: 200ms;
        }

        .stat-card-animate.animate-in:nth-child(3) {
            animation-delay: 400ms;
        }

        .stat-icon-animate {
            opacity: 0;
            transform: scale(0) rotate(-10deg);
        }

        .stat-icon-animate.animate-in {
            animation: iconBounceIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
        }

        .stat-number-animate {
            opacity: 0;
            transform: translateY(20px) scale(0.8);
        }

        .stat-number-animate.animate-in {
            animation: numberCountUp 0.6s cubic-bezier(0.16, 1, 0.3, 1) forwards;
        }

        .stat-label-animate {
            opacity: 0;
            transform: translateY(15px);
        }

        .stat-label-animate.animate-in {
            animation: textSlideUp 0.4s cubic-bezier(0.16, 1, 0.3, 1) forwards;
        }

        /* Staggered text animations */
        .stat-card-animate.animate-in:nth-child(1) .stat-icon-animate.animate-in {
            animation-delay: 300ms;
        }

        .stat-card-animate.animate-in:nth-child(1) .stat-number-animate.animate-in {
            animation-delay: 500ms;
        }

        .stat-card-animate.animate-in:nth-child(1) .stat-label-animate.animate-in {
            animation-delay: 600ms;
        }

        .stat-card-animate.animate-in:nth-child(2) .stat-icon-animate.animate-in {
            animation-delay: 500ms;
        }

        .stat-card-animate.animate-in:nth-child(2) .stat-number-animate.animate-in {
            animation-delay: 700ms;
        }

        .stat-card-animate.animate-in:nth-child(2) .stat-label-animate.animate-in {
            animation-delay: 800ms;
        }

        .stat-card-animate.animate-in:nth-child(3) .stat-icon-animate.animate-in {
            animation-delay: 700ms;
        }

        .stat-card-animate.animate-in:nth-child(3) .stat-number-animate.animate-in {
            animation-delay: 900ms;
        }

        .stat-card-animate.animate-in:nth-child(3) .stat-label-animate.animate-in {
            animation-delay: 1000ms;
        }

        /* Enhanced Reflection Section Animations */

        @keyframes slide-left {
            0% {
                opacity: 0;
                transform: translateX(-50px);
            }
            100% {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slide-right {
            0% {
                opacity: 0;
                transform: translateX(50px);
            }
            100% {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slide-up {
            0% {
                opacity: 0;
                transform: translateY(30px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fade-in {
            0% { opacity: 0; }
            100% { opacity: 1; }
        }

        @keyframes pulse-glow {
            0%, 100% {
                box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
            }
            50% {
                box-shadow: 0 0 40px rgba(59, 130, 246, 0.6);
            }
        }

        /* Mobile Touch Optimizations */
        @media (hover: none) and (pointer: coarse) {
            /* Touch device optimizations */
            .hover\:shadow-lg:hover {
                box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            }

            .hover\:-translate-y-1:hover {
                transform: translateY(-0.25rem);
            }

            /* Ensure touch targets are at least 44px */
            button, .cursor-pointer {
                min-height: 44px;
                min-width: 44px;
            }

            /* Reduce animation intensity on mobile */
            .group-hover\:scale-110:hover {
                transform: scale(1.05);
            }
        }

        /* Mobile-specific responsive utilities */
        @media (max-width: 640px) {
            .mobile-text-center {
                text-align: center;
            }

            .mobile-hidden {
                display: none;
            }

            /* Ensure proper spacing on very small screens */
            .container {
                padding-left: 1rem;
                padding-right: 1rem;
            }
        }

        /* Strategy Cards Animation */
        @keyframes strategy-card-enter {
            0% {
                opacity: 0;
                transform: translateY(30px) scale(0.95);
            }
            100% {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes strategy-icon-enter {
            0% {
                opacity: 0;
                transform: scale(0.5) rotate(-10deg);
            }
            100% {
                opacity: 1;
                transform: scale(1) rotate(0deg);
            }
        }

        @keyframes strategy-text-enter {
            0% {
                opacity: 0;
                transform: translateY(15px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .strategy-card-animate {
            opacity: 0;
            animation: strategy-card-enter 0.6s ease-out forwards;
        }

        .strategy-card-animate:nth-child(1) {
            animation-delay: 0.1s;
        }

        .strategy-card-animate:nth-child(2) {
            animation-delay: 0.3s;
        }

        .strategy-card-animate:nth-child(3) {
            animation-delay: 0.5s;
        }

        .strategy-icon-animate {
            opacity: 0;
            animation: strategy-icon-enter 0.5s ease-out forwards;
        }

        .strategy-card-animate:nth-child(1) .strategy-icon-animate {
            animation-delay: 0.4s;
        }

        .strategy-card-animate:nth-child(2) .strategy-icon-animate {
            animation-delay: 0.6s;
        }

        .strategy-card-animate:nth-child(3) .strategy-icon-animate {
            animation-delay: 0.8s;
        }

        .strategy-text-animate {
            opacity: 0;
            animation: strategy-text-enter 0.5s ease-out forwards;
        }

        .strategy-card-animate:nth-child(1) .strategy-text-animate {
            animation-delay: 0.6s;
        }

        .strategy-card-animate:nth-child(2) .strategy-text-animate {
            animation-delay: 0.8s;
        }

        .strategy-card-animate:nth-child(3) .strategy-text-animate {
            animation-delay: 1.0s;
        }

        /* Challenges & Learnings Cards Animation */
        @keyframes challenges-card-enter {
            0% {
                opacity: 0;
                transform: translateY(30px) scale(0.95);
            }
            100% {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes challenges-icon-enter {
            0% {
                opacity: 0;
                transform: scale(0.5) rotate(-10deg);
            }
            100% {
                opacity: 1;
                transform: scale(1) rotate(0deg);
            }
        }

        @keyframes challenges-text-enter {
            0% {
                opacity: 0;
                transform: translateY(15px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .challenges-card-animate {
            opacity: 0;
            transform: translateY(30px) scale(0.95);
        }

        .challenges-card-animate.animate-in {
            animation: challenges-card-enter 0.6s ease-out forwards;
        }

        .challenges-card-animate.animate-in:nth-child(1) {
            animation-delay: 0.1s;
        }

        .challenges-card-animate.animate-in:nth-child(2) {
            animation-delay: 0.3s;
        }

        .challenges-icon-animate {
            opacity: 0;
            transform: scale(0.5) rotate(-10deg);
        }

        .challenges-icon-animate.animate-in {
            animation: challenges-icon-enter 0.5s ease-out forwards;
        }

        .challenges-card-animate.animate-in:nth-child(1) .challenges-icon-animate.animate-in {
            animation-delay: 0.4s;
        }

        .challenges-card-animate.animate-in:nth-child(2) .challenges-icon-animate.animate-in {
            animation-delay: 0.6s;
        }

        .challenges-text-animate {
            opacity: 0;
            transform: translateY(15px);
        }

        .challenges-text-animate.animate-in {
            animation: challenges-text-enter 0.5s ease-out forwards;
        }

        .challenges-card-animate.animate-in:nth-child(1) .challenges-text-animate.animate-in {
            animation-delay: 0.6s;
        }

        .challenges-card-animate.animate-in:nth-child(2) .challenges-text-animate.animate-in {
            animation-delay: 0.8s;
        }

        /* Animation Classes */

        .animate-slide-left {
            animation: slide-left 0.8s ease-out forwards;
        }

        .animate-slide-right {
            animation: slide-right 0.8s ease-out forwards;
        }

        .animate-slide-up {
            animation: slide-up 0.6s ease-out forwards;
        }

        .animate-fade-in {
            animation: fade-in 0.8s ease-out forwards;
        }

        .delay-200 {
            animation-delay: 200ms;
        }

        .delay-1000 {
            animation-delay: 1000ms;
        }

        /* Reflection Item Hover Effects */
        .reflection-item {
            position: relative;
            overflow: hidden;
        }

        .reflection-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s ease;
        }

        .reflection-item:hover::before {
            left: 100%;
        }

        /* Enhanced backdrop blur for glassmorphism effect */
        .backdrop-blur-enhanced {
            backdrop-filter: blur(20px) saturate(180%);
            -webkit-backdrop-filter: blur(20px) saturate(180%);
        }

        /* Smooth scroll behavior */
        html {
            scroll-behavior: smooth;
        }

        /* Custom scrollbar for reflection section */
        #reflection::-webkit-scrollbar {
            width: 8px;
        }

        #reflection::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }

        #reflection::-webkit-scrollbar-thumb {
            background: rgba(59, 130, 246, 0.3);
            border-radius: 4px;
        }

        #reflection::-webkit-scrollbar-thumb:hover {
            background: rgba(59, 130, 246, 0.5);
        }

        /* Intersection Observer Animation Triggers */
        .reflection-item {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.6s ease-out;
        }

        .reflection-item.animate-in {
            opacity: 1;
            transform: translateY(0);
        }

        /* Questions and Cards Scroll Animation */
        .questions-card-animate {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .questions-card-animate.animate-in {
            opacity: 1;
            transform: translateY(0);
        }

        .questions-item-animate {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .questions-item-animate.animate-in {
            opacity: 1;
            transform: translateY(0);
        }

        .challenges-learnings-card {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .challenges-learnings-card.animate-in {
            opacity: 1;
            transform: translateY(0);
        }

        .experience-level-card {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .experience-level-card.animate-in {
            opacity: 1;
            transform: translateY(0);
        }

        .reflection-item:nth-child(1) { transition-delay: 0ms; }
        .reflection-item:nth-child(2) { transition-delay: 100ms; }
        .reflection-item:nth-child(3) { transition-delay: 200ms; }
        .reflection-item:nth-child(4) { transition-delay: 300ms; }

        /* Enhanced Inspirational Quote Section Animations */
        .inspirational-quote-card {
            opacity: 0;
            transform: translateY(50px) scale(0.95);
            transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .inspirational-quote-card.animate-in {
            opacity: 1;
            transform: translateY(0) scale(1);
        }

        /* Quote Icon Animations */
        .quote-icon-container {
            opacity: 0;
            transform: translateY(30px) scale(0.8);
            transition: all 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
        }

        .quote-icon-container.animate-in {
            opacity: 1;
            transform: translateY(0) scale(1);
        }

        .quote-icon-container.animate-in .quote-glow {
            opacity: 1;
            animation: pulse-glow 2s ease-in-out infinite;
        }

        .quote-icon-svg {
            transition: transform 0.3s ease;
        }

        .quote-icon-container:hover .quote-icon-svg {
            transform: scale(1.1) rotate(5deg);
        }

        /* Typewriter Effect for Main Quote */
        .quote-text-container {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.6s ease-out;
        }

        .quote-text-container.animate-in {
            opacity: 1;
            transform: translateY(0);
        }

        .typewriter-text {
            border-right: 2px solid #3b82f6;
            white-space: nowrap;
            overflow: hidden;
            display: inline-block;
            animation: blink-cursor 1s infinite;
        }

        .typewriter-text.typing-complete {
            border-right: none;
        }

        @keyframes blink-cursor {
            0%, 50% { border-color: #3b82f6; }
            51%, 100% { border-color: transparent; }
        }

        .quote-opening, .quote-closing {
            color: #3b82f6;
            font-size: 1.2em;
            font-weight: 600;
        }

        /* Subtitle Animation */
        .quote-subtitle-container {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.6s ease-out;
        }

        .quote-subtitle-container.animate-in {
            opacity: 1;
            transform: translateY(0);
        }

        .quote-underline {
            width: 0;
            transition: width 0.8s ease-out;
        }

        .quote-subtitle-container.animate-in .quote-underline {
            width: 100%;
        }

        /* Action Hint Animation */
        .quote-action-hint {
            opacity: 0;
            transform: translateY(15px);
            transition: all 0.5s ease-out;
        }

        .quote-action-hint.animate-in {
            opacity: 1;
            transform: translateY(0);
        }

        /* Decorative Elements Animation */
        .quote-decoration-1, .quote-decoration-2 {
            opacity: 0;
            transform: scale(0);
            transition: all 1s ease-out;
        }

        .inspirational-quote-card.animate-in .quote-decoration-1 {
            opacity: 1;
            transform: scale(1);
            transition-delay: 0.5s;
        }

        .inspirational-quote-card.animate-in .quote-decoration-2 {
            opacity: 1;
            transform: scale(1);
            transition-delay: 0.7s;
        }

        /* Enhanced Pulse Glow Animation */
        @keyframes pulse-glow {
            0%, 100% {
                opacity: 0.3;
                transform: scale(1);
            }
            50% {
                opacity: 0.6;
                transform: scale(1.1);
            }
        }

        /* Hover Effects for Quote Card */
        .inspirational-quote-card:hover {
            transform: translateY(-2px) scale(1.01);
        }

        /* Responsive Typography */
        @media (max-width: 768px) {
            .quote-main-text {
                font-size: 1.5rem;
                line-height: 1.4;
            }

            .quote-subtitle-text {
                font-size: 1rem;
            }
        }
        /* ===== INTRO SEQUENCE STYLES ===== */
        .intro-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: white;
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 1;
            transition: opacity 0.8s ease-out;
        }

        .intro-overlay.fade-out {
            opacity: 0;
            pointer-events: none;
        }

        /* Smooth Slide-Up Animation */
        .intro-overlay.slide-up {
            animation: smoothSlideUp 1.0s cubic-bezier(0.4, 0.0, 0.2, 1) forwards;
            pointer-events: none;
        }

        @keyframes smoothSlideUp {
            0% {
                transform: translateY(0);
            }
            100% {
                transform: translateY(-100%);
            }
        }

        /* Hide main content initially */
        .main-content-hidden {
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.8s ease-out, visibility 0.8s ease-out;
        }

        /* Show main content after intro */
        .main-content-visible {
            opacity: 1;
            visibility: visible;
        }

        .intro-content {
            max-width: 1200px;
            width: 100%;
            padding: 2rem;
            text-align: center;
        }

        .intro-welcome {
            opacity: 0;
            transform: translateY(30px);
            animation: slideInUp 0.8s ease-out forwards;
        }

        .intro-preview {
            opacity: 0;
            transform: translateY(30px);
            animation: slideInUp 0.8s ease-out 0.3s forwards;
        }

        .intro-cards-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-top: 3rem;
            opacity: 0;
            transform: translateY(30px);
            animation: slideInUp 0.8s ease-out 0.6s forwards;
        }

        .intro-card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 1rem;
            padding: 2rem;
            text-align: left;
            transition: all 0.3s ease;
        }

        .intro-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .intro-card-header {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 1rem;
        }

        .intro-card-icon {
            width: 2.5rem;
            height: 2.5rem;
            background: #f3f4f6;
            border-radius: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .intro-card-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #111827;
            margin: 0;
        }

        .intro-card-content {
            color: #4b5563;
            line-height: 1.6;
            font-size: 0.95rem;
        }

        .intro-optional-badge {
            display: inline-block;
            background: #dbeafe;
            color: #1d4ed8;
            font-size: 0.75rem;
            font-weight: 500;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            margin-left: 0.5rem;
        }

        .intro-skip-button {
            position: absolute;
            top: 2rem;
            right: 2rem;
            background: #f9fafb;
            border: 1px solid #d1d5db;
            color: #374151;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.875rem;
        }

        .intro-skip-button:hover {
            background: #f3f4f6;
            border-color: #9ca3af;
            transform: translateY(-1px);
        }

        .intro-progress-bar {
            position: absolute;
            bottom: 2rem;
            left: 50%;
            transform: translateX(-50%);
            width: 200px;
            height: 4px;
            background: #e5e7eb;
            border-radius: 2px;
            overflow: hidden;
        }

        .intro-progress-fill {
            height: 100%;
            background: #3674B5;
            width: 0%;
            transition: width 0.1s linear;
            border-radius: 2px;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .intro-cards-container {
                grid-template-columns: 1fr;
                gap: 1.5rem;
                margin-top: 2rem;
            }

            .intro-content {
                padding: 1.5rem;
            }

            .intro-skip-button {
                top: 1rem;
                right: 1rem;
                padding: 0.5rem 1rem;
                font-size: 0.8rem;
            }

            .intro-card {
                padding: 1.5rem;
            }
        }

        /* Go Back Button Styles */
        .go-back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: white;
            border: 2px solid #e5e7eb;
            color: #374151;
            padding: 12px 20px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            gap: 8px;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            /* Initial state for entry animation */
            opacity: 0;
            transform: translateX(-100px) scale(0.8);
        }

        /* Go Back Button Entry Animation */
        .go-back-button.animate-in {
            animation: buttonSlideIn 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        }

        @keyframes buttonSlideIn {
            0% {
                opacity: 0;
                transform: translateX(-100px) scale(0.8);
            }
            60% {
                opacity: 1;
                transform: translateX(5px) scale(1.05);
            }
            100% {
                opacity: 1;
                transform: translateX(0) scale(1);
            }
        }

        /* Go Back Button Text Entry Animation */
        .go-back-button .button-text {
            opacity: 0;
            transform: translateY(10px);
            transition: opacity 0.6s ease, transform 0.6s ease;
        }

        .go-back-button .button-text.animate-in {
            opacity: 1;
            transform: translateY(0);
        }

        .go-back-button:hover {
            background: #f9fafb;
            border-color: #3674B5;
            color: #3674B5;
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(54, 116, 181, 0.15);
        }

        .go-back-button:active {
            transform: translateY(0);
            box-shadow: 0 2px 8px rgba(54, 116, 181, 0.2);
        }

        .go-back-button svg {
            transition: transform 0.3s ease;
        }

        .go-back-button:hover svg {
            transform: translateX(-2px);
        }

        /* Responsive adjustments for mobile */
        @media (max-width: 768px) {
            .go-back-button {
                top: 15px;
                left: 15px;
                padding: 10px 16px;
                font-size: 13px;
            }
        }

        /* Hide Scrollbar */
        body {
            /* Hide scrollbar for Chrome, Safari and Opera */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* Internet Explorer 10+ */
        }

        body::-webkit-scrollbar {
            /* Hide scrollbar for Chrome, Safari and Opera */
            display: none;
        }

        /* Also hide scrollbar for any other scrollable elements */
        * {
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* Internet Explorer 10+ */
        }

        *::-webkit-scrollbar {
            display: none;
        }

        /* Modern Right Side Navigation */
        .right-navigation {
            position: fixed;
            right: 30px;
            top: 50%;
            transform: translateY(-50%);
            z-index: 999;
            /* Initial state for entry animation */
            opacity: 0;
            transform: translateY(-50%) translateX(100px);
        }

        .right-navigation.animate-in {
            animation: navSlideIn 1.0s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        }

        @keyframes navSlideIn {
            0% {
                opacity: 0;
                transform: translateY(-50%) translateX(100px);
            }
            70% {
                opacity: 1;
                transform: translateY(-50%) translateX(-5px);
            }
            100% {
                opacity: 1;
                transform: translateY(-50%) translateX(0);
            }
        }

        /* Navigation Card Container */
        .nav-card {
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            padding: 20px 16px;
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            gap: 12px;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .nav-card:hover {
            background: rgba(255, 255, 255, 0.98);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }

        .nav-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(54, 116, 181, 0.2);
            border: 2px solid rgba(54, 116, 181, 0.3);
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            position: relative;
            align-self: center;
            /* Initial state for staggered animation */
            opacity: 0;
            transform: translateX(30px) scale(0.5);
        }

        .nav-dot.animate-in {
            opacity: 1;
            transform: translateX(0) scale(1);
        }

        .nav-dot:hover {
            background: rgba(54, 116, 181, 0.8);
            border-color: rgba(54, 116, 181, 1);
            transform: scale(1.3);
            box-shadow: 0 0 20px rgba(54, 116, 181, 0.4);
        }

        .nav-dot.active {
            background: #3674B5;
            border-color: #3674B5;
            transform: scale(1.2);
            box-shadow: 0 0 15px rgba(54, 116, 181, 0.3);
        }

        .nav-dot.active:hover {
            transform: scale(1.4);
            box-shadow: 0 0 25px rgba(54, 116, 181, 0.5);
        }

        /* Navigation Labels */
        .nav-dot::before {
            content: attr(data-label);
            position: absolute;
            right: 25px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .nav-dot:hover::before {
            opacity: 1;
            transform: translateY(-50%) translateX(-5px);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .right-navigation {
                right: 20px;
            }

            .nav-card {
                padding: 16px 12px;
                gap: 10px;
                border-radius: 12px;
            }

            .nav-dot {
                width: 10px;
                height: 10px;
            }

            .nav-dot::before {
                font-size: 11px;
                padding: 4px 8px;
                right: 20px;
            }
        }

        /* Hide navigation during intro */
        .right-navigation.hidden {
            display: none;
        }

        /* Modern Hero Section Redesign */
        .hero-container {
            position: relative;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            text-align: center;
            max-width: 1000px;
            padding: 0 2rem;
        }

        /* Enhanced Ultra-Minimalistic SaaS Hero Badge */
        .hero-badge {
            display: inline-flex;
            align-items: center;
            gap: 12px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(0, 0, 0, 0.06);
            border-radius: 40px;
            padding: 16px 28px;
            margin-bottom: 2.5rem;
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            letter-spacing: 0.025em;
            opacity: 0;
            transform: translateY(40px) scale(0.9) rotateX(15deg);
            animation: modernBadgeEntry 1.6s cubic-bezier(0.16, 1, 0.3, 1) 0.2s forwards;
            transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
            position: relative;
            overflow: hidden;
            cursor: default;
            min-height: 52px;
            perspective: 1000px;
            transform-style: preserve-3d;
        }

        .hero-badge::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(255, 255, 255, 0.3),
                transparent);
            animation: modernShimmer 3.5s ease-in-out infinite;
            animation-delay: 3s;
            border-radius: 40px;
        }

        /* Modern Badge Glow Effect */
        .hero-badge::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg,
                rgba(59, 130, 246, 0.1),
                rgba(16, 185, 129, 0.1),
                rgba(245, 158, 11, 0.1));
            border-radius: 42px;
            opacity: 0;
            z-index: -1;
            animation: modernGlow 4s ease-in-out infinite;
            animation-delay: 2s;
        }

        .hero-badge:hover {
            transform: translateY(-2px) scale(1.02);
            border-color: rgba(0, 0, 0, 0.08);
            background: rgba(255, 255, 255, 0.98);
        }

        .hero-badge svg {
            opacity: 0;
            transform: scale(0.6) rotate(-20deg) translateX(-10px);
            animation: modernIconEntry 1.2s cubic-bezier(0.34, 1.56, 0.64, 1) 0.8s forwards;
            transition: all 0.5s cubic-bezier(0.16, 1, 0.3, 1);
            width: 18px;
            height: 18px;
            filter: blur(2px);
        }

        .hero-badge:hover svg {
            transform: scale(1.1) rotate(5deg) translateX(0px);
            filter: blur(0px);
        }

        /* Modern Badge Text Animation */
        .hero-badge-text {
            opacity: 0;
            transform: translateX(15px) scale(0.95);
            animation: modernTextEntry 1.0s cubic-bezier(0.16, 1, 0.3, 1) 1.2s forwards;
            transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
            filter: blur(1px);
        }

        /* Fallback visibility for badge */
        .hero-badge {
            animation-fill-mode: forwards;
        }

        .hero-badge svg {
            animation-fill-mode: forwards;
        }

        .hero-badge-text {
            animation-fill-mode: forwards;
        }

        /* Modern Badge Entry Animation */
        @keyframes modernBadgeEntry {
            0% {
                opacity: 0;
                transform: translateY(40px) scale(0.9) rotateX(15deg);
                filter: blur(4px);
            }
            60% {
                opacity: 0.8;
                transform: translateY(-5px) scale(1.02) rotateX(-2deg);
                filter: blur(1px);
            }
            100% {
                opacity: 1;
                transform: translateY(0) scale(1) rotateX(0deg);
                filter: blur(0px);
            }
        }

        /* Modern Icon Entry Animation */
        @keyframes modernIconEntry {
            0% {
                opacity: 0;
                transform: scale(0.6) rotate(-20deg) translateX(-10px);
                filter: blur(2px);
            }
            50% {
                opacity: 0.7;
                transform: scale(1.1) rotate(5deg) translateX(2px);
                filter: blur(0.5px);
            }
            100% {
                opacity: 1;
                transform: scale(1) rotate(0deg) translateX(0px);
                filter: blur(0px);
            }
        }

        /* Modern Text Entry Animation */
        @keyframes modernTextEntry {
            0% {
                opacity: 0;
                transform: translateX(15px) scale(0.95);
                filter: blur(1px);
            }
            70% {
                opacity: 0.9;
                transform: translateX(-2px) scale(1.01);
                filter: blur(0.2px);
            }
            100% {
                opacity: 1;
                transform: translateX(0px) scale(1);
                filter: blur(0px);
            }
        }

        @keyframes modernSaasEntry {
            0% {
                opacity: 0;
                transform: translateY(32px) scale(0.92);
                filter: blur(4px);
            }
            50% {
                opacity: 0.7;
                transform: translateY(-4px) scale(1.02);
                filter: blur(1px);
            }
            80% {
                opacity: 0.95;
                transform: translateY(1px) scale(0.99);
                filter: blur(0px);
            }
            100% {
                opacity: 1;
                transform: translateY(0) scale(1);
                filter: blur(0px);
            }
        }

        @keyframes iconSaasEntry {
            0% {
                opacity: 0;
                transform: scale(0.7) rotate(-15deg);
            }
            60% {
                opacity: 0.8;
                transform: scale(1.15) rotate(5deg);
            }
            100% {
                opacity: 0.8;
                transform: scale(1) rotate(0deg);
            }
        }

        /* Modern Shimmer Animation */
        @keyframes modernShimmer {
            0% {
                left: -100%;
                opacity: 0;
            }
            50% {
                opacity: 1;
            }
            100% {
                left: 100%;
                opacity: 0;
            }
        }

        /* Modern Glow Animation */
        @keyframes modernGlow {
            0%, 100% {
                opacity: 0;
                transform: scale(1);
            }
            50% {
                opacity: 0.3;
                transform: scale(1.02);
            }
        }

        @keyframes elegantShimmer {
            0% {
                left: -100%;
                opacity: 0;
            }
            50% {
                left: 50%;
                opacity: 1;
            }
            100% {
                left: 100%;
                opacity: 0;
            }
        }

        /* SaaS Hero Title with Character Animation */
        .hero-title {
            font-size: clamp(2.5rem, 6vw, 4.5rem);
            font-weight: 700;
            line-height: 1.2;
            letter-spacing: -0.01em;
            margin-bottom: 1.5rem;
            color: #0f172a;
            opacity: 1;
            overflow: hidden;
        }

        .hero-title-char {
            display: inline-block;
            opacity: 0;
            transform: translateY(40px) rotateX(90deg);
            animation: charReveal 0.8s cubic-bezier(0.16, 1, 0.3, 1) forwards;
            transform-origin: center bottom;
            perspective: 1000px;
        }

        .hero-title-char.space {
            width: 0.3em;
        }

        @keyframes charReveal {
            0% {
                opacity: 0;
                transform: translateY(40px) rotateX(90deg);
            }
            50% {
                opacity: 0.7;
                transform: translateY(-5px) rotateX(0deg);
            }
            100% {
                opacity: 1;
                transform: translateY(0) rotateX(0deg);
            }
        }

        .hero-subtitle {
            font-size: clamp(1.25rem, 3vw, 1.5rem);
            font-weight: 400;
            color: #64748b;
            margin-bottom: 3rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            opacity: 0;
            transform: translateY(30px);
            animation: heroFadeInUp 0.8s ease-out 0.6s forwards;
        }

        .hero-phase-cards {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 1.5rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .phase-card {
            background: transparent;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
            position: relative;
            opacity: 0;
            transform: translateY(50px) scale(0.9);
            animation: phaseCardSlideIn 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        }

        /* Staggered animation delays for each card */
        .phase-card:nth-child(1) {
            animation-delay: 0.8s;
        }

        .phase-card:nth-child(2) {
            animation-delay: 1.0s;
        }

        .phase-card:nth-child(3) {
            animation-delay: 1.2s;
        }

        .phase-number {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            background: #3674B5;
            color: white;
            border: 1px solid #3674B5;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 1rem;
            opacity: 0;
            transform: scale(0.5) rotate(-10deg);
            animation: phaseNumberSlideIn 0.6s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
        }

        .phase-title {
            font-size: 1.125rem;
            font-weight: 500;
            color: #1f2937;
            margin-bottom: 0.5rem;
            opacity: 0;
            transform: translateY(20px);
            animation: phaseTitleSlideIn 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        }

        .phase-description {
            font-size: 0.875rem;
            color: #6b7280;
            line-height: 1.5;
            opacity: 0;
            transform: translateY(15px);
            animation: phaseDescriptionSlideIn 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        }

        /* Staggered content animations for Card 1 */
        .phase-card:nth-child(1) .phase-number {
            animation-delay: 1.2s; /* 0.4s after card 1 appears */
        }
        .phase-card:nth-child(1) .phase-title {
            animation-delay: 1.4s; /* 0.2s after number */
        }
        .phase-card:nth-child(1) .phase-description {
            animation-delay: 1.6s; /* 0.2s after title */
        }

        /* Staggered content animations for Card 2 */
        .phase-card:nth-child(2) .phase-number {
            animation-delay: 1.4s; /* 0.4s after card 2 appears */
        }
        .phase-card:nth-child(2) .phase-title {
            animation-delay: 1.6s; /* 0.2s after number */
        }
        .phase-card:nth-child(2) .phase-description {
            animation-delay: 1.8s; /* 0.2s after title */
        }

        /* Staggered content animations for Card 3 */
        .phase-card:nth-child(3) .phase-number {
            animation-delay: 1.6s; /* 0.4s after card 3 appears */
        }
        .phase-card:nth-child(3) .phase-title {
            animation-delay: 1.8s; /* 0.2s after number */
        }
        .phase-card:nth-child(3) .phase-description {
            animation-delay: 2.0s; /* 0.2s after title */
        }



        /* Animations */
        @keyframes heroFadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes heroCardIn {
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* Modern smooth phase card animation */
        @keyframes phaseCardSlideIn {
            0% {
                opacity: 0;
                transform: translateY(50px) scale(0.9);
            }
            60% {
                opacity: 0.8;
                transform: translateY(-5px) scale(1.02);
            }
            100% {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* Phase number (icon) animation with bounce */
        @keyframes phaseNumberSlideIn {
            0% {
                opacity: 0;
                transform: scale(0.5) rotate(-10deg);
            }
            60% {
                opacity: 1;
                transform: scale(1.1) rotate(2deg);
            }
            100% {
                opacity: 1;
                transform: scale(1) rotate(0deg);
            }
        }

        /* Phase title animation */
        @keyframes phaseTitleSlideIn {
            0% {
                opacity: 0;
                transform: translateY(20px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Phase description animation */
        @keyframes phaseDescriptionSlideIn {
            0% {
                opacity: 0;
                transform: translateY(15px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }



        @keyframes progressShimmer {
            0% {
                transform: translateX(-100%);
            }
            100% {
                transform: translateX(100%);
            }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .hero-container {
                min-height: 90vh;
                padding: 2rem 0;
            }

            .hero-content {
                padding: 0 1rem;
            }

            .hero-progress-card {
                padding: 1.5rem;
                margin: 0 1rem;
            }


        }

        /* Enhanced Strategy Cards Intersection Observer Animations */
        .strategy-card-animate {
            opacity: 0;
            transform: translateY(80px) scale(0.9);
            transition: all 1.2s cubic-bezier(0.16, 1, 0.3, 1);
            will-change: transform, opacity;
        }

        .strategy-card-animate.animate-in {
            opacity: 1;
            transform: translateY(0) scale(1);
        }

        /* Icon container with enhanced animations */
        .strategy-icon-container {
            opacity: 0;
            transform: translateY(30px) scale(0.3);
            transition: all 1.0s cubic-bezier(0.34, 1.56, 0.64, 1);
            will-change: transform, opacity;
        }

        .strategy-icon-container.animate-in {
            opacity: 1;
            transform: translateY(0) scale(1);
        }

        .strategy-icon-animate {
            transform: rotate(-180deg) scale(0.5);
            transition: all 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);
            will-change: transform;
        }

        .strategy-icon-animate.animate-in {
            transform: rotate(0deg) scale(1);
        }

        /* Text animations with staggered timing */
        .strategy-title-animate {
            opacity: 0;
            transform: translateY(25px);
            transition: all 0.8s cubic-bezier(0.16, 1, 0.3, 1);
            will-change: transform, opacity;
        }

        .strategy-title-animate.animate-in {
            opacity: 1;
            transform: translateY(0);
        }

        .strategy-description-animate {
            opacity: 0;
            transform: translateY(30px);
            transition: all 1.0s cubic-bezier(0.16, 1, 0.3, 1);
            will-change: transform, opacity;
        }

        .strategy-description-animate.animate-in {
            opacity: 1;
            transform: translateY(0);
        }

        /* Enhanced hover effects with smooth transitions */
        .strategy-card-animate.animate-in:hover {
            transform: translateY(-12px) scale(1.03);
            box-shadow: 0 25px 50px rgba(54, 116, 181, 0.2);
            transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
        }

        .strategy-card-animate.animate-in:hover .strategy-icon-animate {
            transform: rotate(8deg) scale(1.15);
            transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
        }

        .strategy-card-animate.animate-in:hover .strategy-icon-container {
            transform: translateY(-3px) scale(1.05);
        }

        /* Subtle glow effect on hover */
        .strategy-card-animate.animate-in:hover::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(54, 116, 181, 0.05) 0%, rgba(96, 165, 250, 0.05) 100%);
            border-radius: inherit;
            opacity: 0;
            animation: glowPulse 0.6s ease-out forwards;
        }

        @keyframes glowPulse {
            0% {
                opacity: 0;
                transform: scale(0.95);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

    </style>
</head>
<body class="bg-gray-50 font-inter" style="text-rendering: optimizeLegibility; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1, 'pnum' 1, 'tnum' 0, 'onum' 1, 'lnum' 0, 'dlig' 0;">

    <!-- Go Back Button -->
    <button id="goBackButton" class="go-back-button" onclick="goBack()">
        <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
        </svg>
        <span class="button-text">Go Back</span>
    </button>

    <!-- Right Side Navigation -->
    <nav id="rightNavigation" class="right-navigation hidden">
        <div class="nav-card">
            <div class="nav-dot" data-section="hero" data-label="Introduction" onclick="scrollToSection('hero')"></div>
            <div class="nav-dot" data-section="research" data-label="Research" onclick="scrollToSection('research')"></div>
            <div class="nav-dot" data-section="building" data-label="Building" onclick="scrollToSection('building')"></div>
            <div class="nav-dot" data-section="testing" data-label="Testing" onclick="scrollToSection('testing')"></div>
            <div class="nav-dot" data-section="reflection" data-label="Reflection" onclick="scrollToSection('reflection')"></div>
        </div>
    </nav>

    <!-- Intro Sequence Overlay -->
    <div id="introOverlay" class="intro-overlay">
        <!-- Skip Button -->
        <button id="skipIntro" class="intro-skip-button">
            Skip Intro
        </button>

        <!-- Progress Bar -->
        <div class="intro-progress-bar">
            <div id="introProgressFill" class="intro-progress-fill"></div>
        </div>

        <!-- Main Content -->
        <div class="intro-content">
            <!-- Welcome Section -->
            <div class="intro-welcome">
                <h1 style="font-size: 2.5rem; font-weight: 700; color: #111827; margin-bottom: 1rem; line-height: 1.2;">
                    Welcome to My Coding Journey
                </h1>
                <p style="font-size: 1.125rem; color: #6b7280; margin-bottom: 0;">
                    An authentic story of learning, building, and growing as a developer
                </p>
            </div>

            <!-- Content Preview -->
            <div class="intro-preview">
                <p style="font-size: 1rem; color: #4b5563; line-height: 1.6; max-width: 600px; margin: 0 auto;">
                    You're about to explore my development process, from research and planning to building and testing.
                    This journey shows the real challenges, breakthroughs, and lessons learned along the way.
                </p>
            </div>

            <!-- Information Cards -->
            <div class="intro-cards-container">
                <!-- PC Compatibility Warning Card -->
                <div class="intro-card">
                    <div class="intro-card-header">
                        <div class="intro-card-icon">
                            <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="color: #6b7280;">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                            </svg>
                        </div>
                        <h3 class="intro-card-title">PC Recommended</h3>
                    </div>
                    <div class="intro-card-content">
                        This website is optimized for PC viewing and is currently not mobile-compatible.
                        On mobile devices, text, elements, and cards may appear out of place or not display correctly.
                        For the best experience, please view this journey on a desktop or laptop computer.
                    </div>
                </div>

                <!-- Experience Enhancement Card -->
                <div class="intro-card">
                    <div class="intro-card-header">
                        <div class="intro-card-icon">
                            <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="color: #6b7280;">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"/>
                            </svg>
                        </div>
                        <h3 class="intro-card-title">
                            Best Experience
                            <span class="intro-optional-badge">Optional</span>
                        </h3>
                    </div>
                    <div class="intro-card-content">
                        For an immersive, distraction-free experience, consider pressing <strong>F11</strong> to enter fullscreen mode
                        when using a PC. This will hide browser elements and give you the optimal viewing experience for this interactive journey.
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!-- Mobile Navigation -->
    <nav class="mobile-nav main-content-hidden" id="mobileNav">
        <div class="nav-dot" data-section="hero" title="Introduction"></div>
        <div class="nav-dot" data-section="research" title="Research"></div>
        <div class="nav-dot" data-section="building" title="Building"></div>
        <div class="nav-dot" data-section="testing" title="Testing"></div>
        <div class="nav-dot" data-section="reflection" title="Reflection"></div>
    </nav>

    <div class="max-w-6xl mx-auto p-4 sm:p-6 lg:p-8 main-content-hidden" id="mainContainer">
        <!-- Modern Hero Section -->
        <section id="hero" class="hero-container">


            <div class="hero-content">
                <!-- Hero Badge -->
                <div class="hero-badge">
                    <svg width="18" height="18" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M8 4L16 12L8 20" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
                        <path d="M3 12H13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
                    </svg>
                    <span class="hero-badge-text">Coding Journey</span>
                </div>

                <!-- Hero Title -->
                <h1 class="hero-title">My Journey On Coding</h1>

                <!-- Hero Subtitle -->
                <p class="hero-subtitle">From 47 browser tabs to a working e-commerce platform</p>

                <!-- Phase Cards -->
                <div class="hero-phase-cards">
                    <div class="phase-card">
                        <div class="phase-number">01</div>
                        <h3 class="phase-title">Discovery & Research</h3>
                        <p class="phase-description">Exploring the fundamentals and understanding the coding landscape</p>
                    </div>
                    <div class="phase-card">
                        <div class="phase-number">02</div>
                        <h3 class="phase-title">Building & Development</h3>
                        <p class="phase-description">Creating projects and applying learned concepts in practice</p>
                    </div>
                    <div class="phase-card">
                        <div class="phase-number">03</div>
                        <h3 class="phase-title">Testing & Refinement</h3>
                        <p class="phase-description">Debugging, optimizing, and perfecting the final solutions</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Research Section -->
        <section id="research" class="bg-white rounded-2xl p-4 sm:p-6 lg:p-8 mb-8 sm:mb-12 shadow-lg hover-lift animate-slide-in">
            <!-- Text-Fitted Box Reveal Section Header -->
            <div class="mb-12">
                <!-- Phase indicator with individual reveal -->
                <div class="flex items-center gap-3 mb-4">
                    <div class="search-icon-blur w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                    </div>
                    <div class="text-reveal">
                        <span class="text-reveal-content slide-up text-sm font-medium text-blue-600 uppercase tracking-wide">Phase 01</span>
                        <div class="text-reveal-overlay"></div>
                    </div>
                </div>

                <!-- Title with individual reveal -->
                <div class="block-text-reveal mb-3">
                    <h2 class="text-reveal-content slide-up text-2xl sm:text-3xl font-bold text-gray-900">Discovery & Research</h2>
                    <div class="text-reveal-overlay"></div>
                </div>

                <!-- Description with individual reveal -->
                <div class="block-text-reveal">
                    <p class="text-reveal-content slide-up text-lg sm:text-xl font-medium text-gray-700 leading-relaxed max-w-3xl">I had no idea how to create effective landing pages, so I studied the masters - e-commerce sites that have perfected the art of making people take action. If Amazon can make people buy products, I could learn their conversion principles for making people try Sentio.</p>
                    <div class="text-reveal-overlay"></div>
                </div>
            </div>

            <!-- Research Learning Showcase -->
            <div class="bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 rounded-2xl p-4 sm:p-6 lg:p-8 mb-6 sm:mb-8 breakthrough-moment-enhanced">
                <div class="text-center mb-8">
                    <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg icon-progressive-reveal icon-container-enhanced" data-icon-animation="research-goal">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                        </svg>
                    </div>
                    <h3 class="text-2xl sm:text-3xl font-bold text-gray-900 mb-2 title-animated" data-title-animation="research-goal">What I Learned from Studying Websites</h3>
                </div>

                <div class="bg-white border border-blue-200 rounded-xl p-6 relative overflow-hidden quote-container-enhanced quote-card-animated" data-card-animation="research-goal">
                    <div class="absolute top-0 left-0 w-2 h-full bg-gradient-to-b from-blue-500 to-blue-600"></div>
                    <div class="flex items-start gap-4">
                        <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center flex-shrink-0">
                            <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z"/>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <div class="text-lg text-gray-800 leading-relaxed mb-4 text-animation-container" data-text-animation="research-goal">
                                <span class="word-slide-up">I</span> <span class="word-slide-up">wanted</span> <span class="word-slide-up">to</span> <span class="word-slide-up">understand</span>
                                <span class="font-semibold text-blue-600 bg-blue-50 px-2 py-1 rounded highlight-reveal">how users navigate</span><span class="word-slide-up">,</span>
                                <span class="font-semibold text-blue-600 bg-blue-50 px-2 py-1 rounded highlight-reveal">what catches their attention</span><span class="word-slide-up">,</span> <span class="word-slide-up">and</span>
                                <span class="font-semibold text-blue-600 bg-blue-50 px-2 py-1 rounded highlight-reveal">what makes them buy</span>
                                <span class="word-slide-up">from</span> <span class="word-slide-up">successful</span> <span class="word-slide-up">online</span> <span class="word-slide-up">stores.</span>
                            </div>
                            <div class="text-sm font-medium text-blue-600 uppercase tracking-wider blur-in">MY RESEARCH GOAL</div>
                        </div>
                    </div>
                </div>

                <!-- Research Strategy Explanation -->
                <div class="mt-8 mb-8">
                    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 sm:gap-6">
                        <!-- The Strategy Card -->
                        <div class="strategy-card-animate bg-white border border-gray-200 rounded-2xl p-4 sm:p-6 relative overflow-hidden">
                            <div class="flex items-center justify-center gap-3 mb-4">
                                <div class="strategy-icon-container w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
                                    <svg class="strategy-icon-animate w-5 h-5 text-brand" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                                    </svg>
                                </div>
                                <h4 class="strategy-title-animate text-lg font-semibold text-brand">The Strategy</h4>
                            </div>
                            <p class="strategy-description-animate text-sm text-gray-700 leading-relaxed text-center">
                                E-commerce sites are conversion optimization masters. They've spent billions perfecting the psychology of making people take action.
                            </p>
                        </div>

                        <!-- The Logic Card -->
                        <div class="strategy-card-animate bg-white border border-gray-200 rounded-2xl p-4 sm:p-6 relative overflow-hidden">
                            <div class="flex items-center justify-center gap-3 mb-4">
                                <div class="strategy-icon-container w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
                                    <svg class="strategy-icon-animate w-5 h-5 text-brand" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                </div>
                                <h4 class="strategy-title-animate text-lg font-semibold text-brand">The Logic</h4>
                            </div>
                            <p class="strategy-description-animate text-sm text-gray-700 leading-relaxed text-center">
                                If Amazon can make people buy products they've never touched, I could learn their principles for making people try software they've never used.
                            </p>
                        </div>

                        <!-- Key Insight Card -->
                        <div class="strategy-card-animate bg-white border border-gray-200 rounded-2xl p-4 sm:p-6 relative overflow-hidden">
                            <div class="flex items-center justify-center gap-3 mb-4">
                                <div class="strategy-icon-container w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
                                    <svg class="strategy-icon-animate w-5 h-5 text-brand" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                                    </svg>
                                </div>
                                <h4 class="strategy-title-animate text-lg font-semibold text-brand">Key Insight</h4>
                            </div>
                            <p class="strategy-description-animate text-sm text-gray-700 leading-relaxed text-center">
                                Landing page psychology is universal - whether you're selling shoes or SaaS, the principles of trust, clarity, and conversion remain the same.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Browser Tabs Simulation - The 47 Tabs Moment -->
                <div class="mt-8 mb-8">
                    <!-- Section Header with Icon -->
                    <div class="flex justify-center mb-6">
                        <div class="research-header flex items-center gap-3 px-4 py-3 bg-gray-50 rounded-lg border border-gray-200" data-animation="research-header">
                            <div class="research-icon w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                                </svg>
                            </div>
                            <h4 class="research-text text-lg font-semibold text-gray-900">Research Session: Platform Analysis</h4>
                        </div>
                    </div>

                    <!-- Enhanced Browser Window Mockup -->
                    <div class="browser-window bg-white border border-gray-300 overflow-hidden" style="border-radius: 12px;" data-animation="browser">
                        <!-- Modern Browser Header -->
                        <div class="browser-header bg-gradient-to-r from-gray-50 to-gray-100 px-3 py-2 border-b border-gray-200">
                            <div class="flex items-center gap-2">
                                <!-- Navigation buttons -->
                                <div class="flex items-center gap-1">
                                    <button class="nav-btn w-8 h-8 rounded hover:bg-gray-100 flex items-center justify-center transition-colors duration-200">
                                        <svg class="w-4 h-4 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                                        </svg>
                                    </button>
                                    <button class="nav-btn w-8 h-8 rounded hover:bg-gray-100 flex items-center justify-center transition-colors duration-200 opacity-50">
                                        <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                        </svg>
                                    </button>
                                    <button class="nav-btn w-8 h-8 rounded hover:bg-gray-100 flex items-center justify-center transition-colors duration-200">
                                        <svg class="w-4 h-4 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                                        </svg>
                                    </button>
                                </div>

                                <!-- Interactive Address bar -->
                                <div class="flex-1 mx-3">
                                    <div class="address-bar bg-white rounded-lg px-4 py-2 text-sm text-gray-700 border border-gray-200 flex items-center gap-3 hover:border-blue-300 hover:shadow-sm transition-all duration-200">
                                        <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                                        </svg>
                                        <input
                                            type="text"
                                            id="searchInput"
                                            class="flex-1 font-medium outline-none bg-transparent transition-all duration-500"
                                            value=""
                                            data-typing-text="best e-commerce landing page design examples"
                                        />
                                        <div id="searchStatus" class="flex items-center gap-2 opacity-0 transition-opacity duration-300">
                                            <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                                            <span class="text-xs text-blue-600 font-medium">Searching...</span>
                                        </div>
                                        <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"/>
                                        </svg>
                                    </div>
                                </div>

                                <!-- Window controls -->
                                <div class="flex items-center ml-auto">
                                    <button class="window-control w-11 h-8 hover:bg-gray-100 flex items-center justify-center transition-colors duration-200">
                                        <svg class="w-3 h-3 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"/>
                                        </svg>
                                    </button>
                                    <button class="window-control w-11 h-8 hover:bg-gray-100 flex items-center justify-center transition-colors duration-200">
                                        <svg class="w-3 h-3 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"/>
                                        </svg>
                                    </button>
                                    <button class="window-control w-11 h-8 hover:bg-red-500 hover:text-white flex items-center justify-center transition-all duration-200">
                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Bookmarks Bar -->
                        <div class="bg-gray-50 px-3 py-1.5 border-b border-gray-200 flex items-center gap-3 text-xs">
                            <div class="flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-200 transition-colors cursor-pointer">
                                <div class="w-3 h-3 bg-blue-500 rounded-sm flex items-center justify-center">
                                    <span class="text-white text-xs font-bold">G</span>
                                </div>
                                <span class="text-gray-700">GitHub</span>
                            </div>
                            <div class="flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-200 transition-colors cursor-pointer">
                                <div class="w-3 h-3 bg-orange-500 rounded-sm flex items-center justify-center">
                                    <span class="text-white text-xs font-bold">S</span>
                                </div>
                                <span class="text-gray-700">Stack Overflow</span>
                            </div>
                            <div class="flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-200 transition-colors cursor-pointer">
                                <div class="w-3 h-3 bg-red-500 rounded-sm flex items-center justify-center">
                                    <span class="text-white text-xs font-bold">Y</span>
                                </div>
                                <span class="text-gray-700">YouTube</span>
                            </div>
                            <div class="flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-200 transition-colors cursor-pointer">
                                <div class="w-3 h-3 bg-purple-500 rounded-sm flex items-center justify-center">
                                    <span class="text-white text-xs font-bold">C</span>
                                </div>
                                <span class="text-gray-700">Codepen</span>
                            </div>
                            <div class="text-gray-400 ml-auto">📚 Learning Resources</div>
                        </div>

                        <!-- Enhanced Tab Bar -->
                        <div class="bg-gray-100 px-0 py-0 overflow-x-auto">
                            <div class="flex gap-0 min-w-max border-b border-gray-300">
                                <!-- Active Tab -->
                                <div class="browser-tab windows-tab active flex items-center gap-2 min-w-0 bg-white border-t-2 border-orange-500" data-tab-delay="0">
                                    <div class="w-4 h-4 bg-orange-500 rounded-sm flex items-center justify-center flex-shrink-0">
                                        <span class="text-white text-xs font-bold">A</span>
                                    </div>
                                    <span class="truncate text-sm font-medium">Amazon - Shopping</span>
                                    <button class="w-4 h-4 rounded-full hover:bg-gray-300 flex items-center justify-center ml-1 opacity-70 hover:opacity-100 transition-opacity">
                                        <svg class="w-3 h-3 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                        </svg>
                                    </button>
                                </div>

                                <!-- Other Tabs -->
                                <div class="browser-tab windows-tab flex items-center gap-2 min-w-0 hover:bg-gray-50 transition-colors" data-tab-delay="200">
                                    <div class="w-4 h-4 bg-green-500 rounded-sm flex items-center justify-center flex-shrink-0">
                                        <span class="text-white text-xs font-bold">S</span>
                                    </div>
                                    <span class="truncate text-sm">Shopify - Online Stores</span>
                                    <button class="w-4 h-4 rounded-full hover:bg-gray-300 flex items-center justify-center ml-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                        <svg class="w-3 h-3 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                        </svg>
                                    </button>
                                </div>

                                <div class="browser-tab windows-tab flex items-center gap-2 min-w-0 hover:bg-gray-50 transition-colors" data-tab-delay="400">
                                    <div class="w-4 h-4 bg-black rounded-sm flex items-center justify-center flex-shrink-0">
                                        <span class="text-white text-xs font-bold">✓</span>
                                    </div>
                                    <span class="truncate text-sm">Nike.com</span>
                                    <button class="w-4 h-4 rounded-full hover:bg-gray-300 flex items-center justify-center ml-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                        <svg class="w-3 h-3 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                        </svg>
                                    </button>
                                </div>

                                <div class="browser-tab windows-tab flex items-center gap-2 min-w-0 hover:bg-gray-50 transition-colors" data-tab-delay="600">
                                    <div class="w-4 h-4 bg-red-500 rounded-sm flex items-center justify-center flex-shrink-0">
                                        <span class="text-white text-xs font-bold">▶</span>
                                    </div>
                                    <span class="truncate text-sm">YouTube - CSS Tutorials</span>
                                    <button class="w-4 h-4 rounded-full hover:bg-gray-300 flex items-center justify-center ml-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                        <svg class="w-3 h-3 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                        </svg>
                                    </button>
                                </div>

                                <div class="browser-tab windows-tab flex items-center gap-2 min-w-0 hover:bg-gray-50 transition-colors" data-tab-delay="800">
                                    <div class="w-4 h-4 bg-purple-500 rounded-sm flex items-center justify-center flex-shrink-0">
                                        <span class="text-white text-xs font-bold">#</span>
                                    </div>
                                    <span class="truncate text-sm">CSS Grid Tutorial</span>
                                    <button class="w-4 h-4 rounded-full hover:bg-gray-300 flex items-center justify-center ml-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                        <svg class="w-3 h-3 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                        </svg>
                                    </button>
                                </div>

                                <div class="windows-tab flex items-center gap-2 min-w-0 hover:bg-gray-50 transition-colors">
                                    <div class="w-4 h-4 bg-yellow-500 rounded-sm flex items-center justify-center flex-shrink-0">
                                        <span class="text-white text-xs font-bold">JS</span>
                                    </div>
                                    <span class="truncate text-sm">JavaScript Cart Tutorial</span>
                                    <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse ml-1"></div>
                                    <button class="w-4 h-4 rounded-full hover:bg-gray-300 flex items-center justify-center ml-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                        <svg class="w-3 h-3 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                        </svg>
                                    </button>
                                </div>

                                <div class="windows-tab flex items-center gap-2 min-w-0 hover:bg-gray-50 transition-colors">
                                    <div class="w-4 h-4 bg-cyan-500 rounded-sm flex items-center justify-center flex-shrink-0">
                                        <span class="text-white text-xs font-bold">T</span>
                                    </div>
                                    <span class="truncate text-sm">Tailwind CSS Docs</span>
                                    <button class="w-4 h-4 rounded-full hover:bg-gray-300 flex items-center justify-center ml-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                        <svg class="w-3 h-3 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                        </svg>
                                    </button>
                                </div>

                                <!-- Overflow indicator with notification -->
                                <div class="windows-tab flex items-center gap-2 min-w-0 bg-gray-100 border-l border-gray-300 hover:bg-gray-200 transition-colors cursor-pointer">
                                    <svg class="w-4 h-4 text-gray-500 flex-shrink-0" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M6 10c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm12 0c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm-6 0c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
                                    </svg>
                                    <span class="truncate font-medium text-sm text-gray-600">+39 more</span>
                                    <div class="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Enhanced Browser Content Area -->
                        <div class="p-8 bg-gradient-to-br from-white to-gray-50">
                            <div class="text-center">
                                <div class="fire-icon text-4xl mb-4">🔥</div>
                                <div class="browser-quote text-sm text-gray-600 italic font-medium">
                                    "My laptop was getting hot from all these tabs open at the same time..."
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Modern Learning Insights Cards -->
                <div class="mt-6 sm:mt-8 grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                    <!-- Discoveries Card -->
                    <div class="achievement-card bg-white border border-gray-200 rounded-2xl p-6 hover:shadow-lg transition-all duration-300 hover:-translate-y-1" data-card="discoveries">
                        <div class="achievement-card-content flex items-center justify-center gap-3 mb-6" data-content="discoveries-header">
                            <div class="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
                                <svg class="w-5 h-5 text-brand" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                                </svg>
                            </div>
                            <h4 class="text-lg font-semibold text-gray-900">Discoveries</h4>
                        </div>
                        <div class="achievement-card-content space-y-4" data-content="discoveries-items">
                            <div class="achievement-item flex items-start gap-3 p-3 rounded-lg hover:bg-blue-50 transition-all duration-300 cursor-pointer group">
                                <div class="w-8 h-8 bg-blue-50 rounded-lg flex items-center justify-center mt-0.5 group-hover:bg-blue-200 group-hover:scale-110 transition-all duration-300 border border-blue-200 flex-shrink-0">
                                    <svg class="w-4 h-4 text-brand" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-sm font-medium text-gray-900 mb-1 group-hover:text-blue-600 transition-colors duration-300">Amazon's clean layout</h3>
                                    <p class="text-xs text-gray-600 leading-relaxed">Applied to Sentio's feature sections</p>
                                </div>
                            </div>
                            <div class="achievement-item flex items-start gap-3 p-3 rounded-lg hover:bg-blue-50 transition-all duration-300 cursor-pointer group">
                                <div class="w-8 h-8 bg-blue-50 rounded-lg flex items-center justify-center mt-0.5 group-hover:bg-blue-200 group-hover:scale-110 transition-all duration-300 border border-blue-200 flex-shrink-0">
                                    <svg class="w-4 h-4 text-brand" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-sm font-medium text-gray-900 mb-1 group-hover:text-blue-600 transition-colors duration-300">Shopify's micro-animations</h3>
                                    <p class="text-xs text-gray-600 leading-relaxed">Inspired Sentio's button interactions</p>
                                </div>
                            </div>
                            <div class="achievement-item flex items-start gap-3 p-3 rounded-lg hover:bg-blue-50 transition-all duration-300 cursor-pointer group">
                                <div class="w-8 h-8 bg-blue-50 rounded-lg flex items-center justify-center mt-0.5 group-hover:bg-blue-200 group-hover:scale-110 transition-all duration-300 border border-blue-200 flex-shrink-0">
                                    <svg class="w-4 h-4 text-brand" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-sm font-medium text-gray-900 mb-1 group-hover:text-blue-600 transition-colors duration-300">Nike's trust signals</h3>
                                    <p class="text-xs text-gray-600 leading-relaxed">Adapted for Sentio's testimonial section</p>
                                </div>
                            </div>
                            <div class="achievement-item flex items-start gap-3 p-3 rounded-lg hover:bg-blue-50 transition-all duration-300 cursor-pointer group">
                                <div class="w-8 h-8 bg-blue-50 rounded-lg flex items-center justify-center mt-0.5 group-hover:bg-blue-200 group-hover:scale-110 transition-all duration-300 border border-blue-200 flex-shrink-0">
                                    <svg class="w-4 h-4 text-brand" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-sm font-medium text-gray-900 mb-1 group-hover:text-blue-600 transition-colors duration-300">E-commerce color psychology</h3>
                                    <p class="text-xs text-gray-600 leading-relaxed">Influenced Sentio's brand palette</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Realizations Card -->
                    <div class="achievement-card bg-white border border-gray-200 rounded-2xl p-6 hover:shadow-lg transition-all duration-300 hover:-translate-y-1" data-card="realizations">
                        <div class="achievement-card-content flex items-center justify-center gap-3 mb-6" data-content="realizations-header">
                            <div class="w-10 h-10 bg-amber-100 rounded-xl flex items-center justify-center">
                                <svg class="w-5 h-5 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                                </svg>
                            </div>
                            <h4 class="text-lg font-semibold text-gray-900">Realizations</h4>
                        </div>
                        <div class="achievement-card-content space-y-4" data-content="realizations-items">
                            <div class="achievement-item flex items-start gap-3 p-3 rounded-lg hover:bg-amber-50 transition-all duration-300 cursor-pointer group">
                                <div class="w-8 h-8 bg-amber-50 rounded-lg flex items-center justify-center mt-0.5 group-hover:bg-amber-200 group-hover:scale-110 transition-all duration-300 border border-amber-200 flex-shrink-0">
                                    <svg class="w-4 h-4 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-sm font-medium text-gray-900 mb-1 group-hover:text-amber-600 transition-colors duration-300">E-commerce research was actually perfect for SaaS</h3>
                                    <p class="text-xs text-gray-600 leading-relaxed">Landing pages benefit from proven conversion patterns</p>
                                </div>
                            </div>
                            <div class="achievement-item flex items-start gap-3 p-3 rounded-lg hover:bg-amber-50 transition-all duration-300 cursor-pointer group">
                                <div class="w-8 h-8 bg-amber-50 rounded-lg flex items-center justify-center mt-0.5 group-hover:bg-amber-200 group-hover:scale-110 transition-all duration-300 border border-amber-200 flex-shrink-0">
                                    <svg class="w-4 h-4 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L5.636 5.636"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-sm font-medium text-gray-900 mb-1 group-hover:text-amber-600 transition-colors duration-300">Conversion psychology works the same</h3>
                                    <p class="text-xs text-gray-600 leading-relaxed">For products and software alike</p>
                                </div>
                            </div>
                            <div class="achievement-item flex items-start gap-3 p-3 rounded-lg hover:bg-amber-50 transition-all duration-300 cursor-pointer group">
                                <div class="w-8 h-8 bg-amber-50 rounded-lg flex items-center justify-center mt-0.5 group-hover:bg-amber-200 group-hover:scale-110 transition-all duration-300 border border-amber-200 flex-shrink-0">
                                    <svg class="w-4 h-4 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 9l3 3m0 0l-3 3m3-3H8m13 0a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-sm font-medium text-gray-900 mb-1 group-hover:text-amber-600 transition-colors duration-300">Not everything from e-commerce works for SaaS</h3>
                                    <p class="text-xs text-gray-600 leading-relaxed">Pricing tables need different approaches</p>
                                </div>
                            </div>
                            <div class="achievement-item flex items-start gap-3 p-3 rounded-lg hover:bg-amber-50 transition-all duration-300 cursor-pointer group">
                                <div class="w-8 h-8 bg-amber-50 rounded-lg flex items-center justify-center mt-0.5 group-hover:bg-amber-200 group-hover:scale-110 transition-all duration-300 border border-amber-200 flex-shrink-0">
                                    <svg class="w-4 h-4 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-sm font-medium text-gray-900 mb-1 group-hover:text-amber-600 transition-colors duration-300">Learning from masters beats starting from scratch</h3>
                                    <p class="text-xs text-gray-600 leading-relaxed">Build on proven foundations</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Research Patterns Identified -->
            <div class="mb-8" id="patterns-section">
                <div class="flex items-center justify-center gap-3 mb-6">
                    <div class="w-10 h-10 bg-purple-100 rounded-xl flex items-center justify-center">
                        <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"/>
                        </svg>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900">E-commerce Patterns I Want to Study</h4>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                    <!-- Promising Patterns -->
                    <div id="patterns-card" class="bg-white border border-gray-200 rounded-2xl p-6 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 relative">
                        <!-- Tooltip Indicator - positioned between "Breathing room" and "Clear hierarchy" -->
                        <div class="absolute -left-48 top-40 z-10 pointer-events-none">
                            <div class="flex items-center">
                                <!-- Clean tooltip with SVG arrow and animations -->
                                <div class="tooltip-card flex items-center gap-2 px-3 py-2 rounded-lg bg-white border border-gray-300 shadow-lg pointer-events-none">
                                    <span class="tooltip-text text-black text-sm font-medium whitespace-nowrap">Start hovering here!</span>
                                    <!-- SVG arrow icon -->
                                    <svg class="tooltip-arrow w-4 h-4 text-gray-600 pointer-events-none" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"/>
                                    </svg>
                                </div>
                            </div>
                        </div>
                        <div class="flex items-center justify-center gap-3 mb-6">
                            <div class="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center">
                                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                            </div>
                            <h4 class="text-lg font-semibold text-gray-900">Patterns That Look Promising</h4>
                        </div>
                        <div class="space-y-4">
                            <div class="flex items-start gap-3 group">
                                <div class="w-8 h-8 bg-green-50 rounded-lg flex items-center justify-center mt-0.5 group-hover:bg-green-100 transition-colors border border-green-200">
                                    <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <div class="text-sm font-medium text-gray-900 mb-1 group-hover:text-gray-700 transition-colors">Clean, minimal layouts that build trust</div>
                                    <div class="text-xs text-gray-600 leading-relaxed">Foundation of trustworthy e-commerce design</div>
                                </div>
                            </div>
                            <div class="flex items-start gap-3 group">
                                <div class="w-8 h-8 bg-green-50 rounded-lg flex items-center justify-center mt-0.5 group-hover:bg-green-100 transition-colors border border-green-200">
                                    <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <div class="text-sm font-medium text-gray-900 mb-1 group-hover:text-gray-700 transition-colors">Strategic use of white space</div>
                                    <div class="text-xs text-gray-600 leading-relaxed">Breathing room makes content digestible</div>
                                </div>
                            </div>
                            <div class="flex items-start gap-3 group">
                                <div class="w-8 h-8 bg-green-50 rounded-lg flex items-center justify-center mt-0.5 group-hover:bg-green-100 transition-colors border border-green-200">
                                    <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <div class="text-sm font-medium text-gray-900 mb-1 group-hover:text-gray-700 transition-colors">Clear hierarchy and navigation</div>
                                    <div class="text-xs text-gray-600 leading-relaxed">Users should never feel lost or confused</div>
                                </div>
                            </div>
                            <div class="flex items-start gap-3 group">
                                <div class="w-8 h-8 bg-green-50 rounded-lg flex items-center justify-center mt-0.5 group-hover:bg-green-100 transition-colors border border-green-200">
                                    <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <div class="text-sm font-medium text-gray-900 mb-1 group-hover:text-gray-700 transition-colors">Social proof placement strategies</div>
                                    <div class="text-xs text-gray-600 leading-relaxed">Reviews and testimonials at the right moments</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Questionable Patterns -->
                    <div id="saas-card" class="bg-white border border-gray-200 rounded-2xl p-6 hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                        <div class="flex items-center justify-center gap-3 mb-6">
                            <div class="w-10 h-10 bg-red-100 rounded-xl flex items-center justify-center">
                                <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                            </div>
                            <h4 class="text-lg font-semibold text-gray-900">Need to Figure Out for SaaS</h4>
                        </div>
                        <div class="space-y-4">
                            <div class="flex items-start gap-3 group">
                                <div class="w-8 h-8 bg-red-50 rounded-lg flex items-center justify-center mt-0.5 group-hover:bg-red-100 transition-colors border border-red-200">
                                    <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <div class="text-sm font-medium text-gray-900 mb-1 group-hover:text-gray-700 transition-colors">How to adapt product grids for features</div>
                                    <div class="text-xs text-gray-600 leading-relaxed">Products vs features need different layouts</div>
                                </div>
                            </div>
                            <div class="flex items-start gap-3 group">
                                <div class="w-8 h-8 bg-red-50 rounded-lg flex items-center justify-center mt-0.5 group-hover:bg-red-100 transition-colors border border-red-200">
                                    <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 3H3m4 10v6a1 1 0 001 1h9a1 1 0 001-1v-6m-10 0h10"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <div class="text-sm font-medium text-gray-900 mb-1 group-hover:text-gray-700 transition-colors">Shopping cart vs. demo request flow</div>
                                    <div class="text-xs text-gray-600 leading-relaxed">Different user journeys, different patterns</div>
                                </div>
                            </div>
                            <div class="flex items-start gap-3 group">
                                <div class="w-8 h-8 bg-red-50 rounded-lg flex items-center justify-center mt-0.5 group-hover:bg-red-100 transition-colors border border-red-200">
                                    <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <div class="text-sm font-medium text-gray-900 mb-1 group-hover:text-gray-700 transition-colors">Pricing display for subscriptions</div>
                                    <div class="text-xs text-gray-600 leading-relaxed">Monthly vs annual, tiers, and transparency</div>
                                </div>
                            </div>
                            <div class="flex items-start gap-3 group">
                                <div class="w-8 h-8 bg-red-50 rounded-lg flex items-center justify-center mt-0.5 group-hover:bg-red-100 transition-colors border border-red-200">
                                    <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <div class="text-sm font-medium text-gray-900 mb-1 group-hover:text-gray-700 transition-colors">Reviews vs. use case testimonials</div>
                                    <div class="text-xs text-gray-600 leading-relaxed">Star ratings vs detailed success stories</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modern Research Findings Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6 mb-6 sm:mb-8">
                <!-- Research Questions Card -->
                <div id="questions-card" class="bg-white border border-gray-200 rounded-2xl p-6 hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                    <div class="flex items-center justify-center gap-3 mb-6">
                        <div class="w-10 h-10 bg-red-100 rounded-xl flex items-center justify-center">
                            <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <h4 class="text-lg font-semibold text-gray-900">Questions I Had to Answer</h4>
                    </div>
                    <div class="space-y-4">
                        <div class="flex items-start gap-3 group">
                            <div class="w-8 h-8 bg-red-50 rounded-lg flex items-center justify-center mt-0.5 group-hover:bg-red-100 transition-colors border border-red-200">
                                <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"/>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-900 mb-1">Why does Amazon look so... boring?</div>
                                <div class="text-xs text-gray-600 leading-relaxed">Turns out "boring" = trustworthy. Clean layouts beat flashy animations</div>
                            </div>
                        </div>
                        <div class="flex items-start gap-3 group">
                            <div class="w-8 h-8 bg-red-50 rounded-lg flex items-center justify-center mt-0.5 group-hover:bg-red-100 transition-colors border border-red-200">
                                <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-900 mb-1">Should I use React or just vanilla JS?</div>
                                <div class="text-xs text-gray-600 leading-relaxed">Spent 3 hours on this. Answer: React. My future self will thank me</div>
                            </div>
                        </div>
                        <div class="flex items-start gap-3 group">
                            <div class="w-8 h-8 bg-red-50 rounded-lg flex items-center justify-center mt-0.5 group-hover:bg-red-100 transition-colors border border-red-200">
                                <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"/>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-900 mb-1">How do I make it look "professional"?</div>
                                <div class="text-xs text-gray-600 leading-relaxed">Consistent spacing, real product photos, and NO Comic Sans</div>
                            </div>
                        </div>
                        <div class="flex items-start gap-3 group">
                            <div class="w-8 h-8 bg-red-50 rounded-lg flex items-center justify-center mt-0.5 group-hover:bg-red-100 transition-colors border border-red-200">
                                <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"/>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-900 mb-1">Where do I store all this product data?</div>
                                <div class="text-xs text-gray-600 leading-relaxed">Local storage vs database? Started simple, will scale later</div>
                            </div>
                        </div>
                        <div class="flex items-start gap-3 group">
                            <div class="w-8 h-8 bg-red-50 rounded-lg flex items-center justify-center mt-0.5 group-hover:bg-red-100 transition-colors border border-red-200">
                                <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"/>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-900 mb-1">How do payment systems actually work?</div>
                                <div class="text-xs text-gray-600 leading-relaxed">Stripe handles the scary stuff. I just need to integrate their API</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Key Discoveries Card -->
                <div id="discoveries-card" class="bg-white border border-gray-200 rounded-2xl p-6 hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                    <div class="flex items-center justify-center gap-3 mb-6">
                        <div class="w-10 h-10 bg-emerald-100 rounded-xl flex items-center justify-center">
                            <svg class="w-5 h-5 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                            </svg>
                        </div>
                        <h4 class="text-lg font-semibold text-gray-900">Key Discoveries</h4>
                    </div>
                    <div class="space-y-3">
                        <!-- Clean Layout Discovery -->
                        <div class="discovery-card-3d bg-gray-50 p-4 rounded-xl border border-blue-200 group hover:bg-gray-100 transition-all duration-300 relative">
                            <div class="flex items-center gap-4">
                                <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center flex-shrink-0 border border-blue-600">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <div class="font-semibold text-gray-900">Clean layouts build trust</div>
                                    <div class="text-sm text-gray-600">White space = breathing room for users</div>
                                </div>
                            </div>
                        </div>

                        <!-- Button Psychology Discovery -->
                        <div class="discovery-card-3d bg-gray-50 p-4 rounded-xl border border-green-200 group hover:bg-gray-100 transition-all duration-300 relative">
                            <div class="flex items-center gap-4">
                                <div class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center flex-shrink-0 border border-green-600">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <div class="font-semibold text-gray-900">Big buttons get more clicks</div>
                                    <div class="text-sm text-gray-600">UX psychology in action</div>
                                </div>
                            </div>
                        </div>

                        <!-- Performance Discovery -->
                        <div class="discovery-card-3d bg-gray-50 p-4 rounded-xl border border-orange-200 group hover:bg-gray-100 transition-all duration-300 relative">
                            <div class="flex items-center gap-4">
                                <div class="w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center flex-shrink-0 border border-orange-600">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <div class="font-semibold text-gray-900">Speed matters more than fancy</div>
                                    <div class="text-sm text-gray-600">3 seconds = make or break</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

            <!-- Research Stats - Full Width Section -->
            <div class="py-4">
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
                        <!-- Browser Tabs Stat -->
                        <div class="stat-card-animate bg-white border border-gray-200 rounded-2xl p-4 sm:p-6 hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                            <div class="flex items-center gap-4 mb-4">
                                <div class="stat-icon-animate w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
                                    <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M3.5 6.75C3.5 6.33579 3.83579 6 4.25 6H19.7514C20.1656 6 20.5014 6.33579 20.5014 6.75L20.5014 15.513L22.0014 16.6088L22.0014 6.75C22.0014 5.50736 20.9941 4.5 19.7514 4.5H4.25C3.00736 4.5 2 5.50736 2 6.75V17.25C2 18.4926 3.00736 19.5 4.25 19.5H13.6535L13.4919 18H4.25C3.83579 18 3.5 17.6642 3.5 17.25V6.75Z" fill="#2563eb"/>
                                        <path d="M18.7005 14.1143H18.5867L17.4784 13.3046L17.1965 11.958C17.1925 11.9459 17.1864 11.9399 17.1784 11.9399C17.1703 11.9399 17.1643 11.9459 17.1603 11.958L16.9798 12.9404L16.7118 12.7446C16.2591 12.4139 15.7106 12.2709 15.1738 12.3213L14.5972 10.37L13.9425 12.8342C13.5481 13.1633 13.2803 13.6164 13.1793 14.1143H12.5602C12.5039 14.1143 12.4696 14.0881 12.4576 14.0358L12.0227 11.958C12.0187 11.9459 12.0126 11.9399 12.0046 11.9399C11.9965 11.9399 11.9905 11.9459 11.9864 11.958L11.6059 14.0297C11.5938 14.0861 11.5616 14.1143 11.5093 14.1143H10.6093C10.5852 14.1143 10.563 14.1082 10.5429 14.0962C10.5228 14.08 10.5107 14.0599 10.5066 14.0358L9.42334 10.37L8.44946 14.0358C8.43738 14.0881 8.40517 14.1143 8.35282 14.1143H7.38642C7.33005 14.1143 7.29582 14.0881 7.28374 14.0358L6.84886 11.958C6.84483 11.9459 6.83879 11.9399 6.83074 11.9399C6.82269 11.9399 6.81665 11.9459 6.81262 11.958L6.4321 14.0297C6.42002 14.0861 6.38781 14.1143 6.33546 14.1143H5.4355C5.41134 14.1143 5.38919 14.1082 5.36906 14.0962C5.34893 14.08 5.33685 14.0599 5.33282 14.0358L4.1369 9.98895C4.13287 9.9809 4.13086 9.97083 4.13086 9.95875C4.13086 9.91043 4.15905 9.88627 4.21542 9.88627H5.24222C5.29457 9.88627 5.32678 9.91245 5.33886 9.96479L5.85226 11.9338C5.85629 11.9459 5.86233 11.954 5.87038 11.958C5.87843 11.958 5.88447 11.9499 5.8885 11.9338L6.29318 9.96479C6.30526 9.91245 6.33949 9.88627 6.39586 9.88627H7.2777C7.33005 9.88627 7.36226 9.91245 7.37434 9.96479L7.82734 11.9338C7.83137 11.9499 7.83741 11.958 7.84546 11.958C7.85351 11.958 7.85955 11.9499 7.86358 11.9338L8.31658 9.96479C8.32866 9.91245 8.36087 9.88627 8.41322 9.88627L10.416 9.88627C10.4684 9.88627 10.5006 9.91245 10.5127 9.96479L11.0261 11.9338C11.0301 11.9459 11.0362 11.954 11.0442 11.958C11.0523 11.958 11.0583 11.9499 11.0623 11.9338L11.467 9.96479C11.4791 9.91245 11.5133 9.88627 11.5697 9.88627H12.4515C12.5039 9.88627 12.5361 9.91245 12.5482 9.96479L13.0012 11.9338C13.0052 11.9499 13.0112 11.958 13.0193 11.958C13.0273 11.958 13.0334 11.9499 13.0374 11.9338L13.4904 9.96479C13.5025 9.91245 13.5347 9.88627 13.587 9.88627H15.5899C15.6422 9.88627 15.6744 9.91245 15.6865 9.96479L16.1999 11.9338C16.2039 11.9459 16.21 11.954 16.218 11.958C16.2261 11.958 16.2321 11.9499 16.2362 11.9338L16.6408 9.96479C16.6529 9.91245 16.6871 9.88627 16.7435 9.88627H17.6254C17.6777 9.88627 17.7099 9.91245 17.722 9.96479L18.175 11.9338C18.179 11.9499 18.1851 11.958 18.1931 11.958C18.2012 11.958 18.2072 11.9499 18.2112 11.9338L18.6642 9.96479C18.6763 9.91245 18.7085 9.88627 18.7609 9.88627H19.7937C19.8259 9.88627 19.8481 9.89634 19.8602 9.91647C19.8763 9.93258 19.8803 9.95674 19.8722 9.98895L18.7971 14.0358C18.785 14.0881 18.7528 14.1143 18.7005 14.1143Z" fill="#2563eb"/>
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M15.0065 13.9953C15.2637 13.8469 15.5842 13.8641 15.8239 14.0392L20.6798 17.5866C20.9216 17.7632 21.0351 18.0666 20.9687 18.3586C20.9023 18.6505 20.6686 18.875 20.3743 18.9296L18.8817 19.2066L19.6831 20.5947C19.8902 20.9534 19.7673 21.4121 19.4086 21.6192C19.0499 21.8263 18.5912 21.7034 18.3841 21.3447L17.5825 19.9564L16.5958 21.1111C16.4013 21.3387 16.0901 21.4288 15.8041 21.3404C15.5181 21.2519 15.312 21.0018 15.28 20.7042L14.6358 14.7252C14.604 14.43 14.7494 14.1438 15.0065 13.9953ZM16.5851 18.8147L16.7863 18.5791C17.121 18.1875 17.5798 17.9226 18.0863 17.8286L18.3909 17.7721L16.3089 16.2511L16.5851 18.8147Z" fill="#2563eb"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <div class="stat-number-animate text-2xl font-bold text-blue-600 mb-1">47</div>
                                    <div class="stat-label-animate text-sm text-gray-600 font-medium">Browser Tabs</div>
                                </div>
                            </div>
                        </div>

                        <!-- Hours Per Day Stat -->
                        <div class="stat-card-animate bg-white border border-gray-200 rounded-2xl p-4 sm:p-6 hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                            <div class="flex items-center gap-3 sm:gap-4 mb-4">
                                <div class="stat-icon-animate w-8 h-8 sm:w-10 sm:h-10 bg-green-100 rounded-xl flex items-center justify-center">
                                    <svg class="w-4 h-4 sm:w-5 sm:h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <div class="stat-number-animate text-xl sm:text-2xl font-bold text-green-600 mb-1">6-7</div>
                                    <div class="stat-label-animate text-xs sm:text-sm text-gray-600 font-medium">Hours a Day</div>
                                </div>
                            </div>
                        </div>

                        <!-- Days Done Stat -->
                        <div class="stat-card-animate bg-white border border-gray-200 rounded-2xl p-6 hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                            <div class="flex items-center gap-4 mb-4">
                                <div class="stat-icon-animate w-10 h-10 bg-purple-100 rounded-xl flex items-center justify-center">
                                    <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <div class="stat-number-animate text-2xl font-bold text-purple-600 mb-1">2-6</div>
                                    <div class="stat-label-animate text-sm text-gray-600 font-medium">Days Done</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
        </section>

        <!-- Building Section -->
        <section id="building" class="bg-white rounded-2xl p-4 sm:p-6 lg:p-8 mb-8 sm:mb-12 shadow-lg hover-lift animate-slide-in">
            <!-- Text-Fitted Box Reveal Section Header -->
            <div class="mb-12">
                <!-- Phase indicator with individual reveal -->
                <div class="flex items-center gap-3 mb-4">
                    <div class="search-icon-blur w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold text-sm">B</span>
                    </div>
                    <div class="text-reveal">
                        <span class="text-reveal-content slide-up text-sm font-medium text-green-600 uppercase tracking-wide">Phase 02</span>
                        <div class="text-reveal-overlay"></div>
                    </div>
                </div>

                <!-- Title with individual reveal -->
                <div class="block-text-reveal mb-3">
                    <h2 class="text-reveal-content slide-up text-2xl sm:text-3xl font-bold text-gray-900">Building & Development</h2>
                    <div class="text-reveal-overlay"></div>
                </div>

                <!-- Description with individual reveal -->
                <div class="block-text-reveal">
                    <p class="text-reveal-content slide-up text-lg sm:text-xl font-medium text-gray-700 leading-relaxed max-w-3xl">Time to put my HTML, CSS, and JavaScript skills to work! Built everything from scratch using Tailwind CSS for styling and vanilla JS for interactions. Lots of trial and error, but finally got those smooth animations working.</p>
                    <div class="text-reveal-overlay"></div>
                </div>
            </div>

            <!-- Code evolution showcase -->
            <div class="mb-8 code-evolution-container" id="code-evolution-section">
                <div class="flex items-center gap-3 mb-6 code-header">
                    <div class="w-10 h-10 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl flex items-center justify-center border border-blue-200">
                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"/>
                        </svg>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-lg font-semibold text-gray-900">Code Evolution Journey</h3>
                        <p class="text-sm text-gray-600 mt-1">From basic functionality to sophisticated user experience</p>
                    </div>
                </div>

                <!-- Evolution Timeline -->
                <div class="relative">


                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6 md:gap-8">
                        <!-- Initial Implementation -->
                        <div class="group relative code-card" data-card="initial">
                            <div class="bg-white border border-gray-200 rounded-2xl p-6 transition-all duration-300 hover:shadow-lg hover:border-red-300">
                                <div class="flex items-center gap-3 mb-4 code-card-content code-header">
                                    <div class="w-8 h-8 bg-red-50 rounded-lg flex items-center justify-center border border-red-200 group-hover:bg-red-100 transition-colors">
                                        <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                        </svg>
                                    </div>
                                    <div class="flex-1">
                                        <div class="text-sm font-semibold text-gray-900 mb-1">Initial Approach</div>
                                        <div class="text-xs text-gray-600 leading-relaxed">Simple but functional cart system</div>
                                    </div>
                                    <div class="text-xs bg-red-50 text-red-700 px-2 py-1 rounded-full border border-red-200">
                                        Basic
                                    </div>
                                </div>
                                <div class="code-block text-left mb-4 code-card-content">
                                    <div><span class="code-keyword">function</span> <span class="code-string">addToCart</span>() {</div>
                                    <div>&nbsp;&nbsp;cartCount++;</div>
                                    <div>&nbsp;&nbsp;alert(<span class="code-string">"Added to cart!"</span>);</div>
                                    <div>&nbsp;&nbsp;console.log(<span class="code-string">"Cart updated"</span>);</div>
                                    <div>&nbsp;&nbsp;document.querySelector(<span class="code-string">'.cart-count'</span>).textContent = cartCount;</div>
                                    <div>&nbsp;&nbsp;<span class="code-keyword">return</span> cartCount;</div>
                                    <div>}</div>
                                </div>
                                <div class="flex items-center gap-2 text-xs text-gray-500 code-card-content code-footer">
                                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                    <span>Hardcoded values, basic alerts</span>
                                </div>
                            </div>
                        </div>

                        <!-- Enhanced Implementation -->
                        <div class="group relative code-card" data-card="enhanced">
                            <!-- Enhanced System Tooltip -->
                            <div class="absolute left-full top-1/2 transform -translate-y-1/2 -translate-x-16 z-10 pointer-events-none">
                                <div class="flex items-center">
                                    <!-- Tooltip indicator arrow pointing left -->
                                    <div class="enhanced-tooltip-arrow w-0 h-0 border-t-8 border-b-8 border-r-8 border-t-transparent border-b-transparent border-r-white mr-1 drop-shadow-sm"></div>
                                    <!-- Expanded tooltip box -->
                                    <div class="enhanced-tooltip-card px-5 py-4 rounded-xl bg-white border border-gray-300 shadow-lg pointer-events-none" style="width: 280px;">
                                        <span class="enhanced-tooltip-text text-black text-sm font-medium leading-relaxed block">Shows evolution from basic alerts to smooth animations and visual feedback</span>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white border border-gray-200 rounded-2xl p-6 transition-all duration-300 hover:shadow-lg hover:border-green-300">
                                <div class="flex items-center gap-3 mb-4 code-card-content code-header">
                                    <div class="w-8 h-8 bg-green-50 rounded-lg flex items-center justify-center border border-green-200 group-hover:bg-green-100 transition-colors">
                                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                        </svg>
                                    </div>
                                    <div class="flex-1">
                                        <div class="text-sm font-semibold text-gray-900 mb-1">Enhanced System</div>
                                        <div class="text-xs text-gray-600 leading-relaxed">Dynamic interactions with smooth animations</div>
                                    </div>
                                    <div class="text-xs bg-green-50 text-green-700 px-2 py-1 rounded-full border border-green-200">
                                        Advanced
                                    </div>
                                </div>
                                <div class="code-block text-left mb-4 code-card-content">
                                    <div><span class="code-keyword">function</span> <span class="code-string">addToCart</span>(button) {</div>
                                    <div>&nbsp;&nbsp;button.style.transform = <span class="code-string">'scale(0.95)'</span>;</div>
                                    <div>&nbsp;&nbsp;button.innerHTML = <span class="code-string">'Adding...'</span>;</div>
                                    <div>&nbsp;&nbsp;cartCount++;</div>
                                    <div>&nbsp;&nbsp;animateCart();</div>
                                    <div>&nbsp;&nbsp;button.style.background = <span class="code-string">'#10b981'</span>;</div>
                                    <div>&nbsp;&nbsp;button.innerHTML = <span class="code-string">'Added!'</span>;</div>
                                    <div>}</div>
                                </div>
                                <div class="flex items-center gap-2 text-xs text-gray-500 code-card-content code-footer">
                                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                    <span>Dynamic parameters, visual feedback, state management</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Learning Insights -->
                <div class="mt-6 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-4 scroll-fade-container" data-scroll-trigger="key-learning">
                    <div class="flex items-start gap-3">
                        <div class="w-6 h-6 bg-blue-100 rounded-lg flex items-center justify-center mt-0.5 scroll-fade-element" data-delay="0">
                            <svg class="w-3 h-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <div class="text-sm font-medium text-blue-900 mb-1 scroll-fade-element delay-1" data-delay="1">Key Learning</div>
                            <div class="text-xs text-blue-700 leading-relaxed scroll-fade-element delay-2" data-delay="2">
                                This evolution shows how I learned to think beyond basic functionality to create engaging user experiences.
                                The shift from static alerts to dynamic button states and animations demonstrates growing UX awareness.
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Interactive learning moments -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6 mb-4 sm:mb-6">
                <div class="group relative feature-card">
                    <div class="bg-white border border-gray-200 rounded-2xl p-6 transition-all duration-300 hover:shadow-lg hover:border-green-300">
                        <div class="flex items-center gap-3 mb-6">
                            <div class="w-8 h-8 bg-green-50 rounded-lg flex items-center justify-center border border-green-200 group-hover:bg-green-100 transition-colors">
                                <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <h4 class="text-lg font-semibold text-gray-900 mb-1">What I Actually Built</h4>
                                <div class="text-xs text-gray-600">Key features and functionality</div>
                            </div>
                            <div class="text-xs bg-green-50 text-green-700 px-2 py-1 rounded-full border border-green-200">
                                Features
                            </div>
                        </div>

                        <div class="space-y-3" id="features-slideshow">
                            <div class="feature-item flex items-center gap-3 p-3 bg-blue-50 rounded-lg border border-blue-200 transform transition-all duration-500 hover:bg-blue-100 hover:scale-110 hover:shadow-2xl hover:-translate-y-2 hover:rotate-1 cursor-pointer" data-index="0">
                                <div class="w-6 h-6 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0 transition-transform duration-500 hover:scale-125 hover:rotate-12">
                                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                                    </svg>
                                </div>
                                <span class="text-sm font-medium text-blue-900">Real-time search</span>
                            </div>

                            <div class="feature-item flex items-center gap-3 p-3 bg-red-50 rounded-lg border border-red-200 transform transition-all duration-500 hover:bg-red-100 hover:scale-110 hover:shadow-2xl hover:-translate-y-2 hover:rotate-1 cursor-pointer" data-index="1">
                                <div class="w-6 h-6 bg-red-100 rounded-lg flex items-center justify-center flex-shrink-0 transition-transform duration-500 hover:scale-125 hover:rotate-12">
                                    <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                                    </svg>
                                </div>
                                <span class="text-sm font-medium text-red-900">Interactive wishlist</span>
                            </div>

                            <div class="feature-item flex items-center gap-3 p-3 bg-orange-50 rounded-lg border border-orange-200 transform transition-all duration-500 hover:bg-orange-100 hover:scale-110 hover:shadow-2xl hover:-translate-y-2 hover:rotate-1 cursor-pointer" data-index="2">
                                <div class="w-6 h-6 bg-orange-100 rounded-lg flex items-center justify-center flex-shrink-0 transition-transform duration-500 hover:scale-125 hover:rotate-12">
                                    <svg class="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17M17 13v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01"/>
                                    </svg>
                                </div>
                                <span class="text-sm font-medium text-orange-900">Animated cart</span>
                            </div>

                            <div class="feature-item flex items-center gap-3 p-3 bg-yellow-50 rounded-lg border border-yellow-200 transform transition-all duration-500 hover:bg-yellow-100 hover:scale-110 hover:shadow-2xl hover:-translate-y-2 hover:rotate-1 cursor-pointer" data-index="3">
                                <div class="w-6 h-6 bg-yellow-100 rounded-lg flex items-center justify-center flex-shrink-0 transition-transform duration-500 hover:scale-125 hover:rotate-12">
                                    <svg class="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"/>
                                    </svg>
                                </div>
                                <span class="text-sm font-medium text-yellow-900">Star rating system</span>
                            </div>

                            <div class="feature-item flex items-center gap-3 p-3 bg-purple-50 rounded-lg border border-purple-200 transform transition-all duration-500 hover:bg-purple-100 hover:scale-110 hover:shadow-2xl hover:-translate-y-2 hover:rotate-1 cursor-pointer" data-index="4">
                                <div class="w-6 h-6 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0 transition-transform duration-500 hover:scale-125 hover:rotate-12">
                                    <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                                    </svg>
                                </div>
                                <span class="text-sm font-medium text-purple-900">Responsive header</span>
                            </div>

                            <div class="feature-item flex items-center gap-3 p-3 bg-pink-50 rounded-lg border border-pink-200 transform transition-all duration-500 hover:bg-pink-100 hover:scale-110 hover:shadow-2xl hover:-translate-y-2 hover:rotate-1 cursor-pointer" data-index="5">
                                <div class="w-6 h-6 bg-pink-100 rounded-lg flex items-center justify-center flex-shrink-0 transition-transform duration-500 hover:scale-125 hover:rotate-12">
                                    <svg class="w-4 h-4 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"/>
                                    </svg>
                                </div>
                                <span class="text-sm font-medium text-pink-900">CSS animations</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="group relative struggle-card">
                    <div class="bg-white border border-gray-200 rounded-2xl p-6 transition-all duration-300 hover:shadow-lg hover:border-orange-300">
                        <div class="flex items-center gap-3 mb-6">
                            <div class="w-8 h-8 bg-orange-50 rounded-lg flex items-center justify-center border border-orange-200 group-hover:bg-orange-100 transition-colors">
                                <svg class="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <h4 class="text-lg font-semibold text-gray-900 mb-1">Daily Struggle Tracker</h4>
                                <div class="text-xs text-gray-600">Real-time development challenges</div>
                            </div>
                            <div class="text-xs bg-orange-50 text-orange-700 px-2 py-1 rounded-full border border-orange-200">
                                Progress
                            </div>
                        </div>

                        <div class="space-y-3">
                            <!-- Overwhelming Code Lines -->
                            <div class="group/item relative bg-white border border-red-100 rounded-lg p-3 hover:shadow-lg hover:border-red-200 transition-all duration-400 hover:-translate-y-1">
                                <div class="absolute inset-0 bg-gradient-to-r from-red-50 to-pink-50 rounded-lg opacity-0 group-hover/item:opacity-100 transition-opacity duration-400"></div>
                                <div class="relative">
                                    <div class="flex items-center justify-between mb-2">
                                        <div class="flex items-center gap-3">
                                            <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center group-hover/item:scale-110 transition-transform duration-300">
                                                <span class="text-lg">📄</span>
                                            </div>
                                            <div>
                                                <span class="text-sm font-semibold text-gray-900">Overwhelming Code Lines</span>
                                                <div class="text-xs text-red-600 font-medium">Peak Frustration Level</div>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <div class="text-sm font-bold text-red-600">90%</div>
                                            <div class="text-xs text-gray-500">Struggle</div>
                                        </div>
                                    </div>
                                    <div class="relative">
                                        <div class="w-full bg-red-100 rounded-full h-2 overflow-hidden shadow-inner">
                                            <div class="animated-progress-bar bg-gradient-to-r from-red-400 via-red-500 to-red-600 h-2 rounded-full transition-all duration-1500 ease-out shadow-sm"
                                                 style="width: 0%;"
                                                 data-width="90%"></div>
                                        </div>
                                        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30 rounded-full animate-pulse"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- JavaScript Debugging -->
                            <div class="group/item relative bg-white border border-orange-100 rounded-lg p-3 hover:shadow-lg hover:border-orange-200 transition-all duration-400 hover:-translate-y-1">
                                <div class="absolute inset-0 bg-gradient-to-r from-orange-50 to-yellow-50 rounded-lg opacity-0 group-hover/item:opacity-100 transition-opacity duration-400"></div>
                                <div class="relative">
                                    <div class="flex items-center justify-between mb-2">
                                        <div class="flex items-center gap-3">
                                            <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center group-hover/item:scale-110 transition-transform duration-300">
                                                <span class="text-lg">🐛</span>
                                            </div>
                                            <div>
                                                <span class="text-sm font-semibold text-gray-900">JavaScript Debugging</span>
                                                <div class="text-xs text-orange-600 font-medium">Console.log Overload</div>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <div class="text-sm font-bold text-orange-600">75%</div>
                                            <div class="text-xs text-gray-500">Struggle</div>
                                        </div>
                                    </div>
                                    <div class="relative">
                                        <div class="w-full bg-orange-100 rounded-full h-2 overflow-hidden shadow-inner">
                                            <div class="animated-progress-bar bg-gradient-to-r from-yellow-400 via-orange-500 to-orange-600 h-2 rounded-full transition-all duration-1500 ease-out shadow-sm"
                                                 style="width: 0%;"
                                                 data-width="75%"></div>
                                        </div>
                                        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30 rounded-full animate-pulse"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Positioning Cards Div -->
                            <div class="group/item relative bg-white border border-blue-100 rounded-lg p-3 hover:shadow-lg hover:border-blue-200 transition-all duration-400 hover:-translate-y-1">
                                <div class="absolute inset-0 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg opacity-0 group-hover/item:opacity-100 transition-opacity duration-400"></div>
                                <div class="relative">
                                    <div class="flex items-center justify-between mb-2">
                                        <div class="flex items-center gap-3">
                                            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center group-hover/item:scale-110 transition-transform duration-300">
                                                <span class="text-lg">📐</span>
                                            </div>
                                            <div>
                                                <span class="text-sm font-semibold text-gray-900">Positioning Cards Div</span>
                                                <div class="text-xs text-blue-600 font-medium">Layout Nightmares</div>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <div class="text-sm font-bold text-blue-600">85%</div>
                                            <div class="text-xs text-gray-500">Struggle</div>
                                        </div>
                                    </div>
                                    <div class="relative">
                                        <div class="w-full bg-blue-100 rounded-full h-2 overflow-hidden shadow-inner">
                                            <div class="animated-progress-bar bg-gradient-to-r from-blue-400 via-blue-500 to-blue-600 h-2 rounded-full transition-all duration-1500 ease-out shadow-sm"
                                                 style="width: 0%;"
                                                 data-width="85%"></div>
                                        </div>
                                        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30 rounded-full animate-pulse"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Water and 20 Minute Break -->
                            <div class="group/item relative bg-white border border-green-100 rounded-lg p-3 hover:shadow-lg hover:border-green-200 transition-all duration-400 hover:-translate-y-1">
                                <div class="absolute inset-0 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg opacity-0 group-hover/item:opacity-100 transition-opacity duration-400"></div>
                                <div class="relative">
                                    <div class="flex items-center justify-between mb-2">
                                        <div class="flex items-center gap-3">
                                            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center group-hover/item:scale-110 transition-transform duration-300">
                                                <span class="text-lg">💧</span>
                                            </div>
                                            <div>
                                                <span class="text-sm font-semibold text-gray-900">Water and 20 Minute Break</span>
                                                <div class="text-xs text-green-600 font-medium">To gain energy after coding</div>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <div class="text-sm font-bold text-green-600">100%</div>
                                            <div class="text-xs text-gray-500">Essential</div>
                                        </div>
                                    </div>
                                    <div class="relative">
                                        <div class="w-full bg-green-100 rounded-full h-2 overflow-hidden shadow-inner">
                                            <div class="animated-progress-bar bg-gradient-to-r from-green-400 via-green-500 to-green-600 h-2 rounded-full transition-all duration-1500 ease-out shadow-sm"
                                                 style="width: 0%;"
                                                 data-width="100%"></div>
                                        </div>
                                        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30 rounded-full animate-pulse"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Search Functionality Showcase -->
            <div class="bg-white border border-gray-200 rounded-2xl p-8 mb-8 hover:shadow-lg transition-all duration-500 hover:-translate-y-1 search-showcase-card">
                <!-- Header Section -->
                <div class="flex items-center gap-4 mb-8">
                    <div class="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-xl sm:text-2xl font-bold text-gray-900 mb-1">Advanced Search & Navigation System</h3>
                        <p class="text-gray-600">Complex dropdown navigation with <span class="font-semibold text-blue-600">smooth animations</span> and intelligent state management.</p>
                    </div>
                </div>

                <!-- Code Block -->
                <div class="rounded-2xl p-8 mb-8 border border-gray-800 shadow-lg code-block-animate relative" style="background: #1e1e1e;">
                    <!-- JavaScript Code Tooltip -->
                    <div class="absolute right-0 bottom-8 transform translate-x-3/4 z-10 pointer-events-none">
                        <div class="flex items-center">
                            <!-- Tooltip indicator arrow pointing left -->
                            <div class="js-tooltip-arrow w-0 h-0 border-t-8 border-b-8 border-r-8 border-t-transparent border-b-transparent border-r-white mr-1 drop-shadow-sm"></div>
                            <!-- Expanded tooltip box -->
                            <div class="js-tooltip-card px-4 py-3 rounded-lg bg-white border border-gray-300 shadow-lg pointer-events-none" style="width: 260px;">
                                <span class="js-tooltip-text text-black text-sm font-medium leading-relaxed block">Advanced DOM manipulation with state management and smooth animation effects</span>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center justify-between mb-6">
                        <span class="text-gray-400 text-sm font-medium">script.js</span>
                        <div class="flex items-center gap-2 text-gray-400 text-xs">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"/>
                            </svg>
                            <span>JavaScript</span>
                        </div>
                    </div>
                    <div class="font-mono text-sm leading-7">
                        <div class="text-gray-400 mb-3">// Advanced dropdown navigation with state management</div>
                        <div class="text-gray-200">
                            <span class="text-purple-400">function</span> <span class="text-yellow-400">initializeDropdownNavigation</span><span class="text-gray-300">() {</span>
                        </div>
                        <div class="text-gray-200 ml-4">
                            <span class="text-purple-400">const</span> <span class="text-blue-300">sharedDropdownContainer</span> <span class="text-gray-300">=</span> <span class="text-blue-300">document</span><span class="text-gray-300">.</span><span class="text-yellow-400">getElementById</span><span class="text-gray-300">(</span><span class="text-green-400">'sharedDropdownContainer'</span><span class="text-gray-300">);</span>
                        </div>
                        <div class="text-gray-200 ml-4">
                            <span class="text-purple-400">const</span> <span class="text-blue-300">featuresLink</span> <span class="text-gray-300">=</span> <span class="text-blue-300">document</span><span class="text-gray-300">.</span><span class="text-yellow-400">querySelector</span><span class="text-gray-300">(</span><span class="text-green-400">'.features-link'</span><span class="text-gray-300">);</span>
                        </div>
                        <div class="text-gray-200 ml-4">
                            <span class="text-purple-400">let</span> <span class="text-blue-300">currentPanel</span> <span class="text-gray-300">=</span> <span class="text-purple-400">null</span><span class="text-gray-300">;</span> <span class="text-gray-400">// Track active panel</span>
                        </div>
                        <div class="my-2"></div>
                        <div class="text-gray-200 ml-4">
                            <span class="text-gray-400">// Reset animation with blur and transform effects</span>
                        </div>
                        <div class="text-gray-200 ml-4">
                            <span class="text-purple-400">function</span> <span class="text-yellow-400">resetFeaturesTextAnimation</span><span class="text-gray-300">() {</span>
                        </div>
                        <div class="text-gray-200 ml-8">
                            <span class="text-purple-400">const</span> <span class="text-blue-300">featuresLinks</span> <span class="text-gray-300">=</span> <span class="text-blue-300">document</span><span class="text-gray-300">.</span><span class="text-yellow-400">querySelectorAll</span><span class="text-gray-300">(</span><span class="text-green-400">'.features-panel .dropdown-link'</span><span class="text-gray-300">);</span>
                        </div>
                        <div class="text-gray-200 ml-8">
                            <span class="text-blue-300">featuresLinks</span><span class="text-gray-300">.</span><span class="text-yellow-400">forEach</span><span class="text-gray-300">(</span><span class="text-orange-400">link</span> <span class="text-gray-300">=> {</span>
                        </div>
                        <div class="text-gray-200 ml-12">
                            <span class="text-orange-400">link</span><span class="text-gray-300">.</span><span class="text-blue-300">style</span><span class="text-gray-300">.</span><span class="text-blue-300">opacity</span> <span class="text-gray-300">=</span> <span class="text-green-400">'0'</span><span class="text-gray-300">;</span>
                        </div>
                        <div class="text-gray-200 ml-12">
                            <span class="text-orange-400">link</span><span class="text-gray-300">.</span><span class="text-blue-300">style</span><span class="text-gray-300">.</span><span class="text-blue-300">filter</span> <span class="text-gray-300">=</span> <span class="text-green-400">'blur(6px)'</span><span class="text-gray-300">;</span>
                        </div>
                        <div class="text-gray-200 ml-12">
                            <span class="text-orange-400">link</span><span class="text-gray-300">.</span><span class="text-blue-300">style</span><span class="text-gray-300">.</span><span class="text-blue-300">transform</span> <span class="text-gray-300">=</span> <span class="text-green-400">'translateY(30px)'</span><span class="text-gray-300">;</span>
                        </div>
                        <div class="text-gray-200 ml-8">
                            <span class="text-gray-300">});</span>
                        </div>
                        <div class="text-gray-200 ml-4">
                            <span class="text-gray-300">}</span>
                        </div>
                        <div class="text-gray-200">
                            <span class="text-gray-300">}</span>
                        </div>
                    </div>
                </div>

                <!-- Insight Box -->
                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-4 mb-8 scroll-fade-container" data-scroll-trigger="technical-insight">
                    <div class="flex items-start gap-3">
                        <div class="w-6 h-6 bg-blue-100 rounded-lg flex items-center justify-center mt-0.5 scroll-fade-element" data-delay="0">
                            <svg class="w-3 h-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <div class="text-sm font-medium text-blue-900 mb-1 scroll-fade-element delay-1" data-delay="1">Key Technical Insight</div>
                            <div class="text-xs text-blue-700 leading-relaxed scroll-fade-element delay-2" data-delay="2">
                                Complex state management with smooth blur and transform animations. The reflow trigger was crucial!
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Two Column Layout -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                    <!-- Challenges I Faced -->
                    <div class="challenges-card-animate bg-white border border-gray-200 rounded-2xl p-6 hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                        <div class="flex items-center justify-center gap-3 mb-6">
                            <div class="challenges-icon-animate w-10 h-10 bg-red-100 rounded-xl flex items-center justify-center">
                                <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                                </svg>
                            </div>
                            <h4 class="challenges-text-animate text-lg font-semibold text-gray-900">Challenges I Faced</h4>
                        </div>
                        <div class="space-y-4">
                            <div class="challenges-text-animate flex items-start gap-3 group">
                                <div class="w-8 h-8 bg-red-50 rounded-lg flex items-center justify-center mt-0.5 group-hover:bg-red-100 transition-colors border border-red-200">
                                    <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <div class="text-sm font-medium text-gray-900 mb-1">Event listener not firing</div>
                                    <div class="text-xs text-gray-600 leading-relaxed">Spent hours debugging why nothing happened</div>
                                </div>
                            </div>
                            <div class="challenges-text-animate flex items-start gap-3 group">
                                <div class="w-8 h-8 bg-red-50 rounded-lg flex items-center justify-center mt-0.5 group-hover:bg-red-100 transition-colors border border-red-200">
                                    <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <div class="text-sm font-medium text-gray-900 mb-1">Case sensitivity issues</div>
                                    <div class="text-xs text-gray-600 leading-relaxed">Search for "iPhone" vs "iphone" broke everything</div>
                                </div>
                            </div>
                            <div class="challenges-text-animate flex items-start gap-3 group">
                                <div class="w-8 h-8 bg-red-50 rounded-lg flex items-center justify-center mt-0.5 group-hover:bg-red-100 transition-colors border border-red-200">
                                    <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <div class="text-sm font-medium text-gray-900 mb-1">Performance with many items</div>
                                    <div class="text-xs text-gray-600 leading-relaxed">Laggy with 100+ product cards</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- What I Learned -->
                    <div class="challenges-card-animate bg-white border border-gray-200 rounded-2xl p-6 hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                        <div class="flex items-center justify-center gap-3 mb-6">
                            <div class="challenges-icon-animate w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center">
                                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                                </svg>
                            </div>
                            <h4 class="challenges-text-animate text-lg font-semibold text-gray-900">What I Learned</h4>
                        </div>
                        <div class="space-y-4">
                            <div class="challenges-text-animate flex items-start gap-3 group">
                                <div class="w-8 h-8 bg-green-50 rounded-lg flex items-center justify-center mt-0.5 group-hover:bg-green-100 transition-colors border border-green-200">
                                    <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <div class="text-sm font-medium text-gray-900 mb-1">Always use toLowerCase()</div>
                                    <div class="text-xs text-gray-600 leading-relaxed">Makes search case-insensitive and user-friendly</div>
                                </div>
                            </div>
                            <div class="challenges-text-animate flex items-start gap-3 group">
                                <div class="w-8 h-8 bg-green-50 rounded-lg flex items-center justify-center mt-0.5 group-hover:bg-green-100 transition-colors border border-green-200">
                                    <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <div class="text-sm font-medium text-gray-900 mb-1">Opacity transitions feel smooth</div>
                                    <div class="text-xs text-gray-600 leading-relaxed">Better UX than display: none/block</div>
                                </div>
                            </div>
                            <div class="challenges-text-animate flex items-start gap-3 group">
                                <div class="w-8 h-8 bg-green-50 rounded-lg flex items-center justify-center mt-0.5 group-hover:bg-green-100 transition-colors border border-green-200">
                                    <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <div class="text-sm font-medium text-gray-900 mb-1">Real-time feedback matters</div>
                                    <div class="text-xs text-gray-600 leading-relaxed">Users expect instant results as they type</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Breakthrough Moment -->
            <div class="bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 rounded-2xl p-8 mb-8 breakthrough-moment-enhanced">
                <div class="text-center mb-8">
                    <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg icon-progressive-reveal icon-container-enhanced" data-icon-animation="development-philosophy">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                        </svg>
                    </div>
                    <h3 class="text-2xl sm:text-3xl font-bold text-gray-900 mb-2 title-animated" data-title-animation="development-philosophy">The "It Actually Works!" Moment</h3>
                </div>

                <div class="bg-white border border-blue-200 rounded-xl p-6 relative overflow-hidden quote-container-enhanced quote-card-animated" data-card-animation="development-philosophy">
                    <div class="absolute top-0 left-0 w-2 h-full bg-gradient-to-b from-blue-500 to-blue-600"></div>
                    <div class="flex items-start gap-4">
                        <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center flex-shrink-0">
                            <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z"/>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <div class="text-lg text-gray-800 leading-relaxed mb-4 text-animation-container" data-text-animation="development-philosophy">
                                <span class="word-slide-up">The</span> <span class="word-slide-up">vision</span> <span class="word-slide-up">was</span> <span class="word-slide-up">to</span> <span class="word-slide-up">make</span> <span class="word-slide-up">every</span>
                                <span class="font-semibold text-blue-600 bg-blue-50 px-2 py-1 rounded highlight-reveal">interaction smooth</span><span class="word-slide-up">,</span>
                                <span class="word-slide-up">every</span> <span class="font-semibold text-blue-600 bg-blue-50 px-2 py-1 rounded highlight-reveal">animation purposeful</span><span class="word-slide-up">,</span> <span class="word-slide-up">and</span> <span class="word-slide-up">every</span>
                                <span class="font-semibold text-blue-600 bg-blue-50 px-2 py-1 rounded highlight-reveal">feature functional</span><span class="word-slide-up">.</span>
                            </div>
                            <div class="text-sm font-medium text-blue-600 uppercase tracking-wider blur-in">Development Philosophy</div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Achievement Cards -->
                <div class="mt-6 sm:mt-8 grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                    <!-- Technical Wins Card -->
                    <div class="bg-white border border-blue-200 rounded-2xl p-6 hover:shadow-lg transition-all duration-300 achievement-card" data-card="technical-wins">
                        <div class="flex items-center justify-center gap-3 mb-6 achievement-card-content" data-content="technical-wins-header">
                            <div class="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
                                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                            </div>
                            <h4 class="text-lg font-semibold text-gray-900">Technical Wins</h4>
                        </div>
                        <div class="space-y-4 achievement-card-content" data-content="technical-wins-items">
                            <div class="flex items-start gap-3 p-3 rounded-lg hover:bg-blue-50 transition-all duration-300 cursor-pointer group achievement-item" data-item="technical-1">
                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mt-0.5 flex-shrink-0 group-hover:bg-blue-200 group-hover:scale-110 transition-all duration-300">
                                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-sm font-medium text-gray-900 mb-1 group-hover:text-blue-600 transition-colors duration-300">Real-time search functionality</h3>
                                    <p class="text-xs text-gray-600 leading-relaxed">Dynamic filtering with instant results</p>
                                </div>
                            </div>
                            <div class="flex items-start gap-3 p-3 rounded-lg hover:bg-blue-50 transition-all duration-300 cursor-pointer group achievement-item" data-item="technical-2">
                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mt-0.5 flex-shrink-0 group-hover:bg-blue-200 group-hover:scale-110 transition-all duration-300">
                                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-sm font-medium text-gray-900 mb-1 group-hover:text-blue-600 transition-colors duration-300">Smooth CSS transitions</h3>
                                    <p class="text-xs text-gray-600 leading-relaxed">Polished animations and micro-interactions</p>
                                </div>
                            </div>
                            <div class="flex items-start gap-3 p-3 rounded-lg hover:bg-blue-50 transition-all duration-300 cursor-pointer group achievement-item" data-item="technical-3">
                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mt-0.5 flex-shrink-0 group-hover:bg-blue-200 group-hover:scale-110 transition-all duration-300">
                                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-sm font-medium text-gray-900 mb-1 group-hover:text-blue-600 transition-colors duration-300">Interactive UI elements</h3>
                                    <p class="text-xs text-gray-600 leading-relaxed">Engaging user experience design</p>
                                </div>
                            </div>
                            <div class="flex items-start gap-3 p-3 rounded-lg hover:bg-blue-50 transition-all duration-300 cursor-pointer group achievement-item" data-item="technical-4">
                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mt-0.5 flex-shrink-0 group-hover:bg-blue-200 group-hover:scale-110 transition-all duration-300">
                                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-sm font-medium text-gray-900 mb-1 group-hover:text-blue-600 transition-colors duration-300">Responsive design patterns</h3>
                                    <p class="text-xs text-gray-600 leading-relaxed">Mobile-first approach with fluid layouts</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Personal Growth Card -->
                    <div class="bg-white border border-purple-200 rounded-2xl p-6 hover:shadow-lg transition-all duration-300 achievement-card" data-card="personal-growth">
                        <div class="flex items-center justify-center gap-3 mb-6 achievement-card-content" data-content="personal-growth-header">
                            <div class="w-10 h-10 bg-purple-100 rounded-xl flex items-center justify-center">
                                <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                                </svg>
                            </div>
                            <h4 class="text-lg font-semibold text-gray-900">Personal Growth</h4>
                        </div>
                        <div class="space-y-4 achievement-card-content" data-content="personal-growth-items">
                            <div class="flex items-start gap-3 p-3 rounded-lg hover:bg-purple-50 transition-all duration-300 cursor-pointer group achievement-item" data-item="personal-1">
                                <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mt-0.5 flex-shrink-0 group-hover:bg-purple-200 group-hover:scale-110 transition-all duration-300">
                                    <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-sm font-medium text-gray-900 mb-1 group-hover:text-purple-600 transition-colors duration-300">Problem-solving confidence</h3>
                                    <p class="text-xs text-gray-600 leading-relaxed">Tackling complex challenges with determination</p>
                                </div>
                            </div>
                            <div class="flex items-start gap-3 p-3 rounded-lg hover:bg-purple-50 transition-all duration-300 cursor-pointer group achievement-item" data-item="personal-2">
                                <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mt-0.5 flex-shrink-0 group-hover:bg-purple-200 group-hover:scale-110 transition-all duration-300">
                                    <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-sm font-medium text-gray-900 mb-1 group-hover:text-purple-600 transition-colors duration-300">Debugging persistence</h3>
                                    <p class="text-xs text-gray-600 leading-relaxed">Never giving up until the solution is found</p>
                                </div>
                            </div>
                            <div class="flex items-start gap-3 p-3 rounded-lg hover:bg-purple-50 transition-all duration-300 cursor-pointer group achievement-item" data-item="personal-3">
                                <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mt-0.5 flex-shrink-0 group-hover:bg-purple-200 group-hover:scale-110 transition-all duration-300">
                                    <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-sm font-medium text-gray-900 mb-1 group-hover:text-purple-600 transition-colors duration-300">Feature implementation skills</h3>
                                    <p class="text-xs text-gray-600 leading-relaxed">Building functional solutions from concept to code</p>
                                </div>
                            </div>
                            <div class="flex items-start gap-3 p-3 rounded-lg hover:bg-purple-50 transition-all duration-300 cursor-pointer group achievement-item" data-item="personal-4">
                                <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mt-0.5 flex-shrink-0 group-hover:bg-purple-200 group-hover:scale-110 transition-all duration-300">
                                    <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-sm font-medium text-gray-900 mb-1 group-hover:text-purple-600 transition-colors duration-300">User experience thinking</h3>
                                    <p class="text-xs text-gray-600 leading-relaxed">Designing with the end user in mind</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Progress metrics -->
            <div class="pt-4 metrics-container">
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
                    <!-- Duration Stat -->
                    <div class="stat-card-animate bg-white border border-gray-200 rounded-2xl p-6 hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                        <div class="flex items-center gap-4 mb-4">
                            <div class="stat-icon-animate w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
                                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <div class="stat-number-animate text-2xl font-bold text-blue-600 mb-1">7-36 Progressing</div>
                                <div class="stat-label-animate text-sm text-gray-600 font-medium">Duration</div>
                            </div>
                        </div>
                    </div>

                    <!-- Code Lines Stat -->
                    <div class="stat-card-animate bg-white border border-gray-200 rounded-2xl p-6 hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                        <div class="flex items-center gap-4 mb-4">
                            <div class="stat-icon-animate w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center">
                                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"/>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <div class="stat-number-animate text-2xl font-bold text-green-600 mb-1">3,423 lines</div>
                                <div class="stat-label-animate text-sm text-gray-600 font-medium">Combined HTML, Tailwind CSS and Vanilla Javascript</div>
                            </div>
                        </div>
                    </div>

                    <!-- Functional Stat -->
                    <div class="stat-card-animate bg-white border border-gray-200 rounded-2xl p-6 hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                        <div class="flex items-center gap-4 mb-4">
                            <div class="stat-icon-animate w-10 h-10 bg-orange-100 rounded-xl flex items-center justify-center">
                                <svg class="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <div class="stat-number-animate text-2xl font-bold text-orange-600 mb-1 flex items-center gap-2">
                                    <span>Debugging</span>
                                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                    </svg>
                                </div>
                                <div class="stat-label-animate text-sm text-gray-600 font-medium">After many hours finally fix almost all of them.</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Testing Section -->
        <section id="testing" class="bg-white rounded-2xl p-4 sm:p-6 lg:p-8 mb-8 sm:mb-12 shadow-lg hover-lift animate-slide-in">
            <!-- Text-Fitted Box Reveal Section Header -->
            <div class="mb-12">
                <!-- Phase indicator with individual reveal -->
                <div class="flex items-center gap-3 mb-4">
                    <div class="search-icon-blur w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold text-sm">T</span>
                    </div>
                    <div class="text-reveal">
                        <span class="text-reveal-content slide-up text-sm font-medium text-purple-600 uppercase tracking-wide">Phase 03</span>
                        <div class="text-reveal-overlay"></div>
                    </div>
                </div>

                <!-- Title with individual reveal -->
                <div class="block-text-reveal mb-3">
                    <h2 class="text-reveal-content slide-up text-2xl sm:text-3xl font-bold text-gray-900">Testing & Refinement</h2>
                    <div class="text-reveal-overlay"></div>
                </div>

                <!-- Description with individual reveal -->
                <div class="block-text-reveal">
                    <p class="text-reveal-content slide-up text-lg sm:text-xl font-medium text-gray-700 leading-relaxed max-w-3xl">Phase 3 is where I completed my E-commerce SaaS type landing page after adding more styles, animations, micro interactions, small details, more JavaScript and SVG icons. I finally finished it! This was quite a journey for me and an experience.</p>
                    <div class="text-reveal-overlay"></div>
                </div>
            </div>

            <!-- Questions I Had to Answer -->
            <div class="questions-card-animate bg-white border border-gray-200 rounded-2xl p-8 mb-8">
                <!-- Header with icon - Centered -->
                <div class="flex items-center justify-center gap-3 mb-8">
                    <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900">Questions I Had to Answer</h3>
                </div>

                <!-- Questions list -->
                <div class="space-y-6">
                    <!-- Question 1 -->
                    <div class="questions-item-animate flex items-start gap-4 group hover:bg-gray-50 p-4 rounded-lg transition-all duration-300 cursor-pointer">
                        <div class="w-10 h-10 bg-blue-50 rounded-lg flex items-center justify-center flex-shrink-0 mt-1 group-hover:bg-blue-100 group-hover:scale-110 transition-all duration-300">
                            <svg class="w-5 h-5 text-blue-600 group-hover:text-blue-700 transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <h4 class="text-lg font-semibold text-gray-900 mb-2 group-hover:text-blue-700 transition-colors duration-300">What do I do now after completing my project?</h4>
                            <p class="text-gray-600 text-sm leading-relaxed group-hover:text-gray-700 transition-colors duration-300">I will probably add this as one of my best portfolio HTML, CSS and Tailwind and JavaScript landing page. It's quite a journey for me and an experience.</p>
                        </div>
                    </div>

                    <!-- Question 2 -->
                    <div class="questions-item-animate flex items-start gap-4 group hover:bg-gray-50 p-4 rounded-lg transition-all duration-300 cursor-pointer">
                        <div class="w-10 h-10 bg-green-50 rounded-lg flex items-center justify-center flex-shrink-0 mt-1 group-hover:bg-green-100 group-hover:scale-110 transition-all duration-300">
                            <svg class="w-5 h-5 text-green-600 group-hover:text-green-700 transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"/>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <h4 class="text-lg font-semibold text-gray-900 mb-2 group-hover:text-green-700 transition-colors duration-300">Should I add a backend for this?</h4>
                            <p class="text-gray-600 text-sm leading-relaxed group-hover:text-gray-700 transition-colors duration-300">Without a second thought yes I'll do it to gain more knowledge on APIs library, to explore functionality systems around the internet and possibly increase my chance on getting hired too in the future.</p>
                        </div>
                    </div>

                    <!-- Question 3 -->
                    <div class="questions-item-animate flex items-start gap-4 group hover:bg-gray-50 p-4 rounded-lg transition-all duration-300 cursor-pointer">
                        <div class="w-10 h-10 bg-yellow-50 rounded-lg flex items-center justify-center flex-shrink-0 mt-1 group-hover:bg-yellow-100 group-hover:scale-110 transition-all duration-300">
                            <svg class="w-5 h-5 text-yellow-600 group-hover:text-yellow-700 transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <h4 class="text-lg font-semibold text-gray-900 mb-2 group-hover:text-yellow-700 transition-colors duration-300">Would I sell this website?</h4>
                            <p class="text-gray-600 text-sm leading-relaxed group-hover:text-gray-700 transition-colors duration-300">Absolutely! If anyone is interested contact me we can talk things out if you're only interested!</p>
                        </div>
                    </div>

                    <!-- Question 4 -->
                    <div class="questions-item-animate flex items-start gap-4 group hover:bg-gray-50 p-4 rounded-lg transition-all duration-300 cursor-pointer">
                        <div class="w-10 h-10 bg-purple-50 rounded-lg flex items-center justify-center flex-shrink-0 mt-1 group-hover:bg-purple-100 group-hover:scale-110 transition-all duration-300">
                            <svg class="w-5 h-5 text-purple-600 group-hover:text-purple-700 transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <h4 class="text-lg font-semibold text-gray-900 mb-2 group-hover:text-purple-700 transition-colors duration-300">Why did I create this SaaS type landing page?</h4>
                            <p class="text-gray-600 text-sm leading-relaxed group-hover:text-gray-700 transition-colors duration-300">Honestly? I was getting tired of seeing the same design patterns everywhere. I wanted to try something that felt more modern and clean - like the SaaS tools I actually use. Plus, I thought it would be a fun challenge to see if I could make an e-commerce site that didn't look like every other online store out there!</p>
                        </div>
                    </div>

                    <!-- Question 5 -->
                    <div class="questions-item-animate flex items-start gap-4 group hover:bg-gray-50 p-4 rounded-lg transition-all duration-300 cursor-pointer">
                        <div class="w-10 h-10 bg-red-50 rounded-lg flex items-center justify-center flex-shrink-0 mt-1 group-hover:bg-red-100 group-hover:scale-110 transition-all duration-300">
                            <svg class="w-5 h-5 text-red-600 group-hover:text-red-700 transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"/>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <h4 class="text-lg font-semibold text-gray-900 mb-2 group-hover:text-red-700 transition-colors duration-300">Will I create different backend and front end web apps?</h4>
                            <p class="text-gray-600 text-sm leading-relaxed group-hover:text-gray-700 transition-colors duration-300">Yes to gain more knowledge and skills along the way and to make my skills more flexible :D</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Challenges and Learnings Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6 md:gap-8 mb-6 sm:mb-8">
                <!-- Challenges I Faced -->
                <div class="challenges-learnings-card bg-red-50 border border-red-200 rounded-2xl p-8">
                    <!-- Header with icon -->
                    <div class="flex items-center gap-3 mb-6">
                        <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-red-800">Challenges I Faced</h3>
                    </div>

                    <!-- Challenges list -->
                    <div class="space-y-4">
                        <!-- Challenge 1 -->
                        <div class="questions-item-animate flex items-start gap-3 group hover:bg-red-100 p-3 rounded-lg transition-all duration-300 cursor-pointer">
                            <div class="w-6 h-6 bg-red-200 rounded-lg flex items-center justify-center flex-shrink-0 mt-0.5 group-hover:bg-red-300 group-hover:scale-110 transition-all duration-300">
                                <svg class="w-3 h-3 text-red-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <h4 class="font-semibold text-red-800 mb-1 group-hover:text-red-900 transition-colors duration-300">Overthinking simple problems</h4>
                                <p class="text-red-600 text-sm leading-relaxed group-hover:text-red-700 transition-colors duration-300">I'd spend hours on complex solutions when the answer was usually something basic I missed</p>
                            </div>
                        </div>

                        <!-- Challenge 2 -->
                        <div class="questions-item-animate flex items-start gap-3 group hover:bg-red-100 p-3 rounded-lg transition-all duration-300 cursor-pointer">
                            <div class="w-6 h-6 bg-red-200 rounded-lg flex items-center justify-center flex-shrink-0 mt-0.5 group-hover:bg-red-300 group-hover:scale-110 transition-all duration-300">
                                <svg class="w-3 h-3 text-red-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <h4 class="font-semibold text-red-800 mb-1 group-hover:text-red-900 transition-colors duration-300">Messy code organization</h4>
                                <p class="text-red-600 text-sm leading-relaxed group-hover:text-red-700 transition-colors duration-300">My files became a nightmare to navigate - everything mixed together with no clear structure</p>
                            </div>
                        </div>

                        <!-- Challenge 3 -->
                        <div class="questions-item-animate flex items-start gap-3 group hover:bg-red-100 p-3 rounded-lg transition-all duration-300 cursor-pointer">
                            <div class="w-6 h-6 bg-red-200 rounded-lg flex items-center justify-center flex-shrink-0 mt-0.5 group-hover:bg-red-300 group-hover:scale-110 transition-all duration-300">
                                <svg class="w-3 h-3 text-red-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <h4 class="font-semibold text-red-800 mb-1 group-hover:text-red-900 transition-colors duration-300">Perfectionism paralysis</h4>
                                <p class="text-red-600 text-sm leading-relaxed group-hover:text-red-700 transition-colors duration-300">I kept tweaking tiny details instead of moving forward - sometimes good enough is actually good enough</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- What I Learned -->
                <div class="challenges-learnings-card bg-green-50 border border-green-200 rounded-2xl p-8">
                    <!-- Header with icon -->
                    <div class="flex items-center gap-3 mb-6">
                        <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-green-800">What I Learned</h3>
                    </div>

                    <!-- Learnings list -->
                    <div class="space-y-4">
                        <!-- Learning 1 -->
                        <div class="questions-item-animate flex items-start gap-3 group hover:bg-green-100 p-3 rounded-lg transition-all duration-300 cursor-pointer">
                            <div class="w-6 h-6 bg-green-200 rounded-lg flex items-center justify-center flex-shrink-0 mt-0.5 group-hover:bg-green-300 group-hover:scale-110 transition-all duration-300">
                                <svg class="w-3 h-3 text-green-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <h4 class="font-semibold text-green-800 mb-1 group-hover:text-green-900 transition-colors duration-300">Turn large codebase into thin slices</h4>
                                <p class="text-green-600 text-sm leading-relaxed group-hover:text-green-700 transition-colors duration-300">Rather than a single file that has CSS and VanillaJS on it i learn that the best way is to create seperate files with their own functions</p>
                            </div>
                        </div>

                        <!-- Learning 2 -->
                        <div class="questions-item-animate flex items-start gap-3 group hover:bg-green-100 p-3 rounded-lg transition-all duration-300 cursor-pointer">
                            <div class="w-6 h-6 bg-green-200 rounded-lg flex items-center justify-center flex-shrink-0 mt-0.5 group-hover:bg-green-300 group-hover:scale-110 transition-all duration-300">
                                <svg class="w-3 h-3 text-green-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <h4 class="font-semibold text-green-800 mb-1 group-hover:text-green-900 transition-colors duration-300">Small wins build confidence</h4>
                                <p class="text-green-600 text-sm leading-relaxed group-hover:text-green-700 transition-colors duration-300">Every time I fixed a bug or got an animation working, it felt amazing and kept me motivated to tackle the next challenge</p>
                            </div>
                        </div>

                        <!-- Learning 3 -->
                        <div class="questions-item-animate flex items-start gap-3 group hover:bg-green-100 p-3 rounded-lg transition-all duration-300 cursor-pointer">
                            <div class="w-6 h-6 bg-green-200 rounded-lg flex items-center justify-center flex-shrink-0 mt-0.5 group-hover:bg-green-300 group-hover:scale-110 transition-all duration-300">
                                <svg class="w-3 h-3 text-green-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <h4 class="font-semibold text-green-800 mb-1 group-hover:text-green-900 transition-colors duration-300">Documentation is my best friend</h4>
                                <p class="text-green-600 text-sm leading-relaxed group-hover:text-green-700 transition-colors duration-300">I spent more time reading MDN docs and Stack Overflow than actually coding, but it saved me from so many headaches later</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Experience Level Note -->
            <div class="mb-6">
                <div class="experience-level-card bg-amber-50 p-6 rounded-lg border border-amber-200">
                    <div class="flex items-start gap-3">
                        <div class="w-8 h-8 bg-amber-100 rounded-lg flex items-center justify-center mt-1">
                            <svg class="w-5 h-5 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-semibold text-amber-700 mb-2">My Current Experience Level</h4>
                            <p class="text-sm text-amber-600 leading-relaxed">For this project, I learned some APIs along the way like the Intersection Observer API for the scroll effects - it was amazing discovering how to trigger animations when elements come into view! I also used the Web APIs for DOM manipulation and event handling. While I haven't touched backend development yet, I focused on front-end technologies: HTML, CSS, Tailwind CSS, and Vanilla JavaScript. As a college student with 2.5 years of experience in these frameworks, this landing page represents my current skill level in front-end development.</p>
                        </div>
                    </div>
                </div>
            </div>



            <!-- Final breakthrough -->
            <div class="bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 rounded-2xl p-8 mb-8 breakthrough-moment-enhanced">
                <div class="text-center mb-8">
                    <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg icon-progressive-reveal icon-container-enhanced" data-icon-animation="breakthrough-moment">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                        </svg>
                    </div>
                    <h3 class="text-2xl sm:text-3xl font-bold text-gray-900 mb-2 title-animated" data-title-animation="breakthrough-moment">The "Wait, This Actually Works!" Moment</h3>
                </div>

                <div class="bg-white border border-blue-200 rounded-xl p-6 relative overflow-hidden quote-container-enhanced quote-card-animated" data-card-animation="breakthrough-moment">
                    <div class="absolute top-0 left-0 w-2 h-full bg-gradient-to-b from-blue-500 to-blue-600"></div>
                    <div class="flex items-start gap-4">
                        <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center flex-shrink-0">
                            <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z"/>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <div class="text-lg text-gray-800 leading-relaxed mb-4 text-animation-container" data-text-animation="breakthrough-moment">
                                <span class="word-slide-up">I</span> <span class="word-slide-up">spent</span> <span class="word-slide-up">hours</span> <span class="word-slide-up">making</span> <span class="word-slide-up">sure</span> <span class="word-slide-up">everything</span>
                                <span class="font-semibold text-blue-600 bg-blue-50 px-2 py-1 rounded highlight-reveal">worked properly</span><span class="word-slide-up">,</span> <span class="word-slide-up">testing</span> <span class="word-slide-up">on</span> <span class="word-slide-up">different</span>
                                <span class="font-semibold text-blue-600 bg-blue-50 px-2 py-1 rounded highlight-reveal">browsers and devices</span><span class="word-slide-up">,</span> <span class="word-slide-up">and</span> <span class="word-slide-up">fixing</span> <span class="word-slide-up">all</span> <span class="word-slide-up">the</span>
                                <span class="font-semibold text-blue-600 bg-blue-50 px-2 py-1 rounded highlight-reveal">little details</span> <span class="word-slide-up">that</span> <span class="word-slide-up">bugged</span> <span class="word-slide-up">me.</span>
                            </div>
                            <div class="text-sm font-medium text-blue-600 uppercase tracking-wider blur-in">MY TESTING APPROACH</div>
                        </div>
                    </div>
                </div>

                <!-- Modern Skills Mastery Cards -->
                <div class="mt-6 sm:mt-8 grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                    <!-- Technical Skills Card -->
                    <div class="bg-white border border-indigo-200 rounded-2xl p-6 hover:shadow-lg transition-all duration-300 achievement-card" data-card="technical-skills">
                        <div class="flex items-center justify-center gap-3 mb-6 achievement-card-content" data-content="technical-skills-header">
                            <div class="w-10 h-10 bg-indigo-100 rounded-xl flex items-center justify-center">
                                <svg class="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"/>
                                </svg>
                            </div>
                            <h4 class="text-lg font-semibold text-gray-900">Technical Skills</h4>
                        </div>
                        <div class="space-y-4 achievement-card-content" data-content="technical-skills-items">
                            <div class="flex items-start gap-3 p-3 rounded-lg hover:bg-indigo-50 transition-all duration-300 cursor-pointer group achievement-item" data-item="tech-skill-1">
                                <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center mt-0.5 flex-shrink-0 group-hover:bg-indigo-200 group-hover:scale-110 transition-all duration-300">
                                    <svg class="w-4 h-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-sm font-medium text-gray-900 mb-1 group-hover:text-indigo-600 transition-colors duration-300">Advanced CSS animations & transitions</h3>
                                    <p class="text-xs text-gray-600 leading-relaxed">Smooth micro-interactions and polished effects</p>
                                </div>
                            </div>
                            <div class="flex items-start gap-3 p-3 rounded-lg hover:bg-indigo-50 transition-all duration-300 cursor-pointer group achievement-item" data-item="tech-skill-2">
                                <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center mt-0.5 flex-shrink-0 group-hover:bg-indigo-200 group-hover:scale-110 transition-all duration-300">
                                    <svg class="w-4 h-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-sm font-medium text-gray-900 mb-1 group-hover:text-indigo-600 transition-colors duration-300">Responsive design</h3>
                                    <p class="text-xs text-gray-600 leading-relaxed">Mobile-first approach with fluid layouts</p>
                                </div>
                            </div>
                            <div class="flex items-start gap-3 p-3 rounded-lg hover:bg-indigo-50 transition-all duration-300 cursor-pointer group achievement-item" data-item="tech-skill-3">
                                <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center mt-0.5 flex-shrink-0 group-hover:bg-indigo-200 group-hover:scale-110 transition-all duration-300">
                                    <svg class="w-4 h-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-sm font-medium text-gray-900 mb-1 group-hover:text-indigo-600 transition-colors duration-300">Event delegation & DOM manipulation</h3>
                                    <p class="text-xs text-gray-600 leading-relaxed">Dynamic interactions and element control</p>
                                </div>
                            </div>
                            <div class="flex items-start gap-3 p-3 rounded-lg hover:bg-indigo-50 transition-all duration-300 cursor-pointer group achievement-item" data-item="tech-skill-4">
                                <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center mt-0.5 flex-shrink-0 group-hover:bg-indigo-200 group-hover:scale-110 transition-all duration-300">
                                    <svg class="w-4 h-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-sm font-medium text-gray-900 mb-1 group-hover:text-indigo-600 transition-colors duration-300">Tailwind CSS mastery</h3>
                                    <p class="text-xs text-gray-600 leading-relaxed">Utility-first styling with custom components</p>
                                </div>
                            </div>
                            <div class="flex items-start gap-3 p-3 rounded-lg hover:bg-indigo-50 transition-all duration-300 cursor-pointer group achievement-item" data-item="tech-skill-5">
                                <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center mt-0.5 flex-shrink-0 group-hover:bg-indigo-200 group-hover:scale-110 transition-all duration-300">
                                    <svg class="w-4 h-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-sm font-medium text-gray-900 mb-1 group-hover:text-indigo-600 transition-colors duration-300">Progressive enhancement patterns</h3>
                                    <p class="text-xs text-gray-600 leading-relaxed">Building robust, accessible experiences</p>
                                </div>
                            </div>
                            <div class="flex items-start gap-3 p-3 rounded-lg hover:bg-indigo-50 transition-all duration-300 cursor-pointer group achievement-item" data-item="tech-skill-6">
                                <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center mt-0.5 flex-shrink-0 group-hover:bg-indigo-200 group-hover:scale-110 transition-all duration-300">
                                    <svg class="w-4 h-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-sm font-medium text-gray-900 mb-1 group-hover:text-indigo-600 transition-colors duration-300">Performance optimization</h3>
                                    <p class="text-xs text-gray-600 leading-relaxed">Fast loading and smooth interactions</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Soft Skills Card -->
                    <div class="bg-white border border-rose-200 rounded-2xl p-6 hover:shadow-lg transition-all duration-300 achievement-card" data-card="soft-skills">
                        <div class="flex items-center justify-center gap-3 mb-6 achievement-card-content" data-content="soft-skills-header">
                            <div class="w-10 h-10 bg-rose-100 rounded-xl flex items-center justify-center">
                                <svg class="w-5 h-5 text-rose-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                                </svg>
                            </div>
                            <h4 class="text-lg font-semibold text-gray-900">Soft Skills</h4>
                        </div>
                        <div class="space-y-4 achievement-card-content" data-content="soft-skills-items">
                            <div class="flex items-start gap-3 p-3 rounded-lg hover:bg-rose-50 transition-all duration-300 cursor-pointer group achievement-item" data-item="soft-skill-1">
                                <div class="w-8 h-8 bg-rose-100 rounded-lg flex items-center justify-center mt-0.5 flex-shrink-0 group-hover:bg-rose-200 group-hover:scale-110 transition-all duration-300">
                                    <svg class="w-4 h-4 text-rose-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-sm font-medium text-gray-900 mb-1 group-hover:text-rose-600 transition-colors duration-300">Debugging complex animation issues</h3>
                                    <p class="text-xs text-gray-600 leading-relaxed">Persistent troubleshooting and problem isolation</p>
                                </div>
                            </div>
                            <div class="flex items-start gap-3 p-3 rounded-lg hover:bg-rose-50 transition-all duration-300 cursor-pointer group achievement-item" data-item="soft-skill-2">
                                <div class="w-8 h-8 bg-rose-100 rounded-lg flex items-center justify-center mt-0.5 flex-shrink-0 group-hover:bg-rose-200 group-hover:scale-110 transition-all duration-300">
                                    <svg class="w-4 h-4 text-rose-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-sm font-medium text-gray-900 mb-1 group-hover:text-rose-600 transition-colors duration-300">Cross-browser compatibility testing</h3>
                                    <p class="text-xs text-gray-600 leading-relaxed">Ensuring consistent experience across platforms</p>
                                </div>
                            </div>
                            <div class="flex items-start gap-3 p-3 rounded-lg hover:bg-rose-50 transition-all duration-300 cursor-pointer group achievement-item" data-item="soft-skill-3">
                                <div class="w-8 h-8 bg-rose-100 rounded-lg flex items-center justify-center mt-0.5 flex-shrink-0 group-hover:bg-rose-200 group-hover:scale-110 transition-all duration-300">
                                    <svg class="w-4 h-4 text-rose-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-sm font-medium text-gray-900 mb-1 group-hover:text-rose-600 transition-colors duration-300">User experience thinking</h3>
                                    <p class="text-xs text-gray-600 leading-relaxed">Designing with empathy and user needs in mind</p>
                                </div>
                            </div>
                            <div class="flex items-start gap-3 p-3 rounded-lg hover:bg-rose-50 transition-all duration-300 cursor-pointer group achievement-item" data-item="soft-skill-4">
                                <div class="w-8 h-8 bg-rose-100 rounded-lg flex items-center justify-center mt-0.5 flex-shrink-0 group-hover:bg-rose-200 group-hover:scale-110 transition-all duration-300">
                                    <svg class="w-4 h-4 text-rose-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-sm font-medium text-gray-900 mb-1 group-hover:text-rose-600 transition-colors duration-300">Code organization & structure</h3>
                                    <p class="text-xs text-gray-600 leading-relaxed">Building maintainable and scalable solutions</p>
                                </div>
                            </div>
                            <div class="flex items-start gap-3 p-3 rounded-lg hover:bg-rose-50 transition-all duration-300 cursor-pointer group achievement-item" data-item="soft-skill-5">
                                <div class="w-8 h-8 bg-rose-100 rounded-lg flex items-center justify-center mt-0.5 flex-shrink-0 group-hover:bg-rose-200 group-hover:scale-110 transition-all duration-300">
                                    <svg class="w-4 h-4 text-rose-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-sm font-medium text-gray-900 mb-1 group-hover:text-rose-600 transition-colors duration-300">Problem-solving persistence</h3>
                                    <p class="text-xs text-gray-600 leading-relaxed">Never giving up until finding the right solution</p>
                                </div>
                            </div>
                            <div class="flex items-start gap-3 p-3 rounded-lg hover:bg-rose-50 transition-all duration-300 cursor-pointer group achievement-item" data-item="soft-skill-6">
                                <div class="w-8 h-8 bg-rose-100 rounded-lg flex items-center justify-center mt-0.5 flex-shrink-0 group-hover:bg-rose-200 group-hover:scale-110 transition-all duration-300">
                                    <svg class="w-4 h-4 text-rose-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-sm font-medium text-gray-900 mb-1 group-hover:text-rose-600 transition-colors duration-300">Coffee tolerance (expert level)</h3>
                                    <p class="text-xs text-gray-600 leading-relaxed">Essential fuel for late-night coding sessions</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Progress metrics -->
            <div class="pt-4 metrics-container">
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
                    <!-- Phase 3 Duration Stat -->
                    <div class="stat-card-animate bg-white border border-gray-200 rounded-2xl p-6 hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                        <div class="flex items-center gap-4 mb-4">
                            <div class="stat-icon-animate w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
                                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <div class="stat-number-animate text-2xl font-bold text-blue-600 mb-1">36-66 days</div>
                                <div class="stat-label-animate text-sm text-gray-600 font-medium">Phase 3 Duration</div>
                            </div>
                        </div>
                    </div>

                    <!-- Combined Code Lines Stat -->
                    <div class="stat-card-animate bg-white border border-gray-200 rounded-2xl p-6 hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                        <div class="flex items-center gap-4 mb-4">
                            <div class="stat-icon-animate w-10 h-10 bg-purple-100 rounded-xl flex items-center justify-center">
                                <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"/>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <div class="stat-number-animate text-2xl font-bold text-purple-600 mb-1">6,959 lines</div>
                                <div class="stat-label-animate text-sm text-gray-600 font-medium">(HTML, CSS AND TAILWIND, JS)</div>
                            </div>
                        </div>
                    </div>

                    <!-- Files Stat -->
                    <div class="stat-card-animate bg-white border border-gray-200 rounded-2xl p-6 hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                        <div class="flex items-center gap-4 mb-4">
                            <div class="stat-icon-animate w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center">
                                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <div class="stat-number-animate text-2xl font-bold text-green-600 mb-1">3 files</div>
                                <div class="stat-label-animate text-sm text-gray-600 font-medium">Separate files for easy debugging</div>
                            </div>
                        </div>
                    </div>

                    <!-- Learning Hours Stat -->
                    <div class="stat-card-animate bg-white border border-gray-200 rounded-2xl p-6 hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                        <div class="flex items-center gap-4 mb-4">
                            <div class="stat-icon-animate w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
                                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <div class="stat-number-animate text-2xl font-bold text-blue-600 mb-1">240+</div>
                                <div class="stat-label-animate text-sm text-gray-600 font-medium">Learning hours</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Enhanced Reflection Section -->
        <section id="reflection" class="relative overflow-hidden bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 rounded-3xl p-12 mb-12 border border-blue-200/50 shadow-2xl">


            <!-- Header with Animated Text -->
            <div class="relative z-10 text-center mb-16">
                <div class="inline-flex items-center gap-3 mb-6 animate-fade-in">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                        </svg>
                    </div>
                    <span class="text-sm font-semibold text-blue-600 uppercase tracking-wider">Lessons Learned</span>
                </div>
                <h2 class="text-3xl sm:text-4xl md:text-5xl font-bold text-gray-900 mb-4 animate-slide-up">
                    What I'd Tell My
                    <span class="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent">
                        Past Self
                    </span>
                </h2>
                <p class="text-base sm:text-lg text-gray-600 max-w-2xl mx-auto animate-slide-up delay-200">
                    Hard-earned wisdom from countless hours of debugging, learning, and growing as a developer
                </p>
            </div>

            <!-- Enhanced Two-Column Layout -->
            <div class="relative z-10 grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 max-w-6xl mx-auto px-4 sm:px-6">

                <!-- Don't Do This Column -->
                <div class="space-y-6 animate-slide-left">
                    <!-- Column Header -->
                    <div class="text-center mb-8">
                        <div class="inline-flex items-center gap-3 bg-red-50 border border-red-200 rounded-2xl px-6 py-3 mb-4">
                            <div class="w-8 h-8 bg-red-100 rounded-xl flex items-center justify-center">
                                <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                </svg>
                            </div>
                            <h3 class="text-xl font-bold text-red-700">Don't Do This</h3>
                        </div>
                        <p class="text-sm text-red-600">Mistakes that cost me time and sanity</p>
                    </div>

                    <!-- Don't Items -->
                    <div class="space-y-4">
                        <div class="group bg-white/80 backdrop-blur-sm border border-red-200/50 rounded-2xl p-4 sm:p-6 hover:shadow-lg hover:-translate-y-1 transition-all duration-300 cursor-pointer reflection-item" data-item="dont-1">
                            <div class="flex items-start gap-4">
                                <div class="w-10 h-10 bg-red-100 rounded-xl flex items-center justify-center flex-shrink-0 group-hover:bg-red-200 group-hover:scale-110 transition-all duration-300">
                                    <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h4 class="font-semibold text-gray-900 mb-2 group-hover:text-red-600 transition-colors duration-300">Open 47 browser tabs</h4>
                                    <p class="text-sm text-gray-600 leading-relaxed">Your laptop will hate you, and you'll lose track of everything important</p>
                                </div>
                            </div>
                        </div>

                        <div class="group bg-white/80 backdrop-blur-sm border border-red-200/50 rounded-2xl p-6 hover:shadow-lg hover:-translate-y-1 transition-all duration-300 cursor-pointer reflection-item" data-item="dont-2">
                            <div class="flex items-start gap-4">
                                <div class="w-10 h-10 bg-red-100 rounded-xl flex items-center justify-center flex-shrink-0 group-hover:bg-red-200 group-hover:scale-110 transition-all duration-300">
                                    <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h4 class="font-semibold text-gray-900 mb-2 group-hover:text-red-600 transition-colors duration-300">Try to build everything perfectly from day one</h4>
                                    <p class="text-sm text-gray-600 leading-relaxed">Perfectionism is the enemy of progress and learning</p>
                                </div>
                            </div>
                        </div>

                        <div class="group bg-white/80 backdrop-blur-sm border border-red-200/50 rounded-2xl p-6 hover:shadow-lg hover:-translate-y-1 transition-all duration-300 cursor-pointer reflection-item" data-item="dont-3">
                            <div class="flex items-start gap-4">
                                <div class="w-10 h-10 bg-red-100 rounded-xl flex items-center justify-center flex-shrink-0 group-hover:bg-red-200 group-hover:scale-110 transition-all duration-300">
                                    <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h4 class="font-semibold text-gray-900 mb-2 group-hover:text-red-600 transition-colors duration-300">Compare your week 1 code to senior developers</h4>
                                    <p class="text-sm text-gray-600 leading-relaxed">Everyone starts somewhere - focus on your own growth journey</p>
                                </div>
                            </div>
                        </div>

                        <div class="group bg-white/80 backdrop-blur-sm border border-red-200/50 rounded-2xl p-6 hover:shadow-lg hover:-translate-y-1 transition-all duration-300 cursor-pointer reflection-item" data-item="dont-4">
                            <div class="flex items-start gap-4">
                                <div class="w-10 h-10 bg-red-100 rounded-xl flex items-center justify-center flex-shrink-0 group-hover:bg-red-200 group-hover:scale-110 transition-all duration-300">
                                    <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h4 class="font-semibold text-gray-900 mb-2 group-hover:text-red-600 transition-colors duration-300">Skip the basics (CSS flexbox is your friend)</h4>
                                    <p class="text-sm text-gray-600 leading-relaxed">Strong fundamentals save you from countless headaches later</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Definitely Do This Column -->
                <div class="space-y-6 animate-slide-right">
                    <!-- Column Header -->
                    <div class="text-center mb-8">
                        <div class="inline-flex items-center gap-3 bg-green-50 border border-green-200 rounded-2xl px-6 py-3 mb-4">
                            <div class="w-8 h-8 bg-green-100 rounded-xl flex items-center justify-center">
                                <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                </svg>
                            </div>
                            <h3 class="text-xl font-bold text-green-700">Definitely Do This</h3>
                        </div>
                        <p class="text-sm text-green-600">Strategies that accelerated my growth</p>
                    </div>

                    <!-- Do Items -->
                    <div class="space-y-4">
                        <div class="group bg-white/80 backdrop-blur-sm border border-green-200/50 rounded-2xl p-4 sm:p-6 hover:shadow-lg hover:-translate-y-1 transition-all duration-300 cursor-pointer reflection-item" data-item="do-1">
                            <div class="flex items-start gap-4">
                                <div class="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center flex-shrink-0 group-hover:bg-green-200 group-hover:scale-110 transition-all duration-300">
                                    <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h4 class="font-semibold text-gray-900 mb-2 group-hover:text-green-600 transition-colors duration-300">Start building something real, even if it's ugly</h4>
                                    <p class="text-sm text-gray-600 leading-relaxed">Real projects teach you more than tutorials ever will</p>
                                </div>
                            </div>
                        </div>

                        <div class="group bg-white/80 backdrop-blur-sm border border-green-200/50 rounded-2xl p-6 hover:shadow-lg hover:-translate-y-1 transition-all duration-300 cursor-pointer reflection-item" data-item="do-2">
                            <div class="flex items-start gap-4">
                                <div class="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center flex-shrink-0 group-hover:bg-green-200 group-hover:scale-110 transition-all duration-300">
                                    <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h4 class="font-semibold text-gray-900 mb-2 group-hover:text-green-600 transition-colors duration-300">Break things and fix them (best way to learn)</h4>
                                    <p class="text-sm text-gray-600 leading-relaxed">Every bug you fix makes you a stronger developer</p>
                                </div>
                            </div>
                        </div>

                        <div class="group bg-white/80 backdrop-blur-sm border border-green-200/50 rounded-2xl p-6 hover:shadow-lg hover:-translate-y-1 transition-all duration-300 cursor-pointer reflection-item" data-item="do-3">
                            <div class="flex items-start gap-4">
                                <div class="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center flex-shrink-0 group-hover:bg-green-200 group-hover:scale-110 transition-all duration-300">
                                    <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h4 class="font-semibold text-gray-900 mb-2 group-hover:text-green-600 transition-colors duration-300">Get real user feedback early and often</h4>
                                    <p class="text-sm text-gray-600 leading-relaxed">Users will surprise you with how they actually use your app</p>
                                </div>
                            </div>
                        </div>

                        <div class="group bg-white/80 backdrop-blur-sm border border-green-200/50 rounded-2xl p-6 hover:shadow-lg hover:-translate-y-1 transition-all duration-300 cursor-pointer reflection-item" data-item="do-4">
                            <div class="flex items-start gap-4">
                                <div class="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center flex-shrink-0 group-hover:bg-green-200 group-hover:scale-110 transition-all duration-300">
                                    <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h4 class="font-semibold text-gray-900 mb-2 group-hover:text-green-600 transition-colors duration-300">Celebrate small wins (they add up!)</h4>
                                    <p class="text-sm text-gray-600 leading-relaxed">Every line of working code is progress worth acknowledging</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


        </section>
    </div>

    <script>
        // Navigation sections array
        const sections = ['hero', 'research', 'building', 'testing', 'reflection'];
        let currentSection = 0;

        // Interactive functionality
        function toggleExpand(elementId) {
            const element = document.getElementById(elementId);
            element.classList.toggle('expanded');
        }

        // Animate progress bars on scroll
        function animateProgressBars() {
            const progressBars = document.querySelectorAll('.progress-fill');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.width = entry.target.style.getPropertyValue('--progress-width') || entry.target.style.width;
                    }
                });
            });

            progressBars.forEach(bar => observer.observe(bar));
        }



        // Update mobile navigation
        function updateMobileNav() {
            const navDots = document.querySelectorAll('.nav-dot');
            navDots.forEach((dot, index) => {
                dot.classList.toggle('active', index === currentSection);
            });
        }

        // Scroll to section
        function scrollToSection(sectionId) {
            const section = document.getElementById(sectionId);
            if (section) {
                section.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        }

        // Handle section visibility
        function handleSectionVisibility() {
            const sectionElements = sections.map(id => document.getElementById(id));
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const sectionId = entry.target.id;
                        const sectionIndex = sections.indexOf(sectionId);
                        if (sectionIndex !== -1) {
                            currentSection = sectionIndex;
                            updateMobileNav();
                        }
                    }
                });
            }, {
                threshold: 0.3,
                rootMargin: '-20% 0px -20% 0px'
            });

            sectionElements.forEach(section => {
                if (section) observer.observe(section);
            });
        }

        // Initialize mobile navigation
        function initializeMobileNav() {
            const navDots = document.querySelectorAll('.nav-dot');
            navDots.forEach((dot, index) => {
                dot.addEventListener('click', () => {
                    currentSection = index;
                    scrollToSection(sections[index]);
                    updateMobileNav();
                });
            });
        }

        // Animate new struggle tracker progress bars
        function animateStruggleProgressBars() {
            const progressBars = document.querySelectorAll('.animated-progress-bar');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const progressBar = entry.target;
                        const targetWidth = progressBar.getAttribute('data-width');

                        // Add a small delay for staggered animation
                        const delay = Array.from(progressBars).indexOf(progressBar) * 200;

                        setTimeout(() => {
                            progressBar.style.width = targetWidth;
                        }, delay);

                        observer.unobserve(progressBar);
                    }
                });
            }, {
                threshold: 0.5,
                rootMargin: '0px 0px -50px 0px'
            });

            progressBars.forEach(bar => {
                observer.observe(bar);
            });
        }

        // Features slideshow animation
        function initFeaturesSlideshow() {
            const featureItems = document.querySelectorAll('.feature-item');
            let currentIndex = 0;
            let cycleCount = 0;
            let slideshowInterval;

            function activateFeature(index) {
                // Remove active state from all items
                featureItems.forEach(item => {
                    item.classList.remove('scale-110', 'shadow-2xl', '-translate-y-2', 'rotate-1');
                    const icon = item.querySelector('div div');
                    if (icon) {
                        icon.classList.remove('scale-125', 'rotate-12');
                    }
                });

                // Add active state to current item
                const currentItem = featureItems[index];
                if (currentItem) {
                    currentItem.classList.add('scale-110', 'shadow-2xl', '-translate-y-2', 'rotate-1');
                    const icon = currentItem.querySelector('div div');
                    if (icon) {
                        icon.classList.add('scale-125', 'rotate-12');
                    }
                }
            }

            function activateAllWithUniqueAnimations() {
                // Clear any existing interval
                if (slideshowInterval) {
                    clearInterval(slideshowInterval);
                }

                // Remove all active states first
                featureItems.forEach(item => {
                    item.classList.remove('scale-110', 'shadow-2xl', '-translate-y-2', 'rotate-1');
                    const icon = item.querySelector('div div');
                    if (icon) {
                        icon.classList.remove('scale-125', 'rotate-12');
                    }
                });

                // Apply fluid motion animations to each item with staggered timing
                featureItems.forEach((item, index) => {
                    setTimeout(() => {
                        // Add fluid motion class with unique animation delays
                        item.classList.add('feature-fluid-motion');
                        item.style.animationDelay = `${index * 0.3}s`;

                        // Add enhanced shadow for depth without geometric transforms
                        item.classList.add('shadow-2xl');

                        // Apply subtle glow effect based on color theme
                        const colorClasses = ['bg-blue-50', 'bg-red-50', 'bg-orange-50', 'bg-yellow-50', 'bg-purple-50', 'bg-pink-50'];
                        const glowClasses = ['glow-blue', 'glow-red', 'glow-orange', 'glow-yellow', 'glow-purple', 'glow-pink'];

                        if (item.classList.contains(colorClasses[index])) {
                            item.classList.add(glowClasses[index]);
                        }

                        // Keep icons static - no transformations applied to icons

                    }, index * 150); // Stagger each item by 150ms for wave effect
                });

                // Return to normal state after 2.5 seconds to allow 1.5 seconds for elegant exit
                setTimeout(() => {
                    returnToNormalState();
                }, 2500);
            }

            function returnToNormalState() {
                featureItems.forEach((item, index) => {
                    setTimeout(() => {
                        // Add soft landing transition class first
                        item.classList.add('feature-soft-landing');

                        // Remove fluid motion classes with a slight delay to allow transition to take effect
                        setTimeout(() => {
                            item.classList.remove(
                                'feature-fluid-motion',
                                'shadow-2xl',
                                'glow-blue', 'glow-red', 'glow-orange', 'glow-yellow', 'glow-purple', 'glow-pink'
                            );

                            // Reset animation delay
                            item.style.animationDelay = '';

                            // Remove the landing class after transition completes
                            setTimeout(() => {
                                item.classList.remove('feature-soft-landing');
                            }, 800); // Match the transition duration

                        }, 50); // Small delay to ensure smooth transition

                    }, index * 150); // Increased stagger timing for more elegant wave exit
                });
            }

            function startSlideshow() {
                slideshowInterval = setInterval(() => {
                    activateFeature(currentIndex);
                    currentIndex = (currentIndex + 1) % featureItems.length;

                    // Check if we completed a full cycle
                    if (currentIndex === 0) {
                        cycleCount++;
                        // After one complete cycle, activate all with unique animations
                        if (cycleCount === 1) {
                            setTimeout(() => {
                                activateAllWithUniqueAnimations();
                            }, 2000); // Wait for the last item to finish its 2-second display
                        }
                    }
                }, 2000); // 2 seconds duration
            }

            // Start the slideshow when the section comes into view
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        startSlideshow();
                        observer.unobserve(entry.target);
                    }
                });
            }, { threshold: 0.5 });

            const slideshowContainer = document.getElementById('features-slideshow');
            if (slideshowContainer) {
                observer.observe(slideshowContainer);
            }
        }

        // Animate code blocks on scroll
        function animateCodeBlocks() {
            const codeBlocks = document.querySelectorAll('.code-block-animate');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        // Add a slight delay for a more dramatic entrance
                        setTimeout(() => {
                            entry.target.classList.add('animate');
                        }, 200);
                        observer.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.2,
                rootMargin: '0px 0px -100px 0px'
            });

            codeBlocks.forEach(block => observer.observe(block));
        }

        // Initialize animations when page loads
        document.addEventListener('DOMContentLoaded', function() {
            animateProgressBars();
            animateStruggleProgressBars();
            initFeaturesSlideshow();
            handleSectionVisibility();
            initializeMobileNav();
            updateMobileNav();
            animateCodeBlocks();

            // Add staggered animation to cards
            const cards = document.querySelectorAll('.animate-slide-in');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.2}s`;
            });

            // Add interactive effects to windows browser
            document.querySelectorAll('.windows-tab').forEach(tab => {
                // Tab click simulation
                tab.addEventListener('click', function() {
                    // Remove active from all tabs
                    document.querySelectorAll('.windows-tab').forEach(t => t.classList.remove('active'));
                    // Add active to clicked tab
                    this.classList.add('active');
                });

                // Tab close button functionality
                const closeBtn = tab.querySelector('button');
                if (closeBtn) {
                    closeBtn.addEventListener('click', function(e) {
                        e.stopPropagation();
                        tab.style.transform = 'scale(0.95)';
                        tab.style.opacity = '0';
                        setTimeout(() => {
                            tab.style.display = 'none';
                        }, 200);
                    });
                }
            });

            // Navigation button interactions
            document.querySelectorAll('.nav-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1.05)';
                    }, 100);
                });
            });

            // Window control interactions
            document.querySelectorAll('.window-control').forEach(btn => {
                btn.addEventListener('click', function() {
                    this.style.transform = 'scale(0.9)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });

            // Address bar focus simulation
            const addressBar = document.querySelector('.address-bar');
            if (addressBar) {
                addressBar.addEventListener('click', function() {
                    this.style.borderColor = '#0078d4';
                    this.style.boxShadow = '0 0 0 1px #0078d4';
                    setTimeout(() => {
                        this.style.borderColor = '#d1d5db';
                        this.style.boxShadow = 'none';
                    }, 2000);
                });
            }

            // Function to trigger text reveal animations for a section
            function triggerTextRevealAnimations(section) {
                // Find all text reveal elements in this section
                const textReveals = section.querySelectorAll('.text-reveal, .block-text-reveal');

                // Trigger box reveal animations immediately (no delay)
                setTimeout(() => {
                    textReveals.forEach((reveal, index) => {
                        reveal.classList.add('animate-in');

                        // For each box reveal, trigger its text slide-up after the box animation completes
                        const slideUpText = reveal.querySelector('.text-reveal-content.slide-up');
                        if (slideUpText) {
                            setTimeout(() => {
                                slideUpText.classList.add('animate-in');
                            }, 810); // Wait for this specific box animation to complete (0.8s) + 10ms
                        }
                    });

                    // After all box reveals complete, trigger search icon blur-in animation
                    setTimeout(() => {
                        const searchIcon = section.querySelector('.search-icon-blur');
                        if (searchIcon) {
                            searchIcon.classList.add('animate-in');
                        }
                    }, 815); // Start search icon animation 15ms after box reveals complete
                }, 300);
            }

            // Individual Text Reveal Animation Observer with optimized settings
            const textRevealObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        triggerTextRevealAnimations(entry.target);
                        // Stop observing this section after animation is triggered
                        textRevealObserver.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.1, // Trigger when only 10% is visible (more responsive)
                rootMargin: '50px 0px -50px 0px' // Start animation 50px before element enters viewport
            });

            // Function to check if element is in viewport
            function isElementInViewport(element) {
                const rect = element.getBoundingClientRect();
                const windowHeight = window.innerHeight || document.documentElement.clientHeight;
                const windowWidth = window.innerWidth || document.documentElement.clientWidth;

                return (
                    rect.top >= -50 && // Allow 50px buffer above viewport
                    rect.left >= 0 &&
                    rect.bottom <= windowHeight + 50 && // Allow 50px buffer below viewport
                    rect.right <= windowWidth &&
                    rect.top < windowHeight * 0.9 // Trigger if top 90% of element is visible
                );
            }

            // Observe sections with text reveals and check for initially visible elements
            document.querySelectorAll('section').forEach(section => {
                if (section.querySelector('.text-reveal, .block-text-reveal')) {
                    // Check if section is already visible on page load
                    if (isElementInViewport(section)) {
                        // Trigger animation immediately for visible sections
                        setTimeout(() => {
                            triggerTextRevealAnimations(section);
                        }, 100); // Small delay to ensure DOM is ready
                    } else {
                        // Observe sections that are not initially visible
                        textRevealObserver.observe(section);
                    }
                }
            });

            // Intersection Observer for breakthrough moment animations
            const breakthroughObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const quotes = entry.target.querySelectorAll('.animated-quote');
                        quotes.forEach((quote, index) => {
                            setTimeout(() => {
                                quote.style.animationPlayState = 'running';
                            }, index * 300);
                        });
                    }
                });
            }, { threshold: 0.3 });

            // Observe breakthrough moments and initialize quote animations
            document.querySelectorAll('.breakthrough-moment-modern').forEach(moment => {
                breakthroughObserver.observe(moment);
                // Initialize quotes as paused
                moment.querySelectorAll('.animated-quote').forEach(quote => {
                    quote.style.animationPlayState = 'paused';
                });
            });

            // Physics-Based Animated Counter Function
            function animateCounter(element, target, duration = 2000) {
                const startNumber = 1;
                const endNumber = target;
                let startTime = null;

                element.classList.add('counter-animating');

                /**
                 * Easing function for a slow start, fast middle, and slow end (ease-in-out-cubic).
                 * This creates a more natural acceleration and deceleration.
                 */
                function easeInOutCubic(t) {
                    return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
                }

                /**
                 * The main animation loop function.
                 * It's called by requestAnimationFrame for smooth rendering.
                 */
                function animationLoop(currentTime) {
                    // Set the start time on the first frame
                    if (startTime === null) {
                        startTime = currentTime;
                    }

                    // Calculate the elapsed time and progress
                    const elapsedTime = currentTime - startTime;
                    let progress = elapsedTime / duration;

                    // Ensure progress doesn't exceed 1
                    if (progress > 1) {
                        progress = 1;
                    }

                    // Apply the easing function to the progress
                    const easedProgress = easeInOutCubic(progress);

                    // Calculate the current number based on the eased progress
                    const currentNumber = Math.floor(easedProgress * (endNumber - startNumber) + startNumber);

                    // Update the number on the screen
                    element.textContent = currentNumber;

                    // Continue the loop until the animation is complete
                    if (progress < 1) {
                        requestAnimationFrame(animationLoop);
                    } else {
                        // Ensure the final number is exactly the end number
                        element.textContent = endNumber;
                        element.classList.remove('counter-animating');
                    }
                }

                // Start the animation loop
                requestAnimationFrame(animationLoop);
            }

            // Intersection Observer for counters
            const counterObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const counters = entry.target.querySelectorAll('.animated-counter');
                        counters.forEach((counter, index) => {
                            const target = parseInt(counter.getAttribute('data-target'));
                            // No delay - start immediately
                            animateCounter(counter, target, 2500); // Longer duration for smoother physics
                        });
                        counterObserver.unobserve(entry.target); // Only animate once
                    }
                });
            }, { threshold: 0.3 }); // Lower threshold to trigger earlier

            // Observe metrics containers
            document.querySelectorAll('.metrics-container').forEach(container => {
                counterObserver.observe(container);
            });

            // Character Blur-In Animation Function
            function initializeBlurInTitle(element) {
                const text = element.textContent;
                element.innerHTML = '';

                // Split text into characters and wrap each in a span
                for (let i = 0; i < text.length; i++) {
                    const char = text[i];
                    const span = document.createElement('span');
                    span.className = char === ' ' ? 'blur-char space' : 'blur-char';
                    span.textContent = char === ' ' ? '\u00A0' : char; // Use non-breaking space
                    element.appendChild(span);
                }
            }

            function animateBlurInTitle(element) {
                const chars = element.querySelectorAll('.blur-char');
                chars.forEach((char, index) => {
                    setTimeout(() => {
                        char.classList.add('animate-in');
                    }, index * 75); // 75ms delay between each character
                });
            }

            // Initialize all blur-in titles
            document.querySelectorAll('.blur-in-title').forEach(title => {
                initializeBlurInTitle(title);
            });

            // Intersection Observer for blur-in titles
            const blurTitleObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        animateBlurInTitle(entry.target);
                        blurTitleObserver.unobserve(entry.target); // Only animate once
                    }
                });
            }, { threshold: 0.5 });

            // Observe all blur-in titles
            document.querySelectorAll('.blur-in-title').forEach(title => {
                blurTitleObserver.observe(title);
            });

            // Word Slide-Up Animation Function
            function initializeSlideUpText(element) {
                const text = element.textContent;
                const words = text.split(' ');
                element.innerHTML = '';

                // Split text into words and wrap each in a span
                words.forEach((word, index) => {
                    const span = document.createElement('span');
                    span.className = 'slide-word';
                    span.textContent = word;
                    element.appendChild(span);

                    // Add space after each word except the last one
                    if (index < words.length - 1) {
                        element.appendChild(document.createTextNode(' '));
                    }
                });
            }

            function animateSlideUpText(element) {
                const words = element.querySelectorAll('.slide-word');
                words.forEach((word, index) => {
                    setTimeout(() => {
                        word.classList.add('animate-in');
                    }, index * 100); // 100ms delay between each word
                });
            }

            // Initialize all slide-up texts
            document.querySelectorAll('.slide-up-text').forEach(text => {
                initializeSlideUpText(text);
            });

            // Intersection Observer for slide-up texts
            const slideTextObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        // Add a small delay after the title animation for subtitle text only
                        setTimeout(() => {
                            animateSlideUpText(entry.target);
                        }, 800); // Start after title animation completes
                        slideTextObserver.unobserve(entry.target); // Only animate once
                    }
                });
            }, { threshold: 0.3 });

            // Observe all slide-up texts
            document.querySelectorAll('.slide-up-text').forEach(text => {
                slideTextObserver.observe(text);
            });

            // ===== QUOTE ANIMATION =====
            function initializeQuoteAnimation() {
                const quoteContainers = document.querySelectorAll('.quote-container');

                quoteContainers.forEach(quoteContainer => {
                    const quoteText = quoteContainer.querySelector('.quote-text');
                    const quoteHighlights = quoteContainer.querySelectorAll('.quote-highlight');
                    const quoteAttribution = quoteContainer.querySelector('.quote-attribution');

                    const observerOptions = {
                        root: null,
                        rootMargin: '0px 0px -20% 0px',
                        threshold: 0.3
                    };

                    const observer = new IntersectionObserver((entries) => {
                        entries.forEach(entry => {
                            if (entry.isIntersecting) {
                                // Trigger container animation first
                                setTimeout(() => {
                                    quoteContainer.classList.add('animate');
                                }, 100);

                                // Trigger text animation
                                setTimeout(() => {
                                    if (quoteText) quoteText.classList.add('animate');
                                }, 300);

                                // Trigger highlight animations with much longer stagger
                                // Start highlights after main text has fully appeared (1000ms base delay)
                                quoteHighlights.forEach((highlight, index) => {
                                    setTimeout(() => {
                                        highlight.classList.add('animate');
                                    }, 1000 + (index * 500)); // Much longer delays: 1000ms, 1500ms, 2000ms
                                });

                                // Trigger attribution animation after all highlights are done
                                // Calculate delay: base delay + (number of highlights * delay between them) + extra buffer
                                const attributionDelay = 1000 + (quoteHighlights.length * 500) + 400;
                                setTimeout(() => {
                                    if (quoteAttribution) quoteAttribution.classList.add('animate');
                                }, attributionDelay);

                                // Stop observing after animation is triggered
                                observer.unobserve(entry.target);
                            }
                        });
                    }, observerOptions);

                    observer.observe(quoteContainer);
                });
            }

            // Initialize quote animations
            initializeQuoteAnimation();

            // Scroll-triggered animations for Key Learning cards
            function initializeScrollAnimations() {
                // Create intersection observer with 25% threshold
                const observerOptions = {
                    threshold: 0.25, // Trigger when 25% of element is visible
                    rootMargin: '0px 0px -50px 0px' // Trigger slightly before element is fully visible
                };

                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const container = entry.target;
                            const elements = container.querySelectorAll('.scroll-fade-element');

                            // First, animate the card container
                            container.classList.add('animate-container');

                            // Then trigger line-by-line animations with staggered delays
                            // Start after container animation begins (300ms delay)
                            setTimeout(() => {
                                elements.forEach((element, index) => {
                                    const delay = parseInt(element.dataset.delay) || 0;

                                    setTimeout(() => {
                                        element.classList.add('animate');
                                        if (delay === 1) element.classList.add('delay-1');
                                        if (delay === 2) element.classList.add('delay-2');
                                        if (delay === 3) element.classList.add('delay-3');
                                    }, delay * 150); // Slightly longer delays for better visual separation
                                });
                            }, 200); // Wait for container animation to start

                            // Unobserve after animation to prevent re-triggering
                            observer.unobserve(container);
                        }
                    });
                }, observerOptions);

                // Observe all scroll-fade containers
                const scrollContainers = document.querySelectorAll('.scroll-fade-container');
                scrollContainers.forEach(container => {
                    observer.observe(container);
                });
            }

            // Initialize scroll animations
            initializeScrollAnimations();

            // Advanced Text Animations
            function initializeTextAnimations() {
                const observerOptions = {
                    threshold: 0.3,
                    rootMargin: '0px 0px -100px 0px'
                };

                const textObserver = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const container = entry.target;
                            animateTextSequence(container);
                            textObserver.unobserve(container);
                        }
                    });
                }, observerOptions);

                // Observe text animation containers
                const textContainers = document.querySelectorAll('.text-animation-container');
                textContainers.forEach(container => {
                    textObserver.observe(container);
                });
            }

            function animateTextSequence(container) {
                const words = container.querySelectorAll('.word-slide-up');
                const highlights = container.querySelectorAll('.highlight-reveal');
                const blurElement = container.parentElement.querySelector('.blur-in');

                // Find the associated elements
                const animationId = container.dataset.textAnimation;
                const icon = document.querySelector(`[data-icon-animation="${animationId}"]`);
                const card = document.querySelector(`[data-card-animation="${animationId}"]`);
                const title = document.querySelector(`[data-title-animation="${animationId}"]`);

                // Phase -2: Animate title first (Title entrance)
                if (title) {
                    setTimeout(() => {
                        title.classList.add('animate');
                    }, 50); // Title starts 50ms after scroll trigger
                }

                // Phase -1: Animate card after title (Card entrance)
                if (card) {
                    setTimeout(() => {
                        card.classList.add('animate');
                    }, 300); // Card starts 300ms after scroll trigger (250ms after title)
                }

                // Phase 0: Animate icon after card animation starts (Progressive Reveal)
                if (icon) {
                    setTimeout(() => {
                        icon.classList.add('animate');
                    }, 700); // Icon starts 700ms after scroll trigger (400ms after card)
                }

                // Phase 1: Animate words one by one (start after icon animation begins)
                words.forEach((word, index) => {
                    setTimeout(() => {
                        word.classList.add('animate');
                    }, 1200 + (index * 120)); // Start 1200ms after scroll trigger, 120ms between each word
                });

                // Phase 2: Animate highlights one at a time after all words are done
                const titleDelay = 50;
                const cardDelay = 300;
                const iconDelay = 700;
                const textStartDelay = 1200;
                const wordsDelay = words.length * 120;

                highlights.forEach((highlight, index) => {
                    setTimeout(() => {
                        highlight.classList.add('animate');

                        // Add glow effect to icon when highlights appear
                        if (icon && index === 0) {
                            icon.classList.add('glow');
                        }
                    }, textStartDelay + wordsDelay + 400 + (index * 500)); // 500ms between each highlight
                });

                // Phase 3: Animate blur-in after all highlights are done
                const highlightsDelay = highlights.length * 500;
                setTimeout(() => {
                    if (blurElement) {
                        blurElement.classList.add('animate');
                    }
                }, textStartDelay + wordsDelay + 400 + highlightsDelay + 300); // 300ms after last highlight
            }

            // Initialize text animations
            initializeTextAnimations();

            // Modern text animation - split text into animated words
            function splitTextIntoWords(element) {
                const text = element.textContent;
                const words = text.split(' ');
                element.innerHTML = '';

                words.forEach((word, index) => {
                    const span = document.createElement('span');
                    span.className = 'text-word';
                    span.textContent = word;
                    span.style.transitionDelay = `${index * 0.08}s`; // Stagger each word by 80ms
                    element.appendChild(span);
                });
            }

            // Animate text words in sequence
            function animateTextWords(container, delay = 0) {
                const words = container.querySelectorAll('.text-word');
                words.forEach((word, index) => {
                    setTimeout(() => {
                        word.classList.add('animate-in');
                    }, delay + (index * 80)); // 80ms stagger between words
                });
            }

            // Achievement Cards Animation - Much slower, more deliberate timing
            function initializeAchievementCardsAnimation() {
                const achievementCards = document.querySelectorAll('.achievement-card');

                const observerOptions = {
                    threshold: 0.15,
                    rootMargin: '0px 0px -30px 0px'
                };

                const cardObserver = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const card = entry.target;
                            const cardType = card.dataset.card;

                            // Determine stagger delay based on card type
                            const cardDelays = {
                                'technical-wins': 0,
                                'personal-growth': 400,
                                'discoveries': 0,
                                'realizations': 400
                            };
                            const cardDelay = cardDelays[cardType] || 0;

                            // Step 1: Animate card container first (slower reveal)
                            setTimeout(() => {
                                card.classList.add('animate-in');
                            }, 200 + cardDelay);

                            // Step 2: Prepare and animate header content
                            setTimeout(() => {
                                const headerContent = card.querySelector(`[data-content="${cardType}-header"]`);
                                if (headerContent) {
                                    // Split header title into words
                                    const headerTitle = headerContent.querySelector('h2');
                                    if (headerTitle) splitTextIntoWords(headerTitle);

                                    headerContent.classList.add('animate-in');

                                    // Animate header title words
                                    setTimeout(() => {
                                        if (headerTitle) animateTextWords(headerTitle, 0);
                                    }, 200);
                                }
                            }, 1400 + cardDelay); // Wait 1.2s for card + 200ms buffer

                            // Step 3: Show items container after header settles
                            setTimeout(() => {
                                const itemsContent = card.querySelector(`[data-content="${cardType}-items"]`);
                                if (itemsContent) {
                                    itemsContent.classList.add('animate-in');
                                }
                            }, 2200 + cardDelay); // Wait for header + 800ms

                            // Step 4: Prepare text animations by splitting into words
                            const items = card.querySelectorAll('.achievement-item');
                            items.forEach(item => {
                                const title = item.querySelector('h3');
                                const description = item.querySelector('p');
                                if (title) splitTextIntoWords(title);
                                if (description) splitTextIntoWords(description);
                            });

                            // Step 5: Reveal individual items with generous staggering
                            items.forEach((item, index) => {
                                const itemDelay = 3000 + cardDelay + (index * 400);

                                // Animate item container
                                setTimeout(() => {
                                    item.classList.add('animate-in');
                                }, itemDelay);

                                // Animate title words after container
                                setTimeout(() => {
                                    const title = item.querySelector('h3');
                                    if (title) animateTextWords(title, 0);
                                }, itemDelay + 300);

                                // Animate description words after title
                                setTimeout(() => {
                                    const description = item.querySelector('p');
                                    if (description) animateTextWords(description, 0);
                                }, itemDelay + 600);
                            });

                            // Stop observing after animation is triggered
                            cardObserver.unobserve(card);
                        }
                    });
                }, observerOptions);

                // Observe all achievement cards
                achievementCards.forEach(card => {
                    cardObserver.observe(card);
                });
            }

            // Initialize achievement cards animation
            initializeAchievementCardsAnimation();
        });
    </script>

    <!-- Enhanced Reflection Section Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize reflection section animations
            function initializeReflectionAnimations() {
                const reflectionItems = document.querySelectorAll('.reflection-item');

                // Create intersection observer for reflection items
                const reflectionObserver = new IntersectionObserver((entries) => {
                    entries.forEach((entry, index) => {
                        if (entry.isIntersecting) {
                            setTimeout(() => {
                                entry.target.classList.add('animate-in');
                            }, index * 100); // Stagger animation
                        }
                    });
                }, {
                    threshold: 0.2,
                    rootMargin: '0px 0px -50px 0px'
                });

                // Observe all reflection items
                reflectionItems.forEach(item => {
                    reflectionObserver.observe(item);
                });

                // Add click interactions for reflection items
                reflectionItems.forEach(item => {
                    item.addEventListener('click', function() {
                        // Add a subtle pulse effect on click
                        this.style.transform = 'scale(0.98)';
                        setTimeout(() => {
                            this.style.transform = '';
                        }, 150);

                        // Optional: Add sound effect or haptic feedback here
                        // navigator.vibrate && navigator.vibrate(50);
                    });

                    // Enhanced hover effects
                    item.addEventListener('mouseenter', function() {
                        const icon = this.querySelector('.w-10.h-10');
                        if (icon) {
                            icon.style.transform = 'scale(1.1) rotate(5deg)';
                        }
                    });

                    item.addEventListener('mouseleave', function() {
                        const icon = this.querySelector('.w-10.h-10');
                        if (icon) {
                            icon.style.transform = '';
                        }
                    });
                });
            }

            // Initialize parallax effect for background elements
            function initializeParallaxEffect() {
                const reflectionSection = document.getElementById('reflection');
                if (!reflectionSection) return;

                const backgroundElements = reflectionSection.querySelectorAll('.absolute');

                window.addEventListener('scroll', () => {
                    const scrolled = window.pageYOffset;
                    const sectionTop = reflectionSection.offsetTop;
                    const sectionHeight = reflectionSection.offsetHeight;
                    const windowHeight = window.innerHeight;

                    // Check if section is in viewport
                    if (scrolled + windowHeight > sectionTop && scrolled < sectionTop + sectionHeight) {
                        const progress = (scrolled + windowHeight - sectionTop) / (sectionHeight + windowHeight);

                        backgroundElements.forEach((element, index) => {
                            const speed = (index + 1) * 0.5;
                            const yPos = -(progress * 100 * speed);
                            element.style.transform = `translateY(${yPos}px)`;
                        });
                    }
                });
            }

            // Initialize typewriter effect for the main heading
            function initializeTypewriterEffect() {
                const heading = document.querySelector('#reflection h2 span');
                if (!heading) return;

                const text = heading.textContent;
                heading.textContent = '';
                heading.style.borderRight = '2px solid #3674B5';

                let i = 0;
                const typeWriter = () => {
                    if (i < text.length) {
                        heading.textContent += text.charAt(i);
                        i++;
                        setTimeout(typeWriter, 100);
                    } else {
                        // Remove cursor after typing is complete
                        setTimeout(() => {
                            heading.style.borderRight = 'none';
                        }, 1000);
                    }
                };

                // Start typewriter effect when section comes into view
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            setTimeout(typeWriter, 500);
                            observer.unobserve(entry.target);
                        }
                    });
                }, { threshold: 0.5 });

                observer.observe(document.getElementById('reflection'));
            }



            // Initialize all reflection animations
            initializeReflectionAnimations();
            initializeParallaxEffect();
            initializeTypewriterEffect();

            // Add smooth scroll behavior for internal links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Initialize Enhanced Quote Section Animations
            function initializeQuoteAnimations() {
                const quoteCard = document.querySelector('.inspirational-quote-card');
                if (!quoteCard) return;

                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            startQuoteAnimationSequence();
                            observer.unobserve(entry.target);
                        }
                    });
                }, {
                    threshold: 0.3,
                    rootMargin: '0px 0px -50px 0px'
                });

                observer.observe(quoteCard);
            }

            function startQuoteAnimationSequence() {
                const quoteCard = document.querySelector('.inspirational-quote-card');
                const iconContainer = document.querySelector('.quote-icon-container');
                const textContainer = document.querySelector('.quote-text-container');
                const subtitleContainer = document.querySelector('.quote-subtitle-container');
                const actionHint = document.querySelector('.quote-action-hint');
                const typewriterText = document.querySelector('.typewriter-text');

                // Step 1: Animate card entry (0ms)
                setTimeout(() => {
                    quoteCard.classList.add('animate-in');
                }, 0);

                // Step 2: Animate icon (400ms after card)
                setTimeout(() => {
                    iconContainer.classList.add('animate-in');
                }, 400);

                // Step 3: Animate main quote text container (600ms after card)
                setTimeout(() => {
                    textContainer.classList.add('animate-in');
                    // Start typewriter effect after text container animates in
                    setTimeout(() => {
                        startTypewriterEffect(typewriterText);
                    }, 300);
                }, 600);

                // Step 4: Animate subtitle (1800ms after card - after typewriter completes)
                setTimeout(() => {
                    subtitleContainer.classList.add('animate-in');
                }, 1800);

                // Step 5: Animate action hint (2200ms after card)
                setTimeout(() => {
                    actionHint.classList.add('animate-in');
                }, 2200);
            }

            function startTypewriterEffect(element) {
                if (!element) return;

                const text = element.getAttribute('data-text');
                const speed = 50; // milliseconds per character
                let i = 0;

                element.textContent = '';
                element.style.width = 'auto';

                function typeCharacter() {
                    if (i < text.length) {
                        element.textContent += text.charAt(i);
                        i++;
                        setTimeout(typeCharacter, speed);
                    } else {
                        // Typing complete - remove cursor after a delay
                        setTimeout(() => {
                            element.classList.add('typing-complete');
                        }, 1000);
                    }
                }

                typeCharacter();
            }

            // Initialize quote animations
            initializeQuoteAnimations();
        });
    </script>

    <!-- Interactive Search Bar Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('searchInput');
            const searchStatus = document.getElementById('searchStatus');
            const originalText = "how to build professional e-commerce site";

            if (searchInput && searchStatus) {
                // Handle Enter key press
                searchInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();

                        // Show searching status
                        searchStatus.style.opacity = '1';

                        // Create disintegration effect
                        const currentText = searchInput.value;
                        let currentLength = currentText.length;

                        // Disintegrate text character by character
                        const disintegrateInterval = setInterval(() => {
                            if (currentLength > 0) {
                                // Randomly remove characters to create disintegration effect
                                const randomIndex = Math.floor(Math.random() * currentLength);
                                const textArray = searchInput.value.split('');
                                textArray.splice(randomIndex, 1);
                                searchInput.value = textArray.join('');
                                currentLength--;
                            } else {
                                clearInterval(disintegrateInterval);

                                // After text is completely gone, wait a moment then restore
                                setTimeout(() => {
                                    searchStatus.style.opacity = '0';

                                    // Restore original text with typing effect
                                    let index = 0;
                                    const typeInterval = setInterval(() => {
                                        if (index < originalText.length) {
                                            searchInput.value = originalText.substring(0, index + 1);
                                            index++;
                                        } else {
                                            clearInterval(typeInterval);
                                        }
                                    }, 50); // Type each character every 50ms
                                }, 500); // Wait 500ms after complete disintegration
                            }
                        }, 100); // Remove character every 100ms
                    }
                });

                // Handle input changes
                searchInput.addEventListener('input', function() {
                    // Reset any ongoing effects when user types
                    searchStatus.style.opacity = '0';
                });

                // Handle focus/blur for better UX
                searchInput.addEventListener('focus', function() {
                    this.parentElement.style.borderColor = '#3B82F6';
                    this.parentElement.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                });

                searchInput.addEventListener('blur', function() {
                    this.parentElement.style.borderColor = '#D1D5DB';
                    this.parentElement.style.boxShadow = 'none';
                });
            }

            // Research Header Animation Controller
            function initializeResearchHeaderAnimation() {
                const researchHeader = document.querySelector('[data-animation="research-header"]');
                if (!researchHeader) return;

                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            animateResearchHeader();
                            observer.unobserve(entry.target);
                        }
                    });
                }, { threshold: 0.5 });

                observer.observe(researchHeader);
            }

            function animateResearchHeader() {
                const header = document.querySelector('.research-header');
                const icon = document.querySelector('.research-icon');
                const text = document.querySelector('.research-text');

                // Animate header container first
                setTimeout(() => {
                    header.classList.add('animate-in');
                }, 100);

                // Then animate icon with slight delay
                setTimeout(() => {
                    icon.classList.add('animate-in');
                }, 300);

                // Finally animate text
                setTimeout(() => {
                    text.classList.add('animate-in');
                }, 500);
            }

            // Browser Mockup Animation Controller
            function initializeBrowserAnimations() {
                const browserWindow = document.querySelector('[data-animation="browser"]');
                if (!browserWindow) return;

                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            animateBrowserSequence();
                            observer.unobserve(entry.target);
                        }
                    });
                }, { threshold: 0.3 });

                observer.observe(browserWindow);
            }

            function animateBrowserSequence() {
                // 1. Animate browser window in
                setTimeout(() => {
                    const browserWindow = document.querySelector('.browser-window');
                    const browserHeader = document.querySelector('.browser-header');

                    browserWindow.classList.add('animate-in');
                    browserHeader.classList.add('animate-in');
                }, 100);

                // 2. Animate tabs in sequence
                setTimeout(() => {
                    const tabs = document.querySelectorAll('.browser-tab');
                    tabs.forEach((tab, index) => {
                        const delay = parseInt(tab.dataset.tabDelay) || 0;
                        setTimeout(() => {
                            tab.classList.add('animate-in');
                        }, delay);
                    });
                }, 600);

                // 3. Start typing animation
                setTimeout(() => {
                    startTypingAnimation();
                }, 1400);

                // 4. Animate fire icon and quote
                setTimeout(() => {
                    const fireIcon = document.querySelector('.fire-icon');
                    const quote = document.querySelector('.browser-quote');

                    fireIcon.classList.add('animate-in');

                    setTimeout(() => {
                        quote.classList.add('animate-in');
                    }, 300);
                }, 2800);
            }

            function startTypingAnimation() {
                const searchInput = document.getElementById('searchInput');
                const typingText = searchInput.dataset.typingText;
                const searchStatus = document.getElementById('searchStatus');

                let currentText = '';
                let currentIndex = 0;

                // Show searching status
                searchStatus.style.opacity = '1';

                function typeNextCharacter() {
                    if (currentIndex < typingText.length) {
                        currentText += typingText[currentIndex];
                        searchInput.value = currentText;
                        currentIndex++;

                        // Variable typing speed for more natural feel
                        const delay = Math.random() * 100 + 50;
                        setTimeout(typeNextCharacter, delay);
                    } else {
                        // Hide searching status when done
                        setTimeout(() => {
                            searchStatus.style.opacity = '0';
                        }, 500);
                    }
                }

                typeNextCharacter();
            }

            // Statistics Cards Animation Controller
            function initializeStatisticsAnimation() {
                // Find all statistics containers
                const statsContainers = [
                    document.querySelector('.grid.grid-cols-1.sm\\:grid-cols-2.lg\\:grid-cols-3'), // Phase 1 research stats
                    document.querySelector('.grid.grid-cols-3.gap-4'), // Other research stats
                    ...document.querySelectorAll('.metrics-container .grid') // Progress metrics sections
                ].filter(container => container !== null); // Remove null entries

                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            // Add animate-in class to all stat elements
                            const statCards = entry.target.querySelectorAll('.stat-card-animate');
                            const statIcons = entry.target.querySelectorAll('.stat-icon-animate');
                            const statNumbers = entry.target.querySelectorAll('.stat-number-animate');
                            const statLabels = entry.target.querySelectorAll('.stat-label-animate');

                            // Trigger card animations
                            statCards.forEach(card => {
                                card.classList.add('animate-in');
                            });

                            // Trigger icon animations
                            statIcons.forEach(icon => {
                                icon.classList.add('animate-in');
                            });

                            // Trigger text animations
                            statNumbers.forEach(number => {
                                number.classList.add('animate-in');
                            });

                            statLabels.forEach(label => {
                                label.classList.add('animate-in');
                            });

                            // Unobserve after animation is triggered
                            observer.unobserve(entry.target);
                        }
                    });
                }, {
                    threshold: 0.3, // Trigger when 30% of the element is visible
                    rootMargin: '0px 0px -50px 0px' // Trigger slightly before fully in view
                });

                // Observe all statistics containers
                statsContainers.forEach(container => {
                    if (container) {
                        observer.observe(container);
                    }
                });
            }

            // Initialize all browser section animations
            initializeResearchHeaderAnimation();
            initializeBrowserAnimations();
            initializeStatisticsAnimation();
            initializeChallengesAnimation();

            // Challenges & Learnings Cards Animation Controller
            function initializeChallengesAnimation() {
                // Find all challenge cards directly
                const challengeCards = document.querySelectorAll('.challenges-card-animate');

                if (challengeCards.length === 0) return;

                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            // Trigger card animation
                            entry.target.classList.add('animate-in');

                            // Find and trigger icon animations within this card
                            const challengeIcons = entry.target.querySelectorAll('.challenges-icon-animate');
                            challengeIcons.forEach(icon => {
                                icon.classList.add('animate-in');
                            });

                            // Find and trigger text animations within this card
                            const challengeTexts = entry.target.querySelectorAll('.challenges-text-animate');
                            challengeTexts.forEach(text => {
                                text.classList.add('animate-in');
                            });

                            // Unobserve after animation is triggered
                            observer.unobserve(entry.target);
                        }
                    });
                }, {
                    threshold: 0.3, // Trigger when 30% of the card is visible
                    rootMargin: '0px 0px -30px 0px' // Trigger slightly before fully visible
                });

                // Observe each challenge card individually
                challengeCards.forEach(card => {
                    observer.observe(card);
                });
            }

            // Tooltip scroll-triggered animation (desktop only)
            function initializeTooltipAnimation() {
                // Check if device is mobile (screen width less than 1024px)
                if (window.innerWidth < 1024) return;

                const patternsSection = document.getElementById('patterns-section');
                const tooltipCard = document.querySelector('.tooltip-card');
                const tooltipText = document.querySelector('.tooltip-text');
                const tooltipArrow = document.querySelector('.tooltip-arrow');

                if (!patternsSection || !tooltipCard) return;

                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            // Trigger tooltip animations when section comes into view
                            setTimeout(() => {
                                tooltipCard.classList.add('animate');
                            }, 200);

                            setTimeout(() => {
                                tooltipText.classList.add('animate');
                            }, 200);

                            setTimeout(() => {
                                tooltipArrow.classList.add('animate');
                            }, 200);

                            // Stop observing after animation is triggered
                            observer.unobserve(patternsSection);
                        }
                    });
                }, {
                    threshold: 0.3, // Trigger when 30% of the section is visible
                    rootMargin: '0px 0px -100px 0px' // Trigger slightly before fully in view
                });

                observer.observe(patternsSection);
            }

            // Initialize tooltip animation
            initializeTooltipAnimation();

            // Handle window resize to show/hide tooltips based on screen size
            window.addEventListener('resize', function() {
                const tooltipCard = document.querySelector('.tooltip-card');
                const enhancedTooltipCard = document.querySelector('.enhanced-tooltip-card');
                const jsTooltipCard = document.querySelector('.js-tooltip-card');

                if (tooltipCard) {
                    if (window.innerWidth < 1024) {
                        // Hide tooltip on mobile
                        tooltipCard.style.display = 'none';
                    } else {
                        // Show tooltip on desktop
                        tooltipCard.style.display = 'flex';
                    }
                }

                if (enhancedTooltipCard) {
                    if (window.innerWidth < 1024) {
                        // Hide enhanced tooltip on mobile
                        enhancedTooltipCard.style.display = 'none';
                    } else {
                        // Show enhanced tooltip on desktop
                        enhancedTooltipCard.style.display = 'flex';
                    }
                }

                if (jsTooltipCard) {
                    if (window.innerWidth < 1024) {
                        // Hide JS tooltip on mobile
                        jsTooltipCard.style.display = 'none';
                    } else {
                        // Show JS tooltip on desktop
                        jsTooltipCard.style.display = 'block';
                    }
                }
            });

            // Tooltip fade-out on card hover (desktop only)
            function initializeTooltipHover() {
                // Check if device is mobile (screen width less than 1024px)
                if (window.innerWidth < 1024) return;

                const patternsCard = document.getElementById('patterns-card');
                const tooltipCard = document.querySelector('.tooltip-card');

                if (!patternsCard || !tooltipCard) return;

                patternsCard.addEventListener('mouseenter', () => {
                    // Add fade-out class for smooth exit
                    tooltipCard.classList.add('fade-out');
                });

                // Optional: fade back in when mouse leaves (if you want it to return)
                // patternsCard.addEventListener('mouseleave', () => {
                //     tooltipCard.classList.remove('fade-out');
                // });
            }

            // Initialize tooltip hover behavior
            initializeTooltipHover();

            // Code Evolution Journey Animation
            function initializeCodeEvolutionAnimation() {
                const codeEvolutionSection = document.getElementById('code-evolution-section');
                if (!codeEvolutionSection) return;

                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            // Animate container first - slower start
                            setTimeout(() => {
                                codeEvolutionSection.classList.add('animate');
                            }, 200);

                            // Animate header - more deliberate timing
                            const header = codeEvolutionSection.querySelector('.code-header');
                            if (header) {
                                setTimeout(() => {
                                    header.classList.add('animate');
                                }, 600);
                            }

                            // Animate cards with slower stagger
                            const cards = codeEvolutionSection.querySelectorAll('.code-card');
                            cards.forEach((card, index) => {
                                setTimeout(() => {
                                    card.classList.add('animate');

                                    // Animate card contents with slower sequence
                                    const cardContents = card.querySelectorAll('.code-card-content');
                                    cardContents.forEach((content, contentIndex) => {
                                        setTimeout(() => {
                                            content.classList.add('animate');
                                        }, 400 + (contentIndex * 250));
                                    });
                                }, 1000 + (index * 400));
                            });

                            // Stop observing after animation
                            observer.unobserve(codeEvolutionSection);
                        }
                    });
                }, { threshold: 0.3 });

                observer.observe(codeEvolutionSection);
            }

            // Feature Cards Animation
            function initializeFeatureCardsAnimation() {
                const featureCard = document.querySelector('.feature-card');
                const struggleCard = document.querySelector('.struggle-card');

                if (!featureCard || !struggleCard) return;

                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const card = entry.target;

                            if (card.classList.contains('feature-card')) {
                                // Animate feature card with delay
                                setTimeout(() => {
                                    card.classList.add('animate');
                                }, 300);
                            } else if (card.classList.contains('struggle-card')) {
                                // Animate struggle card with longer delay
                                setTimeout(() => {
                                    card.classList.add('animate');
                                }, 600);
                            }

                            // Stop observing after animation
                            observer.unobserve(card);
                        }
                    });
                }, { threshold: 0.2 });

                observer.observe(featureCard);
                observer.observe(struggleCard);
            }

            // Initialize code evolution animation
            initializeCodeEvolutionAnimation();

            // Enhanced System Tooltip Animation (desktop only)
            function initializeEnhancedTooltipAnimation() {
                // Check if device is mobile (screen width less than 1024px)
                if (window.innerWidth < 1024) return;

                const codeEvolutionSection = document.getElementById('code-evolution-section');
                const enhancedTooltipCard = document.querySelector('.enhanced-tooltip-card');
                const enhancedTooltipText = document.querySelector('.enhanced-tooltip-text');
                const enhancedTooltipArrow = document.querySelector('.enhanced-tooltip-arrow');

                if (!codeEvolutionSection || !enhancedTooltipCard) return;

                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            // Enhanced card appears at 1400ms (1000 + 1*400), tooltip shows 4 seconds after
                            const enhancedCardDelay = 1400;
                            const tooltipDelay = 4000;
                            const totalDelay = enhancedCardDelay + tooltipDelay;

                            // Trigger enhanced tooltip animations
                            setTimeout(() => {
                                enhancedTooltipCard.classList.add('animate');
                            }, totalDelay);

                            setTimeout(() => {
                                enhancedTooltipText.classList.add('animate');
                            }, totalDelay);

                            setTimeout(() => {
                                enhancedTooltipArrow.classList.add('animate');
                            }, totalDelay);

                            // Stop observing after animation is triggered
                            observer.unobserve(codeEvolutionSection);
                        }
                    });
                }, { threshold: 0.3 });

                observer.observe(codeEvolutionSection);
            }

            // Initialize enhanced tooltip animation
            initializeEnhancedTooltipAnimation();

            // JavaScript Code Block Tooltip Animation (desktop only)
            function initializeJsTooltipAnimation() {
                // Check if device is mobile (screen width less than 1024px)
                if (window.innerWidth < 1024) return;

                const jsTooltipCard = document.querySelector('.js-tooltip-card');
                const jsTooltipText = document.querySelector('.js-tooltip-text');
                const jsTooltipArrow = document.querySelector('.js-tooltip-arrow');

                if (!jsTooltipCard) return;

                // Find the specific code block that contains the JavaScript tooltip
                const codeBlock = jsTooltipCard.closest('.code-block-animate');
                if (!codeBlock) return;

                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            // Code block appears at 200ms, tooltip shows after 2 seconds
                            const codeBlockDelay = 200;
                            const tooltipDelay = 2000; // 2 second delay after code block appears
                            const totalDelay = codeBlockDelay + tooltipDelay;

                            // Trigger JS tooltip animations
                            setTimeout(() => {
                                jsTooltipCard.classList.add('animate');
                            }, totalDelay);

                            setTimeout(() => {
                                jsTooltipText.classList.add('animate');
                            }, totalDelay);

                            setTimeout(() => {
                                jsTooltipArrow.classList.add('animate');
                            }, totalDelay);

                            // Stop observing after animation is triggered
                            observer.unobserve(codeBlock);
                        }
                    });
                }, {
                    threshold: 0.2,
                    rootMargin: '0px 0px -100px 0px'
                });

                observer.observe(codeBlock);
            }

            // Initialize JS tooltip animation
            initializeJsTooltipAnimation();

            // Initialize feature cards animation
            initializeFeatureCardsAnimation();

            // Questions and Cards Scroll Animation
            function initializeQuestionsCardsAnimation() {
                // Questions card animation
                const questionsCards = document.querySelectorAll('.questions-card-animate');
                const challengesLearningsCards = document.querySelectorAll('.challenges-learnings-card');
                const experienceLevelCards = document.querySelectorAll('.experience-level-card');

                const observerOptions = {
                    threshold: 0.2,
                    rootMargin: '0px 0px -50px 0px'
                };

                const cardObserver = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            // Animate the main card first
                            entry.target.classList.add('animate-in');

                            // Then animate the individual items inside with staggered delays
                            const items = entry.target.querySelectorAll('.questions-item-animate');
                            items.forEach((item, index) => {
                                setTimeout(() => {
                                    item.classList.add('animate-in');
                                }, 200 + (index * 150)); // Stagger each item by 150ms
                            });

                            // Stop observing after animation
                            cardObserver.unobserve(entry.target);
                        }
                    });
                }, observerOptions);

                // Observe all card types
                [...questionsCards, ...challengesLearningsCards, ...experienceLevelCards].forEach(card => {
                    if (card) {
                        cardObserver.observe(card);
                    }
                });
            }

            // Initialize questions and cards animation
            initializeQuestionsCardsAnimation();
        });
    </script>

    <!-- Progressive Card Unlock Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check if device is mobile (screen width less than 1024px)
            if (window.innerWidth < 1024) {
                // On mobile, don't apply any locking - all cards are accessible
                return;
            }

            // Get all the cards
            const patternsCard = document.getElementById('patterns-card');
            const saasCard = document.getElementById('saas-card');
            const questionsCard = document.getElementById('questions-card');
            const discoveriesCard = document.getElementById('discoveries-card');

            // Initially lock the three dependent cards
            const lockedCards = [saasCard, questionsCard, discoveriesCard];

            // Add locked class to dependent cards
            lockedCards.forEach(card => {
                if (card) {
                    card.classList.add('card-locked');
                }
            });

            // Track if patterns card has been hovered
            let patternsUnlocked = false;

            // Patterns card hover handler
            if (patternsCard) {
                patternsCard.addEventListener('mouseenter', function() {
                    // Reveal the patterns card
                    patternsCard.classList.add('revealed');

                    // Unlock other cards if not already unlocked
                    if (!patternsUnlocked) {
                        patternsUnlocked = true;

                        // Remove locked class from all dependent cards
                        lockedCards.forEach(card => {
                            if (card) {
                                card.classList.remove('card-locked');
                            }
                        });

                        // Add hover listeners to newly unlocked cards
                        addHoverListeners();
                    }
                });
            }

            // Function to add hover listeners to unlocked cards
            function addHoverListeners() {
                const unlockedCards = [
                    { element: saasCard, id: 'saas-card' },
                    { element: questionsCard, id: 'questions-card' },
                    { element: discoveriesCard, id: 'discoveries-card' }
                ];

                unlockedCards.forEach(card => {
                    if (card.element) {
                        card.element.addEventListener('mouseenter', function() {
                            // Add 'revealed' class to permanently remove blur for this specific card
                            card.element.classList.add('revealed');
                        });
                    }
                });
            }
        });


    </script>

    <!-- Intro Sequence JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Intro sequence elements
            const introOverlay = document.getElementById('introOverlay');
            const skipButton = document.getElementById('skipIntro');
            const progressFill = document.getElementById('introProgressFill');

            // Intro sequence configuration
            const INTRO_DURATION = 8000; // 8 seconds total
            const PROGRESS_UPDATE_INTERVAL = 50; // Update every 50ms for smooth animation

            let introTimer;
            let progressTimer;
            let startTime;
            let isIntroActive = true;

            // Initialize intro sequence
            function initializeIntro() {
                // Prevent scrolling during intro
                document.body.style.overflow = 'hidden';

                // Start the intro sequence
                startIntroSequence();

                // Set up skip button
                skipButton.addEventListener('click', skipIntro);

                // Optional: Skip on Escape key
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Escape' && isIntroActive) {
                        skipIntro();
                    }
                });
            }

            // Start the intro sequence with progress tracking
            function startIntroSequence() {
                startTime = Date.now();

                // Start progress bar animation
                updateProgress();

                // Set timer to automatically end intro after 8 seconds
                introTimer = setTimeout(() => {
                    endIntro();
                }, INTRO_DURATION);
            }

            // Update progress bar smoothly
            function updateProgress() {
                if (!isIntroActive) return;

                const currentTime = Date.now();
                const elapsed = currentTime - startTime;
                const progress = Math.min((elapsed / INTRO_DURATION) * 100, 100);

                progressFill.style.width = progress + '%';

                // Continue updating if intro is still active
                if (progress < 100 && isIntroActive) {
                    progressTimer = setTimeout(updateProgress, PROGRESS_UPDATE_INTERVAL);
                }
            }

            // Skip intro function
            function skipIntro() {
                if (!isIntroActive) return;

                // Mark intro as seen in localStorage
                localStorage.setItem('hasSeenIntro', 'true');

                // Clear timers
                if (introTimer) clearTimeout(introTimer);
                if (progressTimer) clearTimeout(progressTimer);

                // Set progress to 100% for visual completion
                progressFill.style.width = '100%';

                // Use faster slide-up animation for skip
                startFastSlideUpAnimation();
            }

            // Faster slide-up animation for skip action
            function startFastSlideUpAnimation() {
                isIntroActive = false;

                // Apply faster slide-up animation for skip
                introOverlay.style.animation = 'smoothSlideUp 0.6s cubic-bezier(0.4, 0.0, 0.2, 1) forwards';

                // Show main content immediately
                showMainContent();

                // Initialize navigation and animations after skip
                setTimeout(() => {
                    initializeAllAnimations();
                }, 300);

                // Complete cleanup after fast animation
                setTimeout(() => {
                    introOverlay.style.display = 'none';
                    document.body.style.overflow = '';
                }, 600); // 0.6s animation duration
            }

            // Show main content function
            function showMainContent() {
                const elementsToShow = [
                    document.getElementById('mobileNav'),
                    document.getElementById('mainContainer')
                ];

                elementsToShow.forEach(element => {
                    if (element) {
                        element.classList.remove('main-content-hidden');
                        element.classList.add('main-content-visible');
                    }
                });
            }

            // End intro sequence
            function endIntro() {
                if (!isIntroActive) return;

                isIntroActive = false;

                // Mark intro as seen in localStorage
                localStorage.setItem('hasSeenIntro', 'true');

                // Clear any remaining timers
                if (introTimer) clearTimeout(introTimer);
                if (progressTimer) clearTimeout(progressTimer);

                // Set progress to 100%
                progressFill.style.width = '100%';

                // Start smooth slide-up animation
                startSlideUpAnimation();
            }

            // Smooth slide-up animation
            function startSlideUpAnimation() {
                // Apply smooth slide-up animation to the entire overlay
                introOverlay.classList.add('slide-up');

                // Show main content immediately as intro starts sliding up
                showMainContent();

                // Initialize navigation and animations after intro
                setTimeout(() => {
                    initializeAllAnimations();
                }, 500);

                // Complete cleanup after animation finishes
                setTimeout(() => {
                    introOverlay.style.display = 'none';
                    document.body.style.overflow = '';
                }, 1000); // 1.0s animation duration
            }



            // Handle page visibility changes (pause/resume intro if user switches tabs)
            document.addEventListener('visibilitychange', function() {
                if (!isIntroActive) return;

                if (document.hidden) {
                    // Pause timers when page is hidden
                    if (introTimer) clearTimeout(introTimer);
                    if (progressTimer) clearTimeout(progressTimer);
                } else {
                    // Resume timers when page becomes visible
                    const currentTime = Date.now();
                    const elapsed = currentTime - startTime;
                    const remaining = INTRO_DURATION - elapsed;

                    if (remaining > 0) {
                        // Resume intro timer
                        introTimer = setTimeout(() => {
                            endIntro();
                        }, remaining);

                        // Resume progress updates
                        updateProgress();
                    } else {
                        // Time has already elapsed, end intro
                        endIntro();
                    }
                }
            });

            // Skip intro directly for returning visitors
            function skipIntroDirectly() {
                // Hide intro overlay immediately
                introOverlay.style.display = 'none';

                // Enable scrolling
                document.body.style.overflow = '';

                // Show main content immediately
                showMainContent();

                // Initialize all animations and functionality
                setTimeout(() => {
                    initializeAllAnimations();
                }, 100);
            }

            // Initialize all animations for returning visitors
            function initializeAllAnimations() {
                // Show and animate go back button
                const goBackButton = document.getElementById('goBackButton');
                const rightNavigation = document.getElementById('rightNavigation');

                if (goBackButton) {
                    goBackButton.style.display = 'flex';
                    goBackButton.classList.add('animate-in');

                    setTimeout(() => {
                        const buttonText = goBackButton.querySelector('.button-text');
                        if (buttonText) {
                            buttonText.classList.add('animate-in');
                        }
                    }, 200);
                }

                // Show and animate right navigation
                if (rightNavigation) {
                    rightNavigation.classList.remove('hidden');
                    rightNavigation.classList.add('animate-in');

                    const navDots = rightNavigation.querySelectorAll('.nav-dot');
                    navDots.forEach((dot, index) => {
                        setTimeout(() => {
                            dot.classList.add('animate-in');
                        }, 300 + (index * 100));
                    });
                }

                // Initialize scroll tracking and hero animations
                initializeScrollTracking();
                initializeHeroAnimations();
            }

            // Check if intro should be played
            const hasSeenIntro = localStorage.getItem('hasSeenIntro');
            const shouldPlayIntro = sessionStorage.getItem('playIntroSequence');

            if (shouldPlayIntro === 'true') {
                // Clear the sessionStorage flag
                sessionStorage.removeItem('playIntroSequence');
                // Force play intro regardless of localStorage
                initializeIntro();
            } else if (hasSeenIntro === 'true') {
                // Skip intro and show main content immediately
                skipIntroDirectly();
            } else {
                // Initialize the intro sequence for first-time visitors
                initializeIntro();
            }

            // Global function to reset intro (accessible via console)
            window.resetIntro = function() {
                localStorage.removeItem('hasSeenIntro');
                location.reload();
            };

            // Log helper message for developers
            console.log('💡 Tip: To see the intro again, type "resetIntro()" in the console');
        });
    </script>

    <!-- Go Back Button Script -->
    <script>
        function goBack() {
            // Navigate to story.html
            window.location.href = 'story.html';
        }

        // Navigation and go back button control
        document.addEventListener('DOMContentLoaded', function() {
            const goBackButton = document.getElementById('goBackButton');
            const rightNavigation = document.getElementById('rightNavigation');
            const introOverlay = document.getElementById('introOverlay');

            // Hide elements during intro
            if (introOverlay && goBackButton && rightNavigation) {
                goBackButton.style.display = 'none';

                // Show and animate elements after intro completes
                const observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                            if (introOverlay.style.display === 'none') {
                                // Show and animate go back button
                                goBackButton.style.display = 'flex';

                                setTimeout(() => {
                                    goBackButton.classList.add('animate-in');
                                }, 100);

                                setTimeout(() => {
                                    const buttonText = goBackButton.querySelector('.button-text');
                                    if (buttonText) {
                                        buttonText.classList.add('animate-in');
                                    }
                                }, 500);

                                // Show and animate right navigation
                                rightNavigation.classList.remove('hidden');

                                setTimeout(() => {
                                    rightNavigation.classList.add('animate-in');
                                }, 300);

                                // Animate navigation dots with stagger
                                const navDots = rightNavigation.querySelectorAll('.nav-dot');
                                navDots.forEach((dot, index) => {
                                    setTimeout(() => {
                                        dot.classList.add('animate-in');
                                    }, 800 + (index * 100)); // Start after nav container, stagger by 100ms
                                });

                                // Initialize scroll tracking
                                initializeScrollTracking();

                                // Initialize hero animations
                                initializeHeroAnimations();

                                observer.disconnect();
                            }
                        }
                    });
                });

                observer.observe(introOverlay, { attributes: true });
            }
        });

        // Initialize hero section animations
        function initializeHeroAnimations() {
            // Animate progress bar after a delay
            setTimeout(() => {
                const progressFill = document.getElementById('heroProgressFill');
                if (progressFill) {
                    progressFill.style.width = '75%';
                }
            }, 1500); // Start after hero content animations

            // Initialize strategy cards observer
            initializeStrategyCardsObserver();
        }

        // Enhanced Strategy Cards Intersection Observer
        function initializeStrategyCardsObserver() {
            const strategyCards = document.querySelectorAll('.strategy-card-animate');

            if (strategyCards.length === 0) return;

            const observerOptions = {
                threshold: 0.15,
                rootMargin: '0px 0px -80px 0px'
            };

            const cardObserver = new IntersectionObserver((entries) => {
                entries.forEach((entry) => {
                    if (entry.isIntersecting) {
                        const card = entry.target;
                        const cardIndex = Array.from(strategyCards).indexOf(card);

                        // Start card animation with staggered delay
                        setTimeout(() => {
                            animateStrategyCard(card);
                        }, cardIndex * 200); // Stagger cards by 200ms

                        // Stop observing this card once animated
                        cardObserver.unobserve(card);
                    }
                });
            }, observerOptions);

            // Start observing all strategy cards
            strategyCards.forEach(card => {
                cardObserver.observe(card);
            });
        }

        // Animate individual strategy card with sophisticated timing
        function animateStrategyCard(card) {
            // Phase 1: Card entry animation (0ms)
            card.classList.add('animate-in');

            // Phase 2: Icon container animation (600ms after card)
            setTimeout(() => {
                const iconContainer = card.querySelector('.strategy-icon-container');
                if (iconContainer) {
                    iconContainer.classList.add('animate-in');
                }
            }, 600);

            // Phase 3: Icon rotation animation (800ms after card)
            setTimeout(() => {
                const icon = card.querySelector('.strategy-icon-animate');
                if (icon) {
                    icon.classList.add('animate-in');
                }
            }, 800);

            // Phase 4: Title animation (1200ms after card)
            setTimeout(() => {
                const title = card.querySelector('.strategy-title-animate');
                if (title) {
                    title.classList.add('animate-in');
                }
            }, 1200);

            // Phase 5: Description animation (1600ms after card)
            setTimeout(() => {
                const description = card.querySelector('.strategy-description-animate');
                if (description) {
                    description.classList.add('animate-in');
                }
            }, 1600);
        }

        // Scroll to section function
        function scrollToSection(sectionId) {
            const section = document.getElementById(sectionId);
            if (section) {
                section.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        }

        // Initialize scroll tracking for active states
        function initializeScrollTracking() {
            const navDots = document.querySelectorAll('.nav-dot');
            const sections = ['hero', 'research', 'building', 'testing', 'reflection'];

            function updateActiveNav() {
                const scrollPosition = window.scrollY + window.innerHeight / 2;

                let activeSection = 'hero';

                sections.forEach(sectionId => {
                    const section = document.getElementById(sectionId);
                    if (section) {
                        const sectionTop = section.offsetTop;
                        const sectionBottom = sectionTop + section.offsetHeight;

                        if (scrollPosition >= sectionTop && scrollPosition < sectionBottom) {
                            activeSection = sectionId;
                        }
                    }
                });

                // Update active states
                navDots.forEach(dot => {
                    if (dot.dataset.section === activeSection) {
                        dot.classList.add('active');
                    } else {
                        dot.classList.remove('active');
                    }
                });
            }

            // Throttled scroll listener
            let scrollTimeout;
            window.addEventListener('scroll', () => {
                if (scrollTimeout) {
                    clearTimeout(scrollTimeout);
                }
                scrollTimeout = setTimeout(updateActiveNav, 10);
            });

            // Initial call
            updateActiveNav();
        }
    </script>


</body>
</html>

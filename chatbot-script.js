// Sentio AI Chatbot JavaScript with Gemini AI Integration

class SentioAIChatbot {
    constructor() {
        this.chatMessages = document.getElementById('chatMessages');
        this.chatInput = document.getElementById('chatInput');
        this.sendButton = document.getElementById('sendButton');
        this.isTyping = false;
        this.sessionId = null;
        this.backendUrl = 'http://localhost:3001'; // Backend server URL
        this.retryCount = 0;
        this.maxRetries = 3;

        // Fallback responses for when AI is unavailable
        this.fallbackResponses = {
            greetings: [
                "Hello! How can I assist you with your shopping today?",
                "Hi there! I'm here to help with any questions about our products or services.",
                "Welcome! What can I help you find today?"
            ],
            orderTracking: [
                "I'd be happy to help you track your order! Could you please provide your order number?",
                "To track your order, I'll need your order number. It usually starts with #ORD followed by numbers.",
                "Let me help you track that order. Please share your order number and I'll look it up for you."
            ],
            productHelp: [
                "I can help you find the perfect product! What are you looking for today?",
                "I'd love to help you with product recommendations. What type of item interests you?",
                "Tell me what you're shopping for and I'll suggest some great options!"
            ],
            returns: [
                "Our return policy allows returns within 30 days of purchase. Items must be in original condition.",
                "You can return most items within 30 days. Would you like me to help you start a return?",
                "Returns are easy! You have 30 days from purchase. What item would you like to return?"
            ],
            support: [
                "I'm here to help! You can also reach our human support <NAME_EMAIL> or call 1-800-SENTIO.",
                "For additional support, our team is available 24/7 at <EMAIL>. How else can I assist you?",
                "Need more help? Contact our support <NAME_EMAIL> or let me know what specific issue you're facing."
            ],
            default: [
                "I understand you're asking about that. Let me help you find the right information.",
                "That's a great question! Let me provide you with the best answer I can.",
                "I'm here to help with that. Could you provide a bit more detail so I can assist you better?"
            ]
        };

        this.init();
    }

    init() {
        // Focus on input when page loads
        this.chatInput.focus();

        // Add event listeners
        this.setupEventListeners();

        // Check backend connectivity
        this.checkBackendHealth();
    }

    async checkBackendHealth() {
        try {
            const response = await fetch(`${this.backendUrl}/api/health`);
            if (response.ok) {
                console.log('✅ Backend server is healthy');
            } else {
                console.warn('⚠️ Backend server responded with error:', response.status);
                this.showConnectionWarning();
            }
        } catch (error) {
            console.error('❌ Backend server is not accessible:', error);
            this.showConnectionWarning();
        }
    }

    showConnectionWarning() {
        const warningDiv = document.createElement('div');
        warningDiv.className = 'bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-3 mb-4 text-sm';
        warningDiv.innerHTML = `
            <div class="flex">
                <div class="flex-shrink-0">⚠️</div>
                <div class="ml-3">
                    <p>AI features are currently unavailable. Using basic responses. Please ensure the backend server is running.</p>
                </div>
            </div>
        `;
        this.chatMessages.parentElement.insertBefore(warningDiv, this.chatMessages);
    }

    setupEventListeners() {
        // Send button click
        this.sendButton.addEventListener('click', () => this.sendMessage());
        
        // Enter key press
        this.chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendMessage();
            }
        });

        // Input focus effects
        this.chatInput.addEventListener('focus', () => {
            this.chatInput.parentElement.style.transform = 'scale(1.02)';
        });

        this.chatInput.addEventListener('blur', () => {
            this.chatInput.parentElement.style.transform = 'scale(1)';
        });
    }

    async sendMessage() {
        const message = this.chatInput.value.trim();
        if (!message || this.isTyping) return;

        // Add user message
        this.addUserMessage(message);

        // Clear input
        this.chatInput.value = '';

        // Show typing indicator and generate AI response
        this.showTypingIndicator();

        try {
            // Try to get AI response from backend
            const aiResponse = await this.getAIResponse(message);
            this.hideTypingIndicator();
            this.addAIMessage(aiResponse);
            this.retryCount = 0; // Reset retry count on success
        } catch (error) {
            console.error('AI Response Error:', error);
            this.hideTypingIndicator();

            // Fallback to template responses
            this.addAIResponse(message);

            // Show error message if multiple failures
            if (this.retryCount >= this.maxRetries) {
                this.showErrorMessage('AI assistant is temporarily unavailable. Using basic responses.');
            }
        }
    }

    async getAIResponse(message) {
        this.retryCount++;

        const response = await fetch(`${this.backendUrl}/api/chat`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                message: message,
                sessionId: this.sessionId
            })
        });

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.error || `HTTP ${response.status}`);
        }

        const data = await response.json();

        // Store session ID for context continuity
        if (data.sessionId) {
            this.sessionId = data.sessionId;
        }

        return data.response;
    }

    showErrorMessage(message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'bg-red-100 border-l-4 border-red-500 text-red-700 p-3 mb-4 text-sm';
        errorDiv.innerHTML = `
            <div class="flex">
                <div class="flex-shrink-0">❌</div>
                <div class="ml-3">
                    <p>${message}</p>
                </div>
            </div>
        `;
        this.chatMessages.appendChild(errorDiv);
        this.scrollToBottom();

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.remove();
            }
        }, 5000);
    }

    sendQuickMessage(message) {
        this.chatInput.value = message;
        this.sendMessage();
    }

    addUserMessage(message) {
        const messageElement = this.createMessageElement('user', message);
        this.chatMessages.appendChild(messageElement);
        this.scrollToBottom();
    }

    addAIMessage(response) {
        const messageElement = this.createMessageElement('ai', response);
        this.chatMessages.appendChild(messageElement);
        this.scrollToBottom();
    }

    addAIResponse(userMessage) {
        const response = this.generateFallbackResponse(userMessage);
        const messageElement = this.createMessageElement('ai', response);
        this.chatMessages.appendChild(messageElement);
        this.scrollToBottom();
    }

    createMessageElement(type, message) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message-bubble';
        
        const timestamp = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

        if (type === 'user') {
            messageDiv.innerHTML = `
                <div class="flex justify-end">
                    <div class="user-message text-white px-4 py-2 rounded-lg max-w-xs">
                        <p class="text-sm">${this.escapeHtml(message)}</p>
                        <span class="text-xs opacity-75 mt-1 block">${timestamp}</span>
                    </div>
                </div>
            `;
        } else {
            messageDiv.innerHTML = `
                <div class="flex items-start gap-3">
                    <div class="ai-avatar w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0">
                        <lord-icon
                            src="https://cdn.lordicon.com/cniwvohj.json"
                            trigger="hover"
                            colors="primary:#ffffff,secondary:#ffffff"
                            style="width:16px;height:16px;">
                        </lord-icon>
                    </div>
                    <div class="bg-gray-100 rounded-lg px-4 py-2 max-w-xs">
                        <p class="text-sm text-gray-800">${this.escapeHtml(message)}</p>
                        <span class="text-xs text-gray-500 mt-1 block">${timestamp}</span>
                    </div>
                </div>
            `;
        }

        return messageDiv;
    }

    showTypingIndicator() {
        this.isTyping = true;
        const typingDiv = document.createElement('div');
        typingDiv.id = 'typingIndicator';
        typingDiv.className = 'message-bubble';
        typingDiv.innerHTML = `
            <div class="flex items-start gap-3">
                <div class="ai-avatar w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0">
                    <lord-icon
                        src="https://cdn.lordicon.com/cniwvohj.json"
                        trigger="hover"
                        colors="primary:#ffffff,secondary:#ffffff"
                        style="width:16px;height:16px;">
                    </lord-icon>
                </div>
                <div class="bg-gray-100 rounded-lg px-4 py-2">
                    <div class="typing-indicator">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                </div>
            </div>
        `;
        
        this.chatMessages.appendChild(typingDiv);
        this.scrollToBottom();
    }

    hideTypingIndicator() {
        const typingIndicator = document.getElementById('typingIndicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
        this.isTyping = false;
    }

    generateFallbackResponse(userMessage) {
        const message = userMessage.toLowerCase();

        // Greeting patterns
        if (message.includes('hello') || message.includes('hi') || message.includes('hey')) {
            return this.getRandomFallbackResponse('greetings');
        }

        // Order tracking
        if (message.includes('track') || message.includes('order') || message.includes('shipping')) {
            return this.getRandomFallbackResponse('orderTracking');
        }

        // Product help
        if (message.includes('product') || message.includes('recommend') || message.includes('find') || message.includes('buy')) {
            return this.getRandomFallbackResponse('productHelp');
        }

        // Returns
        if (message.includes('return') || message.includes('refund') || message.includes('exchange')) {
            return this.getRandomFallbackResponse('returns');
        }

        // Support
        if (message.includes('support') || message.includes('help') || message.includes('contact')) {
            return this.getRandomFallbackResponse('support');
        }

        // Default response
        return this.getRandomFallbackResponse('default');
    }

    getRandomFallbackResponse(category) {
        const responses = this.fallbackResponses[category];
        return responses[Math.floor(Math.random() * responses.length)];
    }

    scrollToBottom() {
        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Global functions for quick actions
function sendQuickMessage(message) {
    if (window.chatbot) {
        window.chatbot.sendQuickMessage(message);
    }
}

function sendMessage() {
    if (window.chatbot) {
        window.chatbot.sendMessage();
    }
}

function handleKeyPress(event) {
    if (event.key === 'Enter') {
        sendMessage();
    }
}

// Initialize chatbot when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.chatbot = new SentioAIChatbot();
    console.log('Sentio AI Chatbot initialized successfully!');
});

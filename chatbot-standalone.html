<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sentio AI Chatbot - Standalone</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Lordicon Script for Animated Icons -->
    <script src="https://cdn.lordicon.com/lordicon.js"></script>
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;900&family=DM+Sans:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'brand': '#3674B5',
                        'brand-blue': '#3674B5'
                    },
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                        'dm-sans': ['DM Sans', 'sans-serif']
                    }
                }
            }
        }
    </script>

    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        /* Chatbot Container Styles */
        .chatbot-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        /* Chat Messages Styles */
        .chat-messages {
            scroll-behavior: smooth;
        }

        .chat-messages::-webkit-scrollbar {
            width: 6px;
        }

        .chat-messages::-webkit-scrollbar-track {
            background: transparent;
        }

        .chat-messages::-webkit-scrollbar-thumb {
            background: rgba(54, 116, 181, 0.3);
            border-radius: 3px;
        }

        .chat-messages::-webkit-scrollbar-thumb:hover {
            background: rgba(54, 116, 181, 0.5);
        }

        /* Message Bubble Animations */
        .message-bubble {
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Typing Indicator */
        .typing-indicator {
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 8px 12px;
        }

        .typing-dot {
            width: 6px;
            height: 6px;
            background: #94a3b8;
            border-radius: 50%;
            animation: typingDot 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typingDot {
            0%, 60%, 100% {
                transform: translateY(0);
                opacity: 0.4;
            }
            30% {
                transform: translateY(-10px);
                opacity: 1;
            }
        }

        /* Input Focus Styles */
        .chat-input:focus {
            outline: none;
            border-color: #3674B5;
            box-shadow: 0 0 0 3px rgba(54, 116, 181, 0.1);
        }

        /* Send Button Hover Effect */
        .send-button {
            transition: all 0.2s ease;
        }

        .send-button:hover {
            background: #2563eb;
            transform: scale(1.05);
        }

        .send-button:active {
            transform: scale(0.95);
        }

        /* Quick Actions */
        .quick-action {
            transition: all 0.2s ease;
            border: 1px solid #e2e8f0;
        }

        .quick-action:hover {
            background: #3674B5;
            color: white;
            border-color: #3674B5;
            transform: translateY(-1px);
        }

        /* AI Avatar Gradient */
        .ai-avatar {
            background: linear-gradient(135deg, #3674B5, #1e40af);
        }

        /* User Message Gradient */
        .user-message {
            background: linear-gradient(135deg, #3674B5, #1e40af);
        }
    </style>
</head>
<body class="p-4">
    <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="text-center mb-6">
            <h1 class="text-3xl font-bold text-gray-800 mb-2">Sentio AI Assistant</h1>
            <p class="text-gray-600">Your intelligent e-commerce support companion</p>
        </div>

        <!-- Chatbot Container -->
        <div class="chatbot-container rounded-2xl overflow-hidden max-w-2xl mx-auto">
            <!-- Chat Header -->
            <div class="bg-gradient-to-r from-brand to-blue-600 text-white p-4">
                <div class="flex items-center gap-3">
                    <div class="ai-avatar w-10 h-10 rounded-full flex items-center justify-center">
                        <lord-icon
                            src="https://cdn.lordicon.com/cniwvohj.json"
                            trigger="hover"
                            colors="primary:#ffffff,secondary:#ffffff"
                            style="width:24px;height:24px;">
                        </lord-icon>
                    </div>
                    <div>
                        <h3 class="font-semibold">Sentio AI</h3>
                        <p class="text-sm opacity-90">Online • Ready to help</p>
                    </div>
                </div>
            </div>

            <!-- Chat Messages -->
            <div id="chatMessages" class="chat-messages h-96 overflow-y-auto p-4 space-y-4">
                <!-- Welcome Message -->
                <div class="message-bubble flex items-start gap-3">
                    <div class="ai-avatar w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0">
                        <lord-icon
                            src="https://cdn.lordicon.com/cniwvohj.json"
                            trigger="hover"
                            colors="primary:#ffffff,secondary:#ffffff"
                            style="width:16px;height:16px;">
                        </lord-icon>
                    </div>
                    <div class="bg-gray-100 rounded-lg px-4 py-2 max-w-xs">
                        <p class="text-sm text-gray-800">Hello! I'm Sentio AI, your e-commerce assistant. How can I help you today?</p>
                        <span class="text-xs text-gray-500 mt-1 block">Just now</span>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="p-4 border-t border-gray-200">
                <p class="text-sm text-gray-600 mb-2">Quick actions:</p>
                <div class="flex flex-wrap gap-2">
                    <button class="quick-action px-3 py-1 rounded-full text-sm bg-gray-50 hover:bg-brand hover:text-white transition-all" onclick="sendQuickMessage('Track my order')">
                        📦 Track Order
                    </button>
                    <button class="quick-action px-3 py-1 rounded-full text-sm bg-gray-50 hover:bg-brand hover:text-white transition-all" onclick="sendQuickMessage('Product recommendations')">
                        🛍️ Product Help
                    </button>
                    <button class="quick-action px-3 py-1 rounded-full text-sm bg-gray-50 hover:bg-brand hover:text-white transition-all" onclick="sendQuickMessage('Return policy')">
                        🔄 Returns
                    </button>
                    <button class="quick-action px-3 py-1 rounded-full text-sm bg-gray-50 hover:bg-brand hover:text-white transition-all" onclick="sendQuickMessage('Contact support')">
                        💬 Support
                    </button>
                </div>
            </div>

            <!-- Chat Input -->
            <div class="p-4 border-t border-gray-200">
                <div class="flex gap-2">
                    <input 
                        type="text" 
                        id="chatInput" 
                        class="chat-input flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-brand"
                        placeholder="Type your message..."
                        onkeypress="handleKeyPress(event)"
                    >
                    <button 
                        id="sendButton"
                        class="send-button bg-brand text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-all"
                        onclick="sendMessage()"
                    >
                        <lord-icon
                            src="https://cdn.lordicon.com/rhvddzym.json"
                            trigger="hover"
                            colors="primary:#ffffff"
                            style="width:20px;height:20px;">
                        </lord-icon>
                    </button>
                </div>
            </div>
        </div>

        <!-- Info Section -->
        <div class="text-center mt-6 text-sm text-gray-600">
            <p>Powered by Sentio AI • Available 24/7 • Secure & Private</p>
        </div>
    </div>

    <script src="chatbot-script.js"></script>
</body>
</html>

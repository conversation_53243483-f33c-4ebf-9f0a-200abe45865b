<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hear My Story - Sentio</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Lordicon Script for Animated Icons -->
    <script src="https://cdn.lordicon.com/lordicon.js"></script>

    <!-- Custom CSS for Timeline Effects -->
    <style>
        .timeline-future {
            filter: blur(2px);
            transition: all 0.5s ease-in-out;
        }
        .timeline-future:hover {
            filter: blur(0px);
            transform: scale(1.05);
        }
        .timeline-revealed {
            filter: blur(0px);
            transition: all 0.5s ease-in-out;
        }
        .timeline-revealed:hover {
            transform: scale(1.05);
        }

        /* Progress Circle Animations */
        .progress-circle-today {
            stroke-dasharray: 0, 100;
            animation: drawProgressToday 2s ease-out 0.2s forwards;
        }

        .progress-circle-phase1 {
            stroke-dasharray: 0, 100;
            animation: drawProgressPhase1 2s ease-out 0.4s forwards;
        }

        .progress-circle-phase2 {
            stroke-dasharray: 0, 100;
            animation: drawProgressPhase2 2s ease-out 0.6s forwards;
        }

        .progress-circle-final {
            stroke-dasharray: 0, 100;
            animation: drawProgressFinal 2s ease-out 0.8s forwards;
        }

        @keyframes drawProgressToday {
            0% { stroke-dasharray: 0, 100; }
            100% { stroke-dasharray: 15, 100; }
        }

        @keyframes drawProgressPhase1 {
            0% { stroke-dasharray: 0, 100; }
            100% { stroke-dasharray: 25, 100; }
        }

        @keyframes drawProgressPhase2 {
            0% { stroke-dasharray: 0, 100; }
            100% { stroke-dasharray: 60, 100; }
        }

        @keyframes drawProgressFinal {
            0% { stroke-dasharray: 0, 100; }
            100% { stroke-dasharray: 100, 100; }
        }

        /* Percentage text fade-in animation */
        .percentage-text {
            opacity: 0;
            animation: fadeInPercentage 1s ease-out 1.5s forwards;
        }

        @keyframes fadeInPercentage {
            0% { opacity: 0; transform: scale(0.8); }
            100% { opacity: 1; transform: scale(1); }
        }

        /* Animated Border Beam for Today Card */
        @property --angle {
            syntax: '<angle>';
            initial-value: 0deg;
            inherits: false;
        }

        .timeline-today {
            position: relative;
            --border-size: 2px;
            --animation-speed: 10s;
            --beam-color: #3674B5; /* Using your brand color */
        }

        .timeline-today::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: inherit;
            padding: var(--border-size);

            /* The magic: a conic gradient that rotates */
            background: conic-gradient(
                from var(--angle),
                transparent 0%,
                transparent 3%,
                var(--beam-color) 5%,
                var(--beam-color) 12%,
                transparent 15%,
                transparent 100%
            );

            /* Masking to create the border effect */
            mask:
                linear-gradient(#fff 0 0) content-box,
                linear-gradient(#fff 0 0);
            mask-composite: exclude;
            -webkit-mask:
                linear-gradient(#fff 0 0) content-box,
                linear-gradient(#fff 0 0);
            -webkit-mask-composite: xor;

            /* Animation */
            animation: rotate var(--animation-speed) linear infinite;
            will-change: transform;

            /* Glow effect */
            filter: drop-shadow(0 0 8px rgba(54, 116, 181, 0.6)) drop-shadow(0 0 16px rgba(54, 116, 181, 0.3));
        }

        @keyframes rotate {
            to {
                --angle: 360deg;
            }
        }

        /* Subscribe Button Styles from Testing.html */
        .subscribe-btn {
            border-radius: 8px;
            height: 40px;
            border: 1px solid #d1d5db !important;
            background: white;
            color: #333;
            padding: 8px 16px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08),
                        0 1px 2px rgba(0, 0, 0, 0.06);
            font-weight: bold;
            font-family: 'Inter', sans-serif;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            transition: all 300ms cubic-bezier(0.25, 0.1, 0.25, 1);
            width: auto;
            text-decoration: none;
            outline: none !important;
        }

        .subscribe-btn:hover {
            background: #3674B5;
            color: white;
            border-color: #3674B5;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(54, 116, 181, 0.15),
                        0 2px 4px rgba(54, 116, 181, 0.1);
        }

        /* Update lord-icon color on hover */
        .subscribe-btn:hover lord-icon {
            --primary-color: #ffffff;
        }

        /* Demo Card Glow Effect */
        .demo-card {
            transition: all 0.5s ease;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        .demo-card:hover {
            box-shadow: 0 20px 25px -5px rgba(54, 116, 181, 0.1),
                        0 10px 10px -5px rgba(54, 116, 181, 0.04),
                        0 0 0 1px rgba(54, 116, 181, 0.05);
            background: radial-gradient(circle at center, rgba(54, 116, 181, 0.02) 0%, transparent 70%);
            transform: translateY(-2px);
        }

        /* Logo Animation Styles */
        body {
            overflow-x: hidden;
        }

        /* Hide scrollbar but keep scroll functionality */
        html::-webkit-scrollbar,
        body::-webkit-scrollbar,
        *::-webkit-scrollbar {
            display: none;
            width: 0px;
            background: transparent;
        }

        html,
        body {
            -ms-overflow-style: none;  /* Internet Explorer 10+ */
            scrollbar-width: none;  /* Firefox */
        }

        /* Prevent scrolling and interactions during animation */
        body.animation-active {
            overflow: hidden;
            pointer-events: none;
        }

        /* Initially hide all elements except logo during animation */
        body.animation-loading .back-btn,
        body.animation-loading .story-nav,
        body.animation-loading .story-nav-item,
        body.animation-loading .fade-in-element,
        body.animation-loading #logo-text {
            opacity: 0 !important;
            visibility: hidden !important;
            pointer-events: none !important;
        }

        /* Extra specific hiding for back button during animation */
        .back-btn {
            opacity: 0;
            visibility: hidden;
            transition: all 0.4s ease;
        }

        /* Show back button only when revealed */
        .back-btn.reveal-element.visible {
            opacity: 1;
            visibility: visible;
        }

        /* Smooth reveal animations for elements after logo animation */
        .reveal-element {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.6s cubic-bezier(0.4, 0.0, 0.2, 1);
        }

        .reveal-element.visible {
            opacity: 1;
            transform: translateY(0);
        }

        /* Staggered reveal for navigation dots */
        .story-nav-item {
            opacity: 0;
            transform: scale(0.8);
            transition: all 0.4s cubic-bezier(0.4, 0.0, 0.2, 1);
        }

        .story-nav-item.reveal {
            opacity: 1;
            transform: scale(1);
        }

        /* Animated Input Labels - From Uiverse.io by alexruix (Adapted) */
        .input-group {
            position: relative;
            margin-bottom: 2rem;
        }

        .input-group .input {
            border: solid 1.5px #e5e7eb;
            border-radius: 1rem;
            background: transparent;
            padding: 1rem 1.25rem;
            font-size: 1rem;
            color: #1f2937;
            width: 100%;
            transition: border 150ms cubic-bezier(0.4,0,0.2,1);
        }

        .input-group .user-label {
            position: absolute;
            left: 20px;
            color: #9ca3af;
            pointer-events: none;
            transform: translateY(1rem);
            transition: 150ms cubic-bezier(0.4,0,0.2,1);
            background: white;
            padding: 0 0.5rem;
            font-size: 1rem;
        }

        .input-group .input:focus,
        .input-group .input:valid {
            outline: none;
            border: 1.5px solid #3674B5;
            box-shadow: 0 0 0 3px rgba(54, 116, 181, 0.1);
        }

        .input-group .input:focus ~ .user-label,
        .input-group .input:valid ~ .user-label {
            transform: translateY(-50%) scale(0.85);
            color: #3674B5;
            font-weight: 500;
        }

        /* Dynamic Textarea with Spring Physics */
        .input-group textarea.input {
            resize: none;
            height: 60px;
            min-height: 60px;
            padding-top: 1.25rem;
            transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
            overflow: hidden;
        }

        .input-group textarea.input:focus ~ .user-label,
        .input-group textarea.input:valid ~ .user-label {
            transform: translateY(-50%) scale(0.85);
        }

        /* Spring Physics Animations */
        .textarea-expanding {
            animation: springExpand 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
        }

        .textarea-collapsing {
            animation: springCollapse 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
        }

        @keyframes springExpand {
            0% {
                height: 60px;
                transform: scale(1) rotate(0deg);
                opacity: 1;
            }
            30% {
                transform: scale(1.02) rotate(0.5deg);
                opacity: 0.95;
            }
            50% {
                transform: scale(0.98) rotate(-0.3deg);
                opacity: 0.98;
            }
            70% {
                transform: scale(1.01) rotate(0.2deg);
                opacity: 0.99;
            }
            85% {
                transform: scale(0.995) rotate(-0.1deg);
                opacity: 1;
            }
            100% {
                height: var(--target-height, 120px);
                transform: scale(1) rotate(0deg);
                opacity: 1;
            }
        }

        @keyframes springCollapse {
            0% {
                height: var(--current-height, 120px);
                transform: scale(1) rotate(0deg);
                opacity: 1;
            }
            20% {
                transform: scale(0.98) rotate(-0.5deg);
                opacity: 0.95;
            }
            40% {
                transform: scale(1.02) rotate(0.3deg);
                opacity: 0.9;
            }
            60% {
                transform: scale(0.99) rotate(-0.2deg);
                opacity: 0.95;
            }
            80% {
                transform: scale(1.005) rotate(0.1deg);
                opacity: 0.98;
            }
            100% {
                height: 60px;
                transform: scale(1) rotate(0deg);
                opacity: 1;
            }
        }

        /* Jiggle effect for extra delight */
        .textarea-jiggle {
            animation: jiggle 0.3s ease-in-out;
        }

        @keyframes jiggle {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-2px) rotate(0.5deg); }
            75% { transform: translateX(2px) rotate(-0.5deg); }
        }

        /* Enhanced Rainbow Animation for GitHub Button */
        @keyframes rainbow {
            0% { background-position: 0% 50%; }
            100% { background-position: 200% 50%; }
        }



        .animate-rainbow {
            animation: rainbow 4s linear infinite;
        }

        .animate-rainbow::before {
            animation: rainbow 4s linear infinite;
        }

        /* Animated Star Button Styles - From Uiverse.io by opMorn */
        .star-button {
            position: relative;
            padding: 12px 35px;
            background: #3674B5;
            font-size: 17px;
            font-weight: 1000;
            color: #ffffff;
            border: 3px solid #3674B5;
            border-radius: 8px;
            box-shadow: 0 0 0 #ffffff;
            transition: all 0.3s ease-in-out;
            cursor: pointer;
        }

        .star-1 {
            position: absolute;
            top: 20%;
            left: 20%;
            width: 25px;
            height: auto;
            filter: drop-shadow(0 0 0 #3674B5);
            z-index: -5;
            transition: all 1s cubic-bezier(0.05, 0.83, 0.43, 0.96);
        }

        .star-2 {
            position: absolute;
            top: 45%;
            left: 45%;
            width: 15px;
            height: auto;
            filter: drop-shadow(0 0 0 #3674B5);
            z-index: -5;
            transition: all 1s cubic-bezier(0, 0.4, 0, 1.01);
        }

        .star-3 {
            position: absolute;
            top: 40%;
            left: 40%;
            width: 5px;
            height: auto;
            filter: drop-shadow(0 0 0 #3674B5);
            z-index: -5;
            transition: all 1s cubic-bezier(0, 0.4, 0, 1.01);
        }

        .star-4 {
            position: absolute;
            top: 20%;
            left: 40%;
            width: 8px;
            height: auto;
            filter: drop-shadow(0 0 0 #3674B5);
            z-index: -5;
            transition: all 0.8s cubic-bezier(0, 0.4, 0, 1.01);
        }

        .star-5 {
            position: absolute;
            top: 25%;
            left: 45%;
            width: 15px;
            height: auto;
            filter: drop-shadow(0 0 0 #3674B5);
            z-index: -5;
            transition: all 0.6s cubic-bezier(0, 0.4, 0, 1.01);
        }

        .star-6 {
            position: absolute;
            top: 5%;
            left: 50%;
            width: 5px;
            height: auto;
            filter: drop-shadow(0 0 0 #3674B5);
            z-index: -5;
            transition: all 0.8s ease;
        }

        .star-button:hover {
            background: transparent;
            color: #3674B5;
            box-shadow: 0 0 0px #3674B5;
        }

        .star-button:hover .star-1 {
            position: absolute;
            top: -80%;
            left: -30%;
            width: 25px;
            height: auto;
            filter: drop-shadow(0 0 0px #3674B5);
            z-index: 2;
        }

        .star-button:hover .star-2 {
            position: absolute;
            top: -0%;
            left: 10%;
            width: 15px;
            height: auto;
            filter: drop-shadow(0 0 0px #3674B5);
            z-index: 2;
        }

        .star-button:hover .star-3 {
            position: absolute;
            top: 55%;
            left: 25%;
            width: 5px;
            height: auto;
            filter: drop-shadow(0 0 0px #3674B5);
            z-index: 2;
        }

        .star-button:hover .star-4 {
            position: absolute;
            top: 30%;
            left: 80%;
            width: 8px;
            height: auto;
            filter: drop-shadow(0 0 0px #3674B5);
            z-index: 2;
        }

        .star-button:hover .star-5 {
            position: absolute;
            top: 25%;
            left: 115%;
            width: 15px;
            height: auto;
            filter: drop-shadow(0 0 0px #3674B5);
            z-index: 2;
        }

        .star-button:hover .star-6 {
            position: absolute;
            top: 5%;
            left: 60%;
            width: 5px;
            height: auto;
            filter: drop-shadow(0 0 0px #3674B5);
            z-index: 2;
        }

        .fil0 {
            fill: #3674B5;
        }

        /* Typing Animation Effects */
        .typing-input {
            position: relative;
            overflow: hidden;
        }

        .typing-input::after {
            content: '';
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            width: 2px;
            height: 20px;
            background: #3674B5;
            opacity: 0;
            animation: none;
        }

        .typing-input.typing::after {
            opacity: 1;
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        /* Character animation effects */
        .char-in {
            animation: charFadeIn 0.3s cubic-bezier(0.4, 0.0, 0.2, 1) forwards;
        }

        .char-out {
            animation: charFadeOut 0.2s cubic-bezier(0.4, 0.0, 0.2, 1) forwards;
        }

        @keyframes charFadeIn {
            0% {
                opacity: 0;
                transform: translateY(10px) scale(0.8);
            }
            100% {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes charFadeOut {
            0% {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
            100% {
                opacity: 0;
                transform: translateY(-10px) scale(1.2);
            }
        }

        /* Input field glow effect when typing */
        .typing-input.typing {
            box-shadow: 0 0 0 2px rgba(54, 116, 181, 0.1);
            border-color: #3674B5 !important;
        }

        .logo-container {
            position: relative;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transform: translate(calc(50vw + 50%), calc(50vh + 50%)) scale(3);
        }

        .logo-container #logo-text {
            opacity: 0;
        }

        .animate-logo-reveal {
            animation: center-scale-move 2s ease-in-out forwards;
        }

        @keyframes center-scale-move {
            0% {
                transform: translate(calc(50vw + 50%), calc(50vh + 50%)) scale(3);
                z-index: 1000;
            }
            40% {
                transform: translate(calc(50vw - 50%), calc(50vh - 50%)) scale(3);
                z-index: 1000;
            }
            100% {
                transform: translate(0, 0) scale(1);
                z-index: auto;
            }
        }

        @keyframes fade-in-logo-text {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .animate-logo-text {
            animation: fade-in-logo-text 1s ease-out 1s forwards;
        }

        .fade-in-element {
            opacity: 0;
        }

        .animate-fade-in {
            animation: fade-in-up 1s ease-out forwards;
        }

        @keyframes fade-in-up {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .main-content {
            opacity: 0;
        }

        .animate-main-content {
            animation: fade-in-up 1s ease-out forwards;
        }

        .subscribe-btn:active {
            scale: 0.95;
            background: #2a5a94;
            color: white;
            transform: translateY(0);
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1),
                        0 1px 1px rgba(0, 0, 0, 0.06);
        }
    </style>

    <!-- Timeline Reveal Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const futureCards = document.querySelectorAll('.timeline-future');

            futureCards.forEach(card => {
                let revealTimer = null;

                card.addEventListener('mouseenter', function() {
                    // Clear any existing timer
                    if (revealTimer) {
                        clearTimeout(revealTimer);
                    }

                    // Remove blur class and add revealed class
                    this.classList.remove('timeline-future');
                    this.classList.add('timeline-revealed');

                    // Set timer to blur again after 7 seconds
                    revealTimer = setTimeout(() => {
                        this.classList.remove('timeline-revealed');
                        this.classList.add('timeline-future');
                    }, 7000);
                });
            });
        });
    </script>

    <!-- Google Fonts: Inter + DM Sans -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;900&family=DM+Sans:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'brand': '#3674B5',
                        'brand-blue': '#3674B5'
                    },
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                        'dm-sans': ['DM Sans', 'sans-serif'],
                        'dm-sans-medium': ['DM Sans', 'sans-serif'],
                        'dm-sans-extrabold': ['DM Sans', 'sans-serif']
                    }
                }
            }
        }
    </script>
    
    <style>
        /* Use Inter font family - matching Testing.html */
        body {
            font-family: 'Inter', sans-serif;
            overflow-x: hidden; /* Prevent horizontal scroll from animation */
        }

        /* Smooth scrolling and story animations */
        html {
            scroll-behavior: smooth;
        }
        
        .story-section {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }
        
        .story-section.visible {
            opacity: 1;
            transform: translateY(0);
        }
        
        .story-timeline {
            position: relative;
        }
        
        .story-timeline::before {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, #3674B5, #e5e7eb);
            transform: translateX(-50%);
        }

        /* Timeline Progress Animation */
        .timeline-progress {
            position: absolute;
            left: 50%;
            top: 0;
            width: 2px;
            background: linear-gradient(to bottom, #3674B5, #3674B5);
            transform: translateX(-50%);
            height: 0%;
            transition: height 0.3s ease-out;
            z-index: 1;
        }

        .timeline-background {
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e5e7eb;
            transform: translateX(-50%);
            z-index: 0;
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 4rem;
        }

        /* Timeline Mobile Responsiveness */
        @media (max-width: 768px) {
            .timeline-container .absolute.left-1\/2 {
                left: 2rem !important;
                transform: translateX(0) !important;
            }

            .timeline-container .w-1\/2 {
                width: 100% !important;
            }

            .timeline-container .pr-8,
            .timeline-container .pl-8 {
                padding-left: 4rem !important;
                padding-right: 1rem !important;
            }

            .timeline-container .ml-auto {
                margin-left: 0 !important;
            }

            .timeline-background,
            .timeline-progress {
                left: 2rem !important;
                transform: translateX(0) !important;
            }
        }
        
        .timeline-dot {
            position: absolute;
            left: 50%;
            top: 2rem;
            width: 16px;
            height: 16px;
            background: #3674B5;
            border: 4px solid white;
            border-radius: 50%;
            transform: translateX(-50%);
            box-shadow: 0 0 0 4px rgba(54, 116, 181, 0.2);
        }
        
        .code-snippet {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 1rem;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.875rem;
            overflow-x: auto;
        }
        
        .highlight-box {
            background: linear-gradient(135deg, rgba(54, 116, 181, 0.1), rgba(54, 116, 181, 0.05));
            border-left: 4px solid #3674B5;
            padding: 1.5rem;
            border-radius: 0 8px 8px 0;
            margin: 1.5rem 0;
        }
        
        /* Minimalistic Navigation Styles */
        .story-nav {
            position: fixed;
            top: 50%;
            right: 1.5rem;
            transform: translateY(-50%);
            z-index: 50;
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            padding: 1rem 0.5rem;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border-radius: 2rem;
            border: 1px solid rgba(54, 116, 181, 0.1);
            box-shadow: 0 4px 20px rgba(54, 116, 181, 0.08);
        }

        .story-nav-item {
            position: relative;
            display: block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(54, 116, 181, 0.2);
            transition: all 0.4s cubic-bezier(0.4, 0.0, 0.2, 1);
            cursor: pointer;
            overflow: hidden;
        }

        .story-nav-item::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: #3674B5;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.4s cubic-bezier(0.4, 0.0, 0.2, 1);
        }

        .story-nav-item:hover {
            background: rgba(54, 116, 181, 0.3);
            transform: scale(1.2);
        }

        .story-nav-item:hover::before {
            width: 100%;
            height: 100%;
        }

        .story-nav-item.active {
            background: #3674B5;
            transform: scale(1.3);
            box-shadow: 0 0 12px rgba(54, 116, 181, 0.4);
        }

        .story-nav-item.active::before {
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.3);
        }

        /* Tooltip for navigation items */
        .story-nav-item::after {
            content: attr(data-tooltip);
            position: absolute;
            right: 2.5rem;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 0.5rem 0.75rem;
            border-radius: 0.5rem;
            font-size: 0.75rem;
            font-weight: 500;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
            pointer-events: none;
            z-index: 100;
        }

        .story-nav-item:hover::after {
            opacity: 1;
            visibility: visible;
            transform: translateY(-50%) translateX(-0.25rem);
        }
        
        /* Back button */
        .back-btn {
            position: fixed;
            top: 2rem;
            left: 2rem;
            z-index: 50;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 50%;
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .back-btn:hover {
            background: #3674B5;
            color: white;
            transform: scale(1.05);
        }
        
        /* Profile picture bouncy hover animation */
        .profile-picture {
            transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            cursor: pointer;
        }

        .profile-picture:hover {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 20px 40px rgba(54, 116, 181, 0.3);
        }

        .profile-picture img {
            transition: all 0.3s ease;
        }

        .profile-picture:hover img {
            transform: scale(1.05);
        }

        /* Stats styling to match Testing.html */
        .stat-number {
            transition: transform 0.3s ease, color 0.3s ease;
        }

        .stat-card:hover .stat-number {
            transform: scale(1.05);
            color: #3674B5;
        }

        @media (max-width: 768px) {
            .story-timeline::before {
                left: 2rem;
            }

            .timeline-dot {
                left: 2rem;
            }

            .story-nav {
                display: none;
            }
        }

        /* Color Psychology Interactive Button - From Uiverse.io by satyamchaudharydev */
        .color-psychology-button {
            position: relative;
            transition: all 0.3s ease-in-out;
            width: 64px;
            height: 64px;
            background-color: #3674B5;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #d1d5db;
            outline: none;
            overflow: hidden;
            cursor: pointer;
        }

        .color-psychology-button:hover {
            transform: scale(1.05);
            border-color: #9ca3af;
        }

        .color-psychology-button:hover::before {
            animation: shine 1.5s ease-out infinite;
        }

        .color-psychology-button::before {
            content: "";
            position: absolute;
            width: 100px;
            height: 100%;
            background-image: linear-gradient(
                120deg,
                rgba(255, 255, 255, 0) 30%,
                rgba(255, 255, 255, 0.8),
                rgba(255, 255, 255, 0) 70%
            );
            top: 0;
            left: -100px;
            opacity: 0.6;
        }

        @keyframes shine {
            0% {
                left: -100px;
            }
            60% {
                left: 100%;
            }
            to {
                left: 100%;
            }
        }

        /* Typography Hierarchy Magnifying Effect */
        .typography-magnify-container {
            position: relative;
            width: 64px;
            height: 64px;
            background-color: rgb(219 234 254);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease-in-out;
            overflow: hidden;
            border: 1px solid #d1d5db;
        }

        .typography-magnify-container:hover {
            transform: scale(1.1);
            border-color: #9ca3af;
        }

        .typography-text {
            font-size: 1.5rem;
            font-weight: 800;
            color: #3674B5;
            transition: all 0.3s ease-in-out;
            font-family: 'DM Sans', sans-serif;
        }

        .typography-magnify-container:hover .typography-text {
            transform: scale(1.2);
            text-shadow: 0 2px 8px rgba(54, 116, 181, 0.4);
        }

        /* Magnifying glass effect overlay */
        .typography-magnify-container::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.4s ease-in-out;
            pointer-events: none;
        }

        .typography-magnify-container:hover::before {
            width: 80px;
            height: 80px;
        }

        /* Week Modal Styles - Global Overlay */
        #weekModal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            backdrop-filter: blur(0px);
        }

        #weekModal.show {
            opacity: 1;
            visibility: visible;
            background-color: rgba(0, 0, 0, 0.6); /* Darker fallback for browsers without backdrop-filter */
            backdrop-filter: blur(4px);
        }

        /* Fallback for browsers without backdrop-filter support */
        @supports not (backdrop-filter: blur(4px)) {
            #weekModal.show {
                background-color: rgba(0, 0, 0, 0.7);
            }
        }

        /* Modal Scroll Progress Indicator */
        .modal-progress-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background-color: rgba(255, 255, 255, 0.2);
            z-index: 10001;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .modal-progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #3674B5 0%, #4A90E2 100%);
            width: 0%;
            transition: width 0.1s ease-out;
            border-radius: 0 2px 2px 0;
            box-shadow: 0 0 8px rgba(54, 116, 181, 0.3);
        }

        #weekModal.show .modal-progress-container {
            opacity: 1;
        }

        /* Modal Navigation Header */
        .modal-nav-header {
            position: sticky;
            top: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            z-index: 100;
            padding: 12px 24px;
            margin: -24px -24px 24px -24px;
        }

        .modal-nav-links {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .modal-nav-link {
            padding: 6px 12px;
            background: rgba(54, 116, 181, 0.1);
            color: #3674B5;
            text-decoration: none;
            border-radius: 20px;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.2s ease;
            border: 1px solid rgba(54, 116, 181, 0.2);
        }

        .modal-nav-link:hover {
            background: #3674B5;
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(54, 116, 181, 0.3);
        }

        .modal-nav-link:active {
            transform: translateY(0);
        }

        /* Responsive navigation */
        @media (max-width: 640px) {
            .modal-nav-header {
                padding: 8px 16px;
                margin: -16px -16px 16px -16px;
            }

            .modal-nav-links {
                gap: 6px;
            }

            .modal-nav-link {
                padding: 4px 8px;
                font-size: 12px;
            }
        }

        /* Enhanced modal centering */
        .modal-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        }

        /* Consolidated modal content styles */
        #weekModal .modal-content {
            position: relative;
            max-width: 72rem;
            width: 95vw;
            max-height: 92vh;
            background: white;
            border-radius: 0.75rem;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            overflow-y: auto;
            transform: scale(0.8) translateY(20px);
            opacity: 0;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        #weekModal.show .modal-content {
            transform: scale(1) translateY(0);
            opacity: 1;
        }

        /* Modal responsive adjustments */
        @media (max-width: 640px) {
            .modal-container {
                padding: 0.5rem;
            }

            #weekModal .modal-content {
                max-height: calc(100vh - 1rem);
                margin: 0;
            }
        }

        /* Enhanced Slideshow Dot Animations */
        .dot {
            transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1) !important;
            transform-origin: center;
        }

        .dot:hover {
            transform: scale(1.1);
        }

        .dot.active {
            transform: scale(1);
        }

        @media (max-height: 600px) {
            .modal-container {
                align-items: flex-start;
                padding-top: 2rem;
            }
        }

        /* Prevent body scroll when modal is open */
        body.modal-open {
            overflow: hidden;
        }

        /* Enhanced Modal Animations */
        .modal-content {
            animation: modalSlideIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        @keyframes modalSlideIn {
            from {
                transform: scale(0.9) translateY(30px);
                opacity: 0;
            }
            to {
                transform: scale(1) translateY(0);
                opacity: 1;
            }
        }

        /* Micro-interactions for cards */
        .hover-lift {
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .hover-lift:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 25px rgba(54, 116, 181, 0.15);
        }

        /* Animated gradient background */
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .animated-gradient {
            background: linear-gradient(-45deg, #3674B5, #4F8CC9, #6BA3D6, #87BAE3);
            background-size: 400% 400%;
            animation: gradientShift 8s ease infinite;
        }

        /* Progress indicator enhancement */
        .progress-bar {
            transition: width 0.3s ease;
            background: linear-gradient(90deg, #3674B5, #4F8CC9);
        }

        /* Button hover effects */
        .btn-primary {
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        /* Enhanced focus states */
        .focus-ring:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(54, 116, 181, 0.3);
        }

        /* Smooth scrolling for modal navigation */
        .modal-content {
            scroll-behavior: smooth;
        }

        /* Phase-specific modal styling */
        .modal-content[data-phase="research"] {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }

        .modal-content[data-phase="research"] .modal-header {
            background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
        }

        .modal-content[data-phase="building"] {
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
        }

        .modal-content[data-phase="building"] .modal-header {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }

        .modal-content[data-phase="challenge"] {
            background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
        }

        .modal-content[data-phase="challenge"] .modal-header {
            background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
        }

        .modal-content[data-phase="launch"] {
            background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
        }

        .modal-content[data-phase="launch"] .modal-header {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }

        /* Phase-specific section styling */
        .phase-section {
            background: rgba(255, 255, 255, 0.7);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
        }

        .phase-section:hover {
            background: rgba(255, 255, 255, 0.9);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        /* Week Badge Design - From Uiverse.io by vinodjangid07 */
        .week-badge {
            width: 45px;
            height: 45px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            background-color: transparent;
            position: relative;
            border-radius: 7px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .week-badge-container {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: transparent;
            backdrop-filter: blur(0px);
            letter-spacing: 0.8px;
            border-radius: 10px;
            transition: all 0.3s;
            border: 1px solid rgba(156, 156, 156, 0.466);
            font-weight: bold;
            color: white;
            font-size: 14px;
            font-family: 'Inter', sans-serif;
        }

        .week-badge-bg {
            position: absolute;
            content: "";
            width: 100%;
            height: 100%;
            background: #3674B5;
            z-index: -1;
            border-radius: 10px;
            pointer-events: none;
            transition: all 0.3s;
        }

        .week-badge:hover .week-badge-bg {
            transform: rotate(35deg);
            transform-origin: bottom;
        }

        .week-badge:hover .week-badge-container {
            background-color: rgba(209, 209, 209, 0.466);
            backdrop-filter: blur(4px);
        }

        /* ===== WEEK CARD SCROLL ANIMATIONS ===== */
        .week-card-hidden {
            opacity: 0;
            filter: blur(10px);
        }

        .week-card-left.week-card-hidden {
            transform: translateX(-100px);
        }

        .week-card-right.week-card-hidden {
            transform: translateX(100px);
        }

        .week-card-visible {
            opacity: 1;
            filter: blur(0px);
            transform: translateX(0);
            animation: weekCardEnter 1.5s ease-out forwards;
        }

        @keyframes weekCardEnter {
            0% {
                opacity: 0;
                filter: blur(15px);
                transform: translateX(0);
            }
            85% {
                opacity: 1;
                filter: blur(0px);
                transform: translateX(0);
            }
            92% {
                transform: translateX(0) rotate(0.5deg);
            }
            96% {
                transform: translateX(0) rotate(-0.3deg);
            }
            100% {
                opacity: 1;
                filter: blur(0px);
                transform: translateX(0) rotate(0deg);
            }
        }

        /* ===== SLIDESHOW ANIMATION STYLES ===== */
        .slide {
            will-change: opacity, filter, transform;
            backface-visibility: hidden;
            -webkit-backface-visibility: hidden;
            transform-style: preserve-3d;
        }

        /* Initial state for slides */
        .slide:not(.active) {
            opacity: 0;
            pointer-events: none;
            filter: blur(0px);
            transform: scale(1);
        }

        /* Active slide state */
        .slide.active {
            opacity: 1;
            pointer-events: auto;
            filter: blur(0px);
            transform: scale(1);
        }

        /* Smooth transition classes for better performance */
        .slide-transition-out {
            transition: opacity 0.4s ease-out, filter 0.4s ease-out !important;
        }

        .slide-transition-in {
            transition: opacity 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                       filter 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                       transform 0.8s cubic-bezier(0.16, 1, 0.3, 1) !important;
        }

        /* Hardware acceleration for smoother animations */
        #slideshow-container {
            transform: translateZ(0);
            -webkit-transform: translateZ(0);
        }

        /* ===== ENHANCED IMAGE MODAL ANIMATIONS ===== */
        /* Modal backdrop with pure blur effect */
        #imageModal {
            background: transparent;
            backdrop-filter: blur(0px);
            -webkit-backdrop-filter: blur(0px);
            transition: backdrop-filter 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                       opacity 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            opacity: 0;
        }

        #imageModal.show {
            background: transparent;
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            opacity: 1;
        }

        /* Image blur in/out animation with subtle scale */
        #imageModal #modalImage {
            transform: scale(0.95);
            filter: blur(8px);
            transition: transform 0.9s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                       filter 0.9s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            transition-delay: 0.1s;
            will-change: transform, filter;
        }

        #imageModal.show #modalImage {
            transform: scale(1);
            filter: blur(0px);
        }

        /* Close button blur animation */
        #imageModal .modal-close-btn {
            transform: scale(0.8);
            filter: blur(4px);
            transition: transform 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                       filter 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                       background-color 0.2s ease;
            transition-delay: 0.2s;
        }

        #imageModal.show .modal-close-btn {
            transform: scale(1);
            filter: blur(0px);
        }

        #imageModal .modal-close-btn:hover {
            transform: scale(1.1);
            background-color: rgba(255, 255, 255, 0.4);
        }

        /* Caption blur animation with centered positioning */
        #imageModal .modal-caption {
            transform: translateX(-50%) translateY(10px);
            filter: blur(3px);
            transition: transform 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                       filter 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            transition-delay: 0.3s;
            left: 50%;
        }

        #imageModal.show .modal-caption {
            transform: translateX(-50%) translateY(0);
            filter: blur(0px);
        }

        /* Prevent body scroll when modal is open */
        body.image-modal-open {
            overflow: hidden;
        }

        /* Hardware acceleration for modal elements */
        #imageModal #modalImage,
        #imageModal .modal-close-btn,
        #imageModal .modal-caption {
            backface-visibility: hidden;
            -webkit-backface-visibility: hidden;
            transform-style: preserve-3d;
        }

        /* ===== SLIDESHOW IMAGE TOOLTIP ===== */
        .slideshow-tooltip-container {
            position: relative;
        }

        .slideshow-tooltip {
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%) translateY(-8px);
            background: white;
            color: #374151;
            padding: 6px 12px;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
            font-size: 13px;
            font-weight: 500;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            filter: blur(4px);
            transition: opacity 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                       visibility 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                       transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                       filter 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            z-index: 1000;
            pointer-events: none;
        }

        /* Tooltip arrow */
        .slideshow-tooltip::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 5px solid transparent;
            border-top-color: white;
        }

        /* Tooltip arrow border */
        .slideshow-tooltip::before {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 6px solid transparent;
            border-top-color: #e5e7eb;
            z-index: -1;
        }

        /* Show tooltip on hover */
        .slideshow-tooltip-container:hover .slideshow-tooltip {
            opacity: 1;
            visibility: visible;
            filter: blur(0px);
            transform: translateX(-50%) translateY(-12px);
        }

        /* Ensure tooltip doesn't interfere with click events */
        .slideshow-image-container {
            position: relative;
            z-index: 1;
        }

        /* ===== MINIMALISTIC QUOTE ANIMATIONS ===== */
        .quote-container {
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                       transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .quote-container.animate {
            opacity: 1;
            transform: translateY(0);
        }

        .quote-text {
            opacity: 0;
            filter: blur(2px);
            transform: translateY(10px);
            transition: opacity 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                       filter 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                       transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .quote-text.animate {
            opacity: 1;
            filter: blur(0px);
            transform: translateY(0);
        }

        .quote-highlight {
            opacity: 0;
            filter: blur(3px);
            transform: scale(0.95);
            transition: opacity 0.5s cubic-bezier(0.34, 1.56, 0.64, 1),
                       filter 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                       transform 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
        }

        .quote-highlight.animate {
            opacity: 1;
            filter: blur(0px);
            transform: scale(1);
        }

        .quote-attribution {
            opacity: 0;
            transform: translateY(15px);
            transition: opacity 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                       transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .quote-attribution.animate {
            opacity: 1;
            transform: translateY(0);
        }

        /* Stagger animation delays */
        .quote-text { transition-delay: 0.2s; }
        .quote-highlight:nth-of-type(1) { transition-delay: 0.4s; }
        .quote-highlight:nth-of-type(2) { transition-delay: 0.6s; }
        .quote-highlight:nth-of-type(3) { transition-delay: 0.8s; }
        .quote-attribution { transition-delay: 1.0s; }

        /* Sentio Today Section - Minimal Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Simple hover effects for feature cards */
        #current .hover\:shadow-lg:hover {
            transform: translateY(-2px);
        }

        /* Smooth transitions */
        #current .transition-all {
            transition: all 0.3s ease;
        }

        /* Animation for section entrance */
        .animate-fade-in-up {
            animation: fadeInUp 0.8s ease-out forwards;
        }

        /* Journey Card Animations */
        .journey-card {
            opacity: 0;
            transform: translateY(60px) scale(0.95);
            transition: all 1.2s cubic-bezier(0.16, 1, 0.3, 1);
            will-change: transform, opacity;
        }

        .journey-card.animate-in {
            opacity: 1;
            transform: translateY(0) scale(1);
        }

        .journey-card-icon {
            opacity: 0;
            transform: translateY(30px) scale(0.5);
            transition: all 1.0s cubic-bezier(0.34, 1.56, 0.64, 1);
            will-change: transform, opacity;
        }

        .journey-card-icon.animate-in {
            opacity: 1;
            transform: translateY(0) scale(1);
        }

        .journey-card-title {
            opacity: 0;
            transform: translateY(25px);
            transition: all 0.8s cubic-bezier(0.16, 1, 0.3, 1);
            will-change: transform, opacity;
        }

        .journey-card-title.animate-in {
            opacity: 1;
            transform: translateY(0);
        }

        .journey-card-description {
            opacity: 0;
            transform: translateY(30px);
            transition: all 1.0s cubic-bezier(0.16, 1, 0.3, 1);
            will-change: transform, opacity;
        }

        .journey-card-description.animate-in {
            opacity: 1;
            transform: translateY(0);
        }

        .journey-card-button {
            opacity: 0;
            transform: translateY(35px) scale(0.9);
            transition: all 1.0s cubic-bezier(0.16, 1, 0.3, 1);
            will-change: transform, opacity;
        }

        .journey-card-button.animate-in {
            opacity: 1;
            transform: translateY(0) scale(1);
        }

        /* Enhanced hover effects */
        .journey-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 25px 50px rgba(54, 116, 181, 0.2);
            transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
        }

        .journey-card:hover .journey-card-icon {
            transform: translateY(-3px) scale(1.1);
        }

        .journey-card:hover .journey-card-button {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 8px 25px rgba(54, 116, 181, 0.3);
        }

        /* Journey Card Container */
        .journey-card-container {
            display: flex;
            align-items: center;
            gap: 2rem;
            transition: all 0.5s cubic-bezier(0.16, 1, 0.3, 1);
        }

        /* Journey Preview Embed - External Right Side */
        .journey-preview {
            width: 0;
            height: 350px;
            background: white;
            border-radius: 16px;
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.2),
                0 0 0 1px rgba(255, 255, 255, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            opacity: 0;
            overflow: hidden;
            transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
            backdrop-filter: blur(10px);
            transform: scale(0.9);
        }

        .journey-card-container.preview-active .journey-preview {
            width: 500px;
            opacity: 1;
            transform: scale(1);
        }

        /* Button text animation */
        .preview-trigger .button-text {
            transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
            transform: translateY(0);
            opacity: 1;
        }

        .preview-trigger.text-changing .button-text {
            transform: translateY(-20px);
            opacity: 0;
        }

        .preview-trigger .button-text-new {
            position: absolute;
            left: 50%;
            transform: translateX(-50%) translateY(20px);
            opacity: 0;
            transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
            white-space: nowrap;
            min-width: max-content;
        }

        .preview-trigger.text-changed .button-text-new {
            transform: translateX(-50%) translateY(0);
            opacity: 1;
        }

        .preview-trigger.text-changed .button-text {
            display: none;
        }

        /* Expand button width when text changes */
        .preview-trigger.text-changed {
            min-width: 200px;
            width: auto;
            padding-left: 2rem;
            padding-right: 2rem;
        }

        /* Hide arrow when text changes */
        .preview-trigger svg {
            transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
            opacity: 1;
        }

        .preview-trigger.text-changed svg {
            opacity: 0;
            transform: scale(0);
        }

        /* Real iframe preview */
        .preview-iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: white;
            border-radius: 16px;
            transform: scale(0.7);
            transform-origin: top left;
            width: 142.857%; /* 100% / 0.7 to compensate for scale */
            height: 142.857%; /* 100% / 0.7 to compensate for scale */
            pointer-events: none;
        }

        /* Enhanced hover effects for side-by-side layout */
        .journey-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 25px 50px rgba(54, 116, 181, 0.2);
            transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
        }

        .journey-card:hover .journey-card-icon {
            transform: translateY(-3px) scale(1.1);
        }

        .journey-card:hover .journey-card-button {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 8px 25px rgba(54, 116, 181, 0.3);
        }

        /* Responsive adjustments */
        @media (max-width: 1400px) {
            .journey-card-container.preview-active .journey-preview {
                width: 400px;
            }
        }

        @media (max-width: 1024px) {
            .journey-card-container {
                flex-direction: column;
                gap: 1.5rem;
            }

            .journey-card-container.preview-active .journey-preview {
                width: 100%;
                max-width: 350px;
                height: 200px;
            }
        }

        @media (max-width: 768px) {
            .journey-card-container.preview-active .journey-preview {
                max-width: 280px;
                height: 160px;
            }
        }

        /* ===== PAGE TRANSITION OVERLAY ANIMATION ===== */
        .page-transition-overlay {
            position: fixed;
            top: 0;
            right: -100vw; /* Start off-screen to the right */
            width: 100vw;
            height: 100vh;
            background: white;
            z-index: 9999; /* Above content but below nav card */
            transition: right 1.2s cubic-bezier(0.16, 1, 0.3, 1);
            overflow: hidden;
        }

        .page-transition-overlay.active {
            right: 0; /* Slide in to cover the screen */
        }

        /* Ensure nav card stays above overlay */
        .story-nav {
            z-index: 10000; /* Higher than overlay */
        }






    </style>
</head>
<body class="bg-white text-gray-800">



    <!-- Back Button -->
    <button class="back-btn" onclick="goBack()" aria-label="Go back">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
    </button>
    
    <!-- Story Navigation -->
    <nav class="story-nav">
        <div class="story-nav-item active" data-section="hero" data-tooltip="Introduction & Landing"></div>
        <div class="story-nav-item" data-section="origin" data-tooltip="The Problem That Started It All"></div>
        <div class="story-nav-item" data-section="vision" data-tooltip="The Vision & Solution"></div>
        <div class="story-nav-item" data-section="technical" data-tooltip="4-Week Development Journey"></div>
        <div class="story-nav-item" data-section="milestones" data-tooltip="Key Achievements & Progress"></div>
        <div class="story-nav-item" data-section="design" data-tooltip="Design Philosophy & Inspirations"></div>
        <div class="story-nav-item" data-section="current" data-tooltip="Final Result & Live Demo"></div>
        <div class="story-nav-item" data-section="future" data-tooltip="What's Next for Sentio"></div>
    </nav>
    
    <!-- Hero Section -->
    <section id="hero" class="min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div class="text-center max-w-4xl mx-auto">
            <div id="logo-container" class="logo-container mb-8">
                <div class="profile-picture w-32 h-32 mx-auto rounded-full border-4 border-brand shadow-lg overflow-hidden">
                    <img src="Image/Profile.jpg" alt="Josiah De Jesus" class="w-full h-full object-cover">
                </div>
            </div>
            <h1 id="logo-text" class="text-5xl md:text-6xl font-dm-sans-extrabold text-gray-900 mb-6 fade-in-element">
                The Sentio Story
            </h1>
            <p class="text-xl text-gray-600 mb-8 leading-relaxed fade-in-element">
                Ever spent hours fighting with CSS just to get something to look right? Yeah, me too.
                Here's how that frustration turned into building Sentio - my take on what e-commerce should actually feel like.
            </p>
            <div class="mt-6 max-w-lg mx-auto fade-in-element">
                <ul class="grid grid-cols-3 divide-x divide-gray-200 overflow-hidden border border-gray-200 rounded-lg">
                    <li class="stat-card flex flex-col p-3 sm:p-4 cursor-pointer transition-all duration-300 hover:bg-gray-50 rounded-l-lg">
                        <div class="stat-number text-lg sm:text-xl font-bold text-gray-800 mb-1">
                            2.5
                        </div>
                        <p class="text-xs text-gray-600">
                            years experience
                        </p>
                    </li>

                    <li class="stat-card flex flex-col p-3 sm:p-4 cursor-pointer transition-all duration-300 hover:bg-gray-50">
                        <div class="stat-number text-lg sm:text-xl font-bold text-gray-800 mb-1">
                            4
                        </div>
                        <p class="text-xs text-gray-600">
                            weeks built
                        </p>
                    </li>

                    <li class="stat-card flex flex-col p-3 sm:p-4 cursor-pointer transition-all duration-300 hover:bg-gray-50 rounded-r-lg">
                        <div class="stat-number text-lg sm:text-xl font-bold text-gray-800 mb-1">
                            100%
                        </div>
                        <p class="text-xs text-gray-600">
                            vanilla JS
                        </p>
                    </li>
                </ul>
            </div>
        </div>
    </section>


    <!-- Origin Story Section -->
    <section id="origin" class="story-section py-24 sm:py-32">
        <div class="mx-auto max-w-7xl px-6 lg:px-8 text-center">
            <p class="text-base font-semibold leading-7 text-brand-blue">THE BEGINNING</p>
            <h2 class="mt-2 text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl">The Problem That Started It All</h2>
            <p class="mt-6 text-lg leading-8 text-gray-600 max-w-3xl mx-auto">
                You know that feeling when you're 2.5 years into coding and still Googling "how to center a div"?
                Yeah, that was me. Every project turned into a CSS wrestling match that I usually lost.
            </p>

            <div class="mt-20 grid grid-cols-1 gap-y-12 sm:grid-cols-2 lg:grid-cols-3 sm:gap-x-8 lg:gap-x-16">
                <!-- Challenge 1: CSS Frustration -->
                <div class="flex flex-col items-center">
                    <div class="flex items-center justify-center h-16 w-16 rounded-full border border-gray-300 mb-6">
                        <lord-icon
                            id="cssNightmaresIcon"
                            src="https://cdn.lordicon.com/lrubprlz.json"
                            trigger="hover"
                            colors="primary:#3674B5"
                            style="width:32px;height:32px;opacity:0;transition:opacity 0.3s ease">
                        </lord-icon>
                    </div>
                    <h3 class="text-xl font-semibold leading-7 text-black">CSS Nightmares</h3>
                    <p class="mt-2 text-base leading-7 text-gray-600 text-center">
                        Seriously, I'd spend 3 hours trying to get a button to look right.
                        My browser's inspect element was basically my second home.
                    </p>
                </div>

                <!-- Challenge 2: Endless Research -->
                <div class="flex flex-col items-center">
                    <div class="flex items-center justify-center h-16 w-16 rounded-full border border-gray-300 mb-6">
                        <lord-icon
                            id="inspirationIcon"
                            src="https://cdn.lordicon.com/zhtsepgu.json"
                            trigger="hover"
                            colors="primary:#3674B5"
                            style="width:32px;height:32px;opacity:0;transition:opacity 0.3s ease">
                        </lord-icon>
                    </div>
                    <h3 class="text-xl font-semibold leading-7 text-black">Inspiration Overload</h3>
                    <p class="mt-2 text-base leading-7 text-gray-600 text-center">
                        I'd lose entire afternoons on Behance and Dribbble, bookmarking designs I'd never recreate.
                        Classic procrastination disguised as "research."
                    </p>
                </div>

                <!-- Challenge 3: Reinventing the Wheel -->
                <div class="flex flex-col items-center">
                    <div class="flex items-center justify-center h-16 w-16 rounded-full border border-gray-300 mb-6">
                        <lord-icon
                            id="copyPasteIcon"
                            src="https://cdn.lordicon.com/wwcdwkaf.json"
                            trigger="hover"
                            colors="primary:#3674B5"
                            style="width:32px;height:32px;opacity:0;transition:opacity 0.3s ease">
                        </lord-icon>
                    </div>
                    <h3 class="text-xl font-semibold leading-7 text-black">Copy-Paste Chaos</h3>
                    <p class="mt-2 text-base leading-7 text-gray-600 text-center">
                        Every new project meant starting from zero. Copy-pasting code snippets from old projects
                        and hoping they'd work. Spoiler: they rarely did.
                    </p>
                </div>
            </div>

            <!-- Quote Section -->
            <div class="mt-20">
                <div class="mx-auto max-w-4xl text-center">
                    <blockquote class="text-3xl md:text-4xl font-bold tracking-tight text-black leading-tight">
                        "I was spending more time fighting with CSS than actually building cool stuff.
                        Something had to change."
                    </blockquote>
                    <div class="mt-6">
                        <p class="text-base font-semibold leading-7 text-brand-blue">THE LIGHTBULB MOMENT</p>
                    </div>
                </div>
            </div>

            <!-- Transition to next section -->
            <div class="mt-16 text-center">
                <p class="text-lg leading-8 text-gray-600 max-w-3xl mx-auto">
                    That's when I started looking at the e-commerce world differently.
                    What if there was a way to build something that actually made sense?
                </p>
            </div>
        </div>
    </section>

    <!-- The Vision Section -->
    <section id="vision" class="story-section py-24 sm:py-32 bg-gray-50">
        <div class="mx-auto max-w-6xl px-6 lg:px-8">
            <!-- Header -->
            <div class="text-center mb-16">
                <div class="inline-flex items-center px-4 py-2 rounded-full bg-brand-blue/10 border border-brand-blue/20 mb-6">
                    <span class="text-sm font-medium text-brand-blue">THE VISION</span>
                </div>
                <h2 class="text-3xl md:text-4xl font-bold text-black mb-6">E-commerce Reimagined</h2>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
                    Looking at the e-commerce landscape, I saw a sea of vibrant chaos. I envisioned something different:
                    a platform that felt alive yet remained elegantly minimal.
                </p>
            </div>

            <!-- Before vs After Comparison -->
            <div class="relative">
                <!-- Background decoration -->
                <div class="absolute inset-0 bg-gradient-to-r from-red-50/30 via-transparent to-blue-50/30 rounded-3xl"></div>

                <div class="relative grid lg:grid-cols-2 gap-8 lg:gap-16 mb-20 items-stretch">
                    <!-- Before: My Frustrations -->
                    <div class="group">
                        <div class="bg-white rounded-3xl p-8 lg:p-10 border border-gray-200 shadow-sm hover:shadow-lg transition-all duration-300 relative overflow-hidden h-full">
                            <!-- Decorative element -->
                            <div class="absolute top-0 right-0 w-32 h-32 bg-red-50 rounded-full -translate-y-16 translate-x-16"></div>

                            <div class="relative">
                                <div class="flex items-center mb-6">
                                    <div class="w-12 h-12 bg-red-100 rounded-2xl flex items-center justify-center mr-4">
                                        <lord-icon
                                            src="https://cdn.lordicon.com/wjyqkiew.json"
                                            trigger="morph"
                                            stroke="bold"
                                            state="morph-cross"
                                            colors="primary:#e83a30,secondary:#e83a30"
                                            style="width:28px;height:28px">
                                        </lord-icon>
                                    </div>
                                    <div>
                                        <span class="bg-red-100 text-red-700 px-3 py-1 rounded-full text-sm font-semibold">What I Observed</span>
                                        <h3 class="text-2xl font-bold text-black mt-2">E-commerce Sites I Found</h3>
                                    </div>
                                </div>

                                <div class="space-y-4">
                                    <div class="flex items-start group/item hover:cursor-pointer">
                                        <div>
                                            <p class="font-semibold text-gray-900 group-hover/item:text-red-600 transition-colors duration-200">Analyzed 25+ e-commerce sites - they looked like digital yard sales</p>
                                            <p class="text-sm text-gray-600 mt-1 group-hover/item:text-red-500 transition-colors duration-200">Seriously, Amazon has like 50 different font sizes and colors on one page. My eyes didn't know where to look.</p>
                                        </div>
                                    </div>

                                    <div class="flex items-start group/item hover:cursor-pointer">
                                        <div>
                                            <p class="font-semibold text-gray-900 group-hover/item:text-red-600 transition-colors duration-200">Found sites still stuck in 2015 with heavy gradients and shadows</p>
                                            <p class="text-sm text-gray-600 mt-1 group-hover/item:text-red-500 transition-colors duration-200">eBay, Walmart, most big retailers still use chunky buttons and outdated layouts that scream 'old internet.'</p>
                                        </div>
                                    </div>

                                    <div class="flex items-start group/item hover:cursor-pointer">
                                        <div>
                                            <p class="font-semibold text-gray-900 group-hover/item:text-red-600 transition-colors duration-200">Every site used 10+ random colors with zero design consistency</p>
                                            <p class="text-sm text-gray-600 mt-1 group-hover/item:text-red-500 transition-colors duration-200">One site had neon green buttons, bright red sale tags, blue headers - it was visual chaos with no brand identity.</p>
                                        </div>
                                    </div>

                                    <div class="flex items-start group/item hover:cursor-pointer">
                                        <div>
                                            <p class="font-semibold text-gray-900 group-hover/item:text-red-600 transition-colors duration-200">Basic features were scattered across 6 different pages and menus</p>
                                            <p class="text-sm text-gray-600 mt-1 group-hover/item:text-red-500 transition-colors duration-200">Want to see your orders? Click here. Track shipping? Different page. Update payment? Another menu. Nothing was intuitive.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- After: My Solution -->
                    <div class="group">
                        <div class="bg-white rounded-3xl p-8 lg:p-10 border border-brand-blue/30 shadow-sm hover:shadow-lg transition-all duration-300 relative overflow-hidden h-full">
                            <!-- Decorative element -->
                            <div class="absolute top-0 right-0 w-32 h-32 bg-blue-50 rounded-full -translate-y-16 translate-x-16"></div>

                            <div class="relative">
                                <div class="flex items-center mb-6">
                                    <div class="w-12 h-12 bg-blue-100 rounded-2xl flex items-center justify-center mr-4">
                                        <lord-icon
                                            src="https://cdn.lordicon.com/rxgzsafd.json"
                                            trigger="hover"
                                            colors="primary:#3674b5"
                                            style="width:28px;height:28px">
                                        </lord-icon>
                                    </div>
                                    <div>
                                        <span class="bg-blue-100 text-brand-blue px-3 py-1 rounded-full text-sm font-semibold">My Solution</span>
                                        <h3 class="text-2xl font-bold text-black mt-2">How Sentio Solved This</h3>
                                    </div>
                                </div>

                                <div class="space-y-4">
                                    <div class="flex items-start group/item hover:cursor-pointer">
                                        <div>
                                            <p class="font-semibold text-gray-900 transition-colors duration-200 group-hover/item:text-[#3674B5]">Designed Sentio like modern SaaS apps - clean, minimal, purposeful</p>
                                            <p class="text-sm text-gray-600 mt-1 transition-colors duration-200 group-hover/item:text-[#2563EB]">Took inspiration from Stripe, Notion, and Linear. Every element has breathing room and serves a purpose.</p>
                                        </div>
                                    </div>

                                    <div class="flex items-start group/item hover:cursor-pointer">
                                        <div>
                                            <p class="font-semibold text-gray-900 transition-colors duration-200 group-hover/item:text-[#3674B5]">Used only 3 core colors with consistent spacing throughout</p>
                                            <p class="text-sm text-gray-600 mt-1 transition-colors duration-200 group-hover/item:text-[#2563EB]">Brand blue (#3674B5), clean grays, and white. Plus 8px grid system like professional design systems.</p>
                                        </div>
                                    </div>

                                    <div class="flex items-start group/item hover:cursor-pointer">
                                        <div>
                                            <p class="font-semibold text-gray-900 transition-colors duration-200 group-hover/item:text-[#3674B5]">Built everything into one unified dashboard experience</p>
                                            <p class="text-sm text-gray-600 mt-1 transition-colors duration-200 group-hover/item:text-[#2563EB]">Orders, inventory, analytics, customer data - all in one place. No more hunting through 10 different pages.</p>
                                        </div>
                                    </div>

                                    <div class="flex items-start group/item hover:cursor-pointer">
                                        <div>
                                            <p class="font-semibold text-gray-900 transition-colors duration-200 group-hover/item:text-[#3674B5]">Applied React-style component thinking to every interface</p>
                                            <p class="text-sm text-gray-600 mt-1 transition-colors duration-200 group-hover/item:text-[#2563EB]">Reusable buttons, consistent cards, predictable layouts. It feels like using a professional tool, not a cluttered marketplace.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


            </div>

            <!-- The Sentio Philosophy -->
            <div class="text-center mb-24">
                <!-- Section Header -->
                <div class="mb-16">
                    <h3 class="text-3xl font-bold text-gray-900 mb-4 tracking-tight">What Makes Sentio Actually Different</h3>
                    <div class="w-16 h-0.5 bg-brand-blue mx-auto"></div>
                </div>

                <!-- Why Story Introduction -->
                <div class="max-w-4xl mx-auto mb-20">
                    <p class="text-xl font-light text-gray-600 leading-relaxed mb-8">
                        I spent some time exploring <strong class="font-medium text-gray-900">25+ e-commerce sites</strong> to understand what's working well in the current landscape.
                        I noticed some common patterns: feature-rich interfaces, detailed product information, and comprehensive checkout flows that serve different user needs.
                    </p>
                    <p class="text-xl font-light text-gray-600 leading-relaxed">
                        <strong class="font-medium text-brand-blue">This research helped me think about what approach might work for Sentio.</strong> Here's what I'm focusing on:
                    </p>
                </div>

                <div class="grid md:grid-cols-3 gap-8 lg:gap-12">
                    <div class="group relative bg-white rounded-3xl p-8 border border-gray-200 shadow-[0_4px_20px_rgb(0,0,0,0.03)] hover:shadow-[0_8px_30px_rgb(0,0,0,0.08)] transition-all duration-500">
                        <div class="w-14 h-14 bg-brand-blue/10 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:bg-brand-blue/15 transition-colors duration-300">
                            <lord-icon
                                src="https://cdn.lordicon.com/apgkpdeb.json"
                                trigger="hover"
                                colors="primary:#3674b5"
                                style="width:32px;height:32px">
                            </lord-icon>
                        </div>
                        <h4 class="font-bold text-gray-900 mb-4 text-lg group-hover:text-brand-blue transition-colors duration-300">Optimized Performance</h4>
                        <p class="text-gray-600 leading-relaxed font-light">I noticed many sites have load times around 4-6 seconds, so I focused on optimization. Sentio loads in <strong class="font-medium text-gray-900">1.8 seconds</strong> using vanilla JavaScript, which keeps things lightweight and responsive.</p>
                    </div>
                    <div class="group relative bg-white rounded-3xl p-8 border border-gray-200 shadow-[0_4px_20px_rgb(0,0,0,0.03)] hover:shadow-[0_8px_30px_rgb(0,0,0,0.08)] transition-all duration-500">
                        <div class="w-14 h-14 bg-brand-blue/10 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:bg-brand-blue/15 transition-colors duration-300">
                            <lord-icon
                                src="https://cdn.lordicon.com/rxgzsafd.json"
                                trigger="hover"
                                colors="primary:#3674b5"
                                style="width:32px;height:32px">
                            </lord-icon>
                        </div>
                        <h4 class="font-bold text-gray-900 mb-4 text-lg group-hover:text-brand-blue transition-colors duration-300">Streamlined Checkout</h4>
                        <p class="text-gray-600 leading-relaxed font-light">In future additions, I'll implement <strong class="font-medium text-gray-900">simple, easy steps</strong> to make checkout even better and faster. I noticed many sites use 7+ step processes, so I'm planning a streamlined approach with helpful form validation to guide users smoothly.</p>
                    </div>
                    <div class="group relative bg-white rounded-3xl p-8 border border-gray-200 shadow-[0_4px_20px_rgb(0,0,0,0.03)] hover:shadow-[0_8px_30px_rgb(0,0,0,0.08)] transition-all duration-500">
                        <div class="w-14 h-14 bg-brand-blue/10 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:bg-brand-blue/15 transition-colors duration-300">
                            <lord-icon
                                src="https://cdn.lordicon.com/jectmwqf.json"
                                trigger="hover"
                                stroke="bold"
                                colors="primary:#3674b5,secondary:#3674b5"
                                style="width:32px;height:32px">
                            </lord-icon>
                        </div>
                        <h4 class="font-bold text-gray-900 mb-4 text-lg group-hover:text-brand-blue transition-colors duration-300">Modular Architecture</h4>
                        <p class="text-gray-600 leading-relaxed font-light">I wanted to avoid repetitive coding, so I built everything as reusable components. This approach lets me reuse <strong class="font-medium text-gray-900">70% of my code</strong> across different pages, making future development and maintenance much more efficient.</p>
                    </div>
                </div>
            </div>

            <!-- The Sentio Difference -->
            <div class="text-center mt-24">
                <div class="max-w-6xl mx-auto">
                    <!-- Enhanced Section Header -->
                    <div class="mb-16">
                        <span class="text-brand-blue font-semibold text-sm uppercase tracking-wider mb-4 block">My Approach</span>
                        <h3 class="text-2xl font-bold text-gray-900 mb-4">What I'm Trying Different</h3>
                        <div class="w-12 h-0.5 bg-brand-blue mx-auto"></div>
                    </div>

                    <!-- Enhanced Comparison Cards -->
                    <div class="grid md:grid-cols-2 gap-8 lg:gap-12 mb-16">
                        <!-- Common Patterns Card -->
                        <div class="group relative bg-white rounded-3xl p-8 border border-gray-200 shadow-[0_4px_20px_rgb(0,0,0,0.03)] hover:shadow-[0_8px_30px_rgb(0,0,0,0.08)] transition-all duration-500">
                            <!-- Header with Icon -->
                            <div class="flex items-center mb-6">
                                <div class="w-10 h-10 bg-gray-100 rounded-xl flex items-center justify-center mr-4">
                                    <lord-icon
                                        src="https://cdn.lordicon.com/snxksidl.json"
                                        trigger="hover"
                                        stroke="bold"
                                        colors="primary:#000000,secondary:#000000"
                                        style="width:24px;height:24px">
                                    </lord-icon>
                                </div>
                                <h4 class="text-xl font-bold text-gray-900">What I Observed</h4>
                            </div>

                            <!-- Enhanced List -->
                            <ul class="space-y-5 text-left">
                                <li class="flex items-start group/item">
                                    <div class="w-6 h-6 bg-gray-100 rounded-lg flex items-center justify-center mt-0.5 mr-4 flex-shrink-0">
                                        <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
                                    </div>
                                    <span class="text-gray-600 font-light leading-relaxed">Feature-rich interfaces with lots of options</span>
                                </li>
                                <li class="flex items-start group/item">
                                    <div class="w-6 h-6 bg-gray-100 rounded-lg flex items-center justify-center mt-0.5 mr-4 flex-shrink-0">
                                        <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
                                    </div>
                                    <span class="text-gray-600 font-light leading-relaxed">Detailed multi-step checkout flows</span>
                                </li>
                                <li class="flex items-start group/item">
                                    <div class="w-6 h-6 bg-gray-100 rounded-lg flex items-center justify-center mt-0.5 mr-4 flex-shrink-0">
                                        <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
                                    </div>
                                    <span class="text-gray-600 font-light leading-relaxed">Comprehensive framework setups</span>
                                </li>
                            </ul>
                        </div>

                        <!-- Sentio's Approach Card -->
                        <div class="group relative bg-gradient-to-br from-brand-blue/5 to-brand-blue/10 rounded-3xl p-8 border border-brand-blue/20 shadow-[0_4px_20px_rgb(54,116,181,0.08)] hover:shadow-[0_8px_30px_rgb(54,116,181,0.15)] transition-all duration-500">
                            <!-- Header with Icon -->
                            <div class="flex items-center mb-6">
                                <div class="w-10 h-10 bg-brand-blue/15 rounded-xl flex items-center justify-center mr-4">
                                    <lord-icon
                                        src="https://cdn.lordicon.com/jectmwqf.json"
                                        trigger="hover"
                                        stroke="bold"
                                        colors="primary:#3674b5,secondary:#3674b5"
                                        style="width:24px;height:24px">
                                    </lord-icon>
                                </div>
                                <h4 class="text-xl font-bold text-gray-900">My Direction with Sentio</h4>
                            </div>

                            <!-- Enhanced List -->
                            <ul class="space-y-5 text-left">
                                <li class="flex items-start group/item">
                                    <div class="w-6 h-6 bg-brand-blue/20 rounded-lg flex items-center justify-center mt-0.5 mr-4 flex-shrink-0">
                                        <div class="w-2 h-2 bg-brand-blue rounded-full"></div>
                                    </div>
                                    <span class="text-gray-700 font-light leading-relaxed">Focus on what users actually need</span>
                                </li>
                                <li class="flex items-start group/item">
                                    <div class="w-6 h-6 bg-brand-blue/20 rounded-lg flex items-center justify-center mt-0.5 mr-4 flex-shrink-0">
                                        <div class="w-2 h-2 bg-brand-blue rounded-full"></div>
                                    </div>
                                    <span class="text-gray-700 font-light leading-relaxed">Keep things simple and intuitive</span>
                                </li>
                                <li class="flex items-start group/item">
                                    <div class="w-6 h-6 bg-brand-blue/20 rounded-lg flex items-center justify-center mt-0.5 mr-4 flex-shrink-0">
                                        <div class="w-2 h-2 bg-brand-blue rounded-full"></div>
                                    </div>
                                    <span class="text-gray-700 font-light leading-relaxed">Build lightweight and fast</span>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <!-- Enhanced Result Card -->
                    <div class="relative bg-gradient-to-br from-white to-gray-50/50 rounded-3xl p-12 border border-gray-200 shadow-[0_8px_40px_rgb(0,0,0,0.06)] overflow-hidden">
                        <!-- Background Pattern -->
                        <div class="absolute inset-0 opacity-5">
                            <div class="absolute top-0 left-0 w-32 h-32 bg-brand-blue rounded-full -translate-x-16 -translate-y-16"></div>
                            <div class="absolute bottom-0 right-0 w-24 h-24 bg-brand-blue rounded-full translate-x-12 translate-y-12"></div>
                        </div>

                        <div class="relative text-center">
                            <!-- Enhanced Icon -->
                            <div class="w-16 h-16 bg-gradient-to-br from-brand-blue/10 to-brand-blue/20 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-lg">
                                <lord-icon
                                    src="https://cdn.lordicon.com/weqkkuwt.json"
                                    trigger="hover"
                                    stroke="bold"
                                    colors="primary:#3674b5,secondary:#3674b5"
                                    style="width:36px;height:36px">
                                </lord-icon>
                            </div>

                            <!-- Enhanced Typography -->
                            <h5 class="text-2xl font-bold text-gray-900 mb-6">What I'm Aiming For</h5>
                            <p class="text-lg text-gray-600 leading-relaxed font-light mb-8 max-w-3xl mx-auto">
                                A platform that feels <span class="font-medium text-brand-blue">natural and responsive</span>. Something that loads quickly, makes purchasing straightforward, and has a structure that lets me add cool features down the road.
                            </p>

                            <!-- Future Vision -->
                            <div class="bg-white/80 rounded-2xl p-6 max-w-2xl mx-auto border border-gray-100">
                                <p class="text-sm text-gray-600 font-light leading-relaxed">
                                    <span class="font-medium text-gray-900">Built for Tomorrow:</span> The architecture is flexible enough to grow with new ideas - whether that's AI features, better analytics, or whatever interesting tech comes next.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Technical Challenge Section -->
    <section id="technical" class="story-section py-24 sm:py-32">
        <div class="mx-auto max-w-6xl px-6 lg:px-8">
            <!-- Header -->
            <div class="text-center mb-20">
                <div class="inline-flex items-center px-4 py-2 rounded-full bg-brand-blue/10 border border-brand-blue/20 mb-6">
                    <span class="text-sm font-medium text-brand-blue">THE JOURNEY</span>
                </div>
                <h2 class="text-3xl md:text-4xl font-bold text-black mb-6">The Technical Challenge</h2>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
                    Four weeks of pushing my 2.5 years of experience to the limit. Here's what I learned when I decided to build something bigger than anything I'd attempted before.
                </p>
            </div>

            <!-- Timeline Container -->
            <div class="timeline-container relative max-w-4xl mx-auto mb-20">
                <!-- Timeline Background Line -->
                <div class="timeline-background"></div>
                <!-- Timeline Progress Line -->
                <div class="timeline-progress" id="timeline-progress"></div>

                <!-- Timeline Items -->
                <div class="space-y-16">
                    <!-- Week 1: CSS Wrestling Match -->
                    <div class="relative flex items-center">
                        <!-- Timeline Dot -->
                        <div class="absolute left-1/2 transform -translate-x-1/2 w-6 h-6 bg-brand-blue rounded-full border-4 border-white shadow-lg z-10 flex items-center justify-center">
                            <div class="w-2 h-2 bg-white rounded-full"></div>
                        </div>

                        <!-- Content (Left Side) -->
                        <div class="w-1/2 pr-12">
                            <div class="week-card week-card-hidden week-card-left relative bg-gradient-to-br from-white to-blue-50/30 rounded-2xl p-6 shadow-[0_6px_24px_rgba(59,130,246,0.10)] border border-blue-100/50 ml-auto max-w-sm backdrop-blur-sm overflow-hidden group hover:shadow-[0_8px_32px_rgba(59,130,246,0.15)] transition-all duration-500">
                                <!-- Decorative Background Elements -->
                                <div class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-blue-100/30 to-transparent rounded-full -translate-y-6 translate-x-6"></div>
                                <div class="absolute bottom-0 left-0 w-14 h-14 bg-gradient-to-tr from-blue-50/50 to-transparent rounded-full translate-y-3 -translate-x-3"></div>

                                <!-- Week Badge -->
                                <div class="relative inline-flex items-center px-3 py-1.5 rounded-full border border-gray-300 text-xs mb-5" style="color: #3674B5; font-family: ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'; font-weight: normal; font-variant-numeric: tabular-nums; letter-spacing: 0.3px; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;">
                                    Week 1
                                </div>

                                <h4 class="text-lg font-bold text-gray-900 mb-4 relative z-10">The CSS Wrestling Match</h4>

                                <div class="space-y-3 relative z-10">
                                    <p class="text-xs text-gray-700 leading-relaxed font-medium mb-3">
                                        <strong class="text-gray-900">Honestly?</strong> I spent 3 whole days just trying to get a simple dropdown menu to work. Every time I "fixed" something, two other things would break. It was like playing whack-a-mole with CSS.
                                    </p>

                                    <div class="bg-gradient-to-r from-blue-50 to-blue-100/50 rounded-lg p-3 border-l-3 border-blue-500 shadow-sm mb-3">
                                        <p class="text-xs italic font-medium" style="color: #3674B5;">"I'm pretty sure I right-clicked 'Inspect Element' more than I actually wrote code that week. Chrome DevTools was basically my home."</p>
                                    </div>

                                    <div class="bg-gradient-to-r from-blue-100 to-blue-50 rounded-lg p-3 border border-blue-200 shadow-sm">
                                        <p class="text-xs font-bold mb-1" style="color: #3674B5;">The lightbulb moment:</p>
                                        <p class="text-xs" style="color: #3674B5;">I finally stopped copy-pasting from Stack Overflow and actually learned how CSS Grid and Flexbox work. Game changer.</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Empty Right Side -->
                        <div class="w-1/2 pl-8"></div>
                    </div>

                    <!-- Week 2: Building Without Frameworks -->
                    <div class="relative flex items-center">
                        <!-- Timeline Dot -->
                        <div class="absolute left-1/2 transform -translate-x-1/2 w-6 h-6 bg-brand-blue rounded-full border-4 border-white shadow-lg z-10 flex items-center justify-center">
                            <div class="w-2 h-2 bg-white rounded-full"></div>
                        </div>

                        <!-- Empty Left Side -->
                        <div class="w-1/2 pr-12"></div>

                        <!-- Content (Right Side) -->
                        <div class="w-1/2 pl-12">
                            <div class="week-card week-card-hidden week-card-right relative bg-gradient-to-bl from-white to-blue-50/30 rounded-2xl p-6 shadow-[0_6px_24px_rgba(59,130,246,0.10)] border border-blue-100/50 max-w-sm backdrop-blur-sm overflow-hidden group hover:shadow-[0_8px_32px_rgba(59,130,246,0.15)] transition-all duration-500">
                                <!-- Decorative Background Elements -->
                                <div class="absolute top-0 left-0 w-18 h-18 bg-gradient-to-br from-blue-100/30 to-transparent rounded-full -translate-y-5 -translate-x-5"></div>
                                <div class="absolute bottom-0 right-0 w-16 h-16 bg-gradient-to-tl from-blue-50/50 to-transparent rounded-full translate-y-4 translate-x-4"></div>

                                <!-- Week Badge -->
                                <div class="relative inline-flex items-center px-3 py-1.5 rounded-full border border-gray-300 text-xs mb-5" style="color: #3674B5; font-family: ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'; font-weight: normal; font-variant-numeric: tabular-nums; letter-spacing: 0.3px; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;">
                                    Week 2
                                </div>

                                <h4 class="text-lg font-bold text-gray-900 mb-4 relative z-10">Building Without Frameworks</h4>

                                <div class="space-y-3 relative z-10">
                                    <p class="text-xs text-gray-700 leading-relaxed font-medium mb-3">
                                        <strong class="text-gray-900">Everyone kept saying:</strong> "Dude, just use React!" But I'm stubborn. I wanted to actually understand what's happening behind all the magic before jumping into frameworks.
                                    </p>

                                    <div class="bg-gradient-to-r from-blue-50 to-blue-100/50 rounded-lg p-3 border-l-3 border-blue-500 shadow-sm mb-3">
                                        <p class="text-xs italic font-medium" style="color: #3674B5;">"So I built my own little component system with vanilla JS. It's definitely not React, but hey - it works and I know exactly what every line does! When it works, don't touch it :D"</p>
                                    </div>

                                    <div class="bg-gradient-to-r from-blue-100 to-blue-50 rounded-lg p-3 border border-blue-200 shadow-sm">
                                        <p class="text-xs font-bold mb-1" style="color: #3674B5;">Plot twist:</p>
                                        <p class="text-xs" style="color: #3674B5;">Turns out organizing code into reusable pieces isn't rocket science. But once it works? I'm never touching that code again. It's sacred now.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Week 3: Interactive Demo -->
                    <div class="relative flex items-center">
                        <!-- Timeline Dot -->
                        <div class="absolute left-1/2 transform -translate-x-1/2 w-6 h-6 bg-brand-blue rounded-full border-4 border-white shadow-lg z-10 flex items-center justify-center">
                            <div class="w-2 h-2 bg-white rounded-full"></div>
                        </div>

                        <!-- Content (Left Side) -->
                        <div class="w-1/2 pr-12">
                            <div class="week-card week-card-hidden week-card-left relative bg-gradient-to-br from-white to-blue-50/30 rounded-2xl p-6 shadow-[0_6px_24px_rgba(59,130,246,0.10)] border border-blue-100/50 ml-auto max-w-sm backdrop-blur-sm overflow-hidden group hover:shadow-[0_8px_32px_rgba(59,130,246,0.15)] transition-all duration-500">
                                <!-- Decorative Background Elements -->
                                <div class="absolute top-0 right-0 w-19 h-19 bg-gradient-to-br from-blue-100/30 to-transparent rounded-full -translate-y-6 translate-x-6"></div>
                                <div class="absolute bottom-0 left-0 w-12 h-12 bg-gradient-to-tr from-blue-50/50 to-transparent rounded-full translate-y-2 -translate-x-2"></div>

                                <!-- Week Badge -->
                                <div class="relative inline-flex items-center px-3 py-1.5 rounded-full border border-gray-300 text-xs mb-5" style="color: #3674B5; font-family: ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'; font-weight: normal; font-variant-numeric: tabular-nums; letter-spacing: 0.3px; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;">
                                    Week 3
                                </div>

                                <h4 class="text-lg font-bold text-gray-900 mb-4 relative z-10">The Interactive Demo</h4>

                                <div class="space-y-3 relative z-10">
                                    <p class="text-xs text-gray-700 leading-relaxed font-medium mb-3">
                                        <strong class="text-gray-900">My brilliant idea:</strong> "Why show boring screenshots when I can build a whole interactive demo?" Famous last words. This took me down a rabbit hole that lasted way longer than I planned.
                                    </p>

                                    <div class="bg-gradient-to-r from-blue-50 to-blue-100/50 rounded-lg p-3 border-l-3 border-blue-500 shadow-sm mb-3">
                                        <p class="text-xs italic font-medium" style="color: #3674B5;">"I wanted people to actually click around and feel what shopping on Sentio would be like. Because apparently I love making things harder for myself."</p>
                                    </div>

                                    <div class="bg-gradient-to-r from-blue-100 to-blue-50 rounded-lg p-3 border border-blue-200 shadow-sm">
                                        <p class="text-xs font-bold mb-1" style="color: #3674B5;">Worth it though:</p>
                                        <p class="text-xs" style="color: #3674B5;">Built a working product catalog with cart functionality that actually feels legit. People can add stuff to cart and everything!</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Empty Right Side -->
                        <div class="w-1/2 pl-8"></div>
                    </div>

                    <!-- Week 4: Making It Fast -->
                    <div class="relative flex items-center">
                        <!-- Timeline Dot -->
                        <div class="absolute left-1/2 transform -translate-x-1/2 w-6 h-6 bg-brand-blue rounded-full border-4 border-white shadow-lg z-10 flex items-center justify-center">
                            <div class="w-2 h-2 bg-white rounded-full"></div>
                        </div>

                        <!-- Empty Left Side -->
                        <div class="w-1/2 pr-12"></div>

                        <!-- Content (Right Side) -->
                        <div class="w-1/2 pl-12">
                            <div class="week-card week-card-hidden week-card-right relative bg-gradient-to-bl from-white to-blue-50/30 rounded-2xl p-6 shadow-[0_6px_24px_rgba(59,130,246,0.10)] border border-blue-100/50 max-w-sm backdrop-blur-sm overflow-hidden group hover:shadow-[0_8px_32px_rgba(59,130,246,0.15)] transition-all duration-500">
                                <!-- Decorative Background Elements -->
                                <div class="absolute top-0 left-0 w-17 h-17 bg-gradient-to-br from-blue-100/30 to-transparent rounded-full -translate-y-4 -translate-x-4"></div>
                                <div class="absolute bottom-0 right-0 w-14 h-14 bg-gradient-to-tl from-blue-50/50 to-transparent rounded-full translate-y-3 translate-x-3"></div>

                                <!-- Week Badge -->
                                <div class="relative inline-flex items-center px-3 py-1.5 rounded-full border border-gray-300 text-xs mb-5" style="color: #3674B5; font-family: ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'; font-weight: normal; font-variant-numeric: tabular-nums; letter-spacing: 0.3px; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;">
                                    Week 4
                                </div>

                                <h4 class="text-lg font-bold text-gray-900 mb-4 relative z-10">Making It Actually Fast</h4>

                                <div class="space-y-3 relative z-10">
                                    <p class="text-xs text-gray-700 leading-relaxed font-medium mb-3">
                                        <strong class="text-gray-900">Reality check time:</strong> My site looked amazing but loaded like it was running on dial-up. I was aiming for that sweet 1.8-second load time, but I was getting more like... let's not talk about it.
                                    </p>

                                    <div class="bg-gradient-to-r from-blue-50 to-blue-100/50 rounded-lg p-3 border-l-3 border-blue-500 shadow-sm mb-3">
                                        <p class="text-xs italic font-medium" style="color: #3674B5;">"Turns out you can't just throw every cool animation and high-res image at a website and expect it to fly. Who would've thought?"</p>
                                    </div>

                                    <div class="bg-gradient-to-r from-blue-100 to-blue-50 rounded-lg p-3 border border-blue-200 shadow-sm">
                                        <p class="text-xs font-bold mb-1" style="color: #3674B5;">Hard truth:</p>
                                        <p class="text-xs" style="color: #3674B5;">Sometimes the best code is the code you don't write. Less really can be more when it comes to performance.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Technology Stack -->
            <div class="text-center mb-16">
                <span class="text-brand-blue font-semibold text-sm uppercase tracking-wider mb-4 block">TECH STACK</span>
                <h3 class="text-2xl font-bold text-gray-900 mb-4">What I Actually Used</h3>
                <div class="w-12 h-0.5 bg-brand-blue mx-auto mb-8"></div>
            </div>

            <div class="grid md:grid-cols-3 gap-6 mb-16">
                <div class="group relative bg-white rounded-3xl p-6 border border-gray-200 shadow-[0_4px_20px_rgb(0,0,0,0.03)] hover:shadow-[0_8px_30px_rgb(0,0,0,0.08)] transition-all duration-500 text-center">
                    <div class="w-12 h-12 bg-brand-blue/10 rounded-xl flex items-center justify-center mx-auto mb-4">
                        <lord-icon
                            src="https://cdn.lordicon.com/uonachts.json"
                            trigger="hover"
                            stroke="bold"
                            colors="primary:#3674b5,secondary:#3674b5"
                            style="width:32px;height:32px">
                        </lord-icon>
                    </div>
                    <h4 class="font-bold text-gray-900 mb-2">Vanilla JavaScript</h4>
                    <p class="text-gray-600 text-sm font-light">No frameworks, no libraries - just me and pure JS figuring things out the hard way</p>
                </div>

                <div class="group relative bg-white rounded-3xl p-6 border border-gray-200 shadow-[0_4px_20px_rgb(0,0,0,0.03)] hover:shadow-[0_8px_30px_rgb(0,0,0,0.08)] transition-all duration-500 text-center">
                    <div class="w-12 h-12 bg-brand-blue/10 rounded-xl flex items-center justify-center mx-auto mb-4">
                        <span class="text-brand-blue font-bold text-lg transition-all duration-300 ease-in-out hover:scale-110 hover:rotate-3">TW</span>
                    </div>
                    <h4 class="font-bold text-gray-900 mb-2">Tailwind CSS</h4>
                    <p class="text-gray-600 text-sm font-light">The first CSS framework that didn't make me want to throw my laptop out the window</p>
                </div>

                <div class="group relative bg-white rounded-3xl p-6 border border-gray-200 shadow-[0_4px_20px_rgb(0,0,0,0.03)] hover:shadow-[0_8px_30px_rgb(0,0,0,0.08)] transition-all duration-500 text-center">
                    <div class="w-12 h-12 bg-brand-blue/10 rounded-xl flex items-center justify-center mx-auto mb-4">
                        <lord-icon
                            src="https://cdn.lordicon.com/atzcyedn.json"
                            trigger="hover"
                            colors="primary:#3674b5"
                            style="width:28px;height:28px">
                        </lord-icon>
                    </div>
                    <h4 class="font-bold text-gray-900 mb-2">LordIcon</h4>
                    <p class="text-gray-600 text-sm font-light">Because static icons are boring and these little animations make everything feel alive</p>
                </div>
            </div>

            <!-- Key Insight -->
            <div class="relative bg-gradient-to-br from-white to-gray-50/50 rounded-2xl px-8 py-6 border border-gray-200 shadow-[0_8px_40px_rgb(0,0,0,0.06)] overflow-hidden">
                <!-- Background Pattern -->
                <div class="absolute inset-0 opacity-5">
                    <div class="absolute top-0 left-0 w-24 h-24 bg-brand-blue rounded-full -translate-x-12 -translate-y-12"></div>
                    <div class="absolute bottom-0 right-0 w-20 h-20 bg-brand-blue rounded-full translate-x-10 translate-y-10"></div>
                </div>

                <div class="relative text-center">
                    <!-- Enhanced Icon -->
                    <div class="w-12 h-12 bg-gradient-to-br from-brand-blue/10 to-brand-blue/20 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                        <lord-icon
                            src="https://cdn.lordicon.com/rrbmabsx.json"
                            trigger="morph"
                            stroke="bold"
                            state="morph-open"
                            colors="primary:#3674b5,secondary:#3674b5"
                            style="width:24px;height:24px">
                        </lord-icon>
                    </div>

                    <!-- Enhanced Typography -->
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">What I Actually Learned</h3>
                    <p class="text-lg text-gray-600 leading-relaxed font-light mb-6 max-w-2xl mx-auto">
                        "Turns out <span class="font-medium text-brand-blue">you don't need the shiniest, newest framework to build something that looks good</span>. Sometimes the messy, slow way of figuring it out yourself teaches you way more than any tutorial ever could."
                    </p>

                    <!-- Insight Card -->
                    <div class="bg-white/80 rounded-xl p-4 max-w-xl mx-auto border border-gray-100">
                        <p class="text-sm text-gray-600 font-light leading-relaxed">
                            Four weeks, way too many Stack Overflow tabs, and one surprisingly decent e-commerce site later.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>



    <!-- Key Milestones Section -->
    <section id="milestones" class="story-section py-24 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div class="max-w-6xl mx-auto">
            <div class="text-center mb-20">
                <div class="inline-flex items-center px-4 py-2 rounded-full bg-brand-blue/10 border border-brand-blue/20 mb-6">
                    <span class="text-sm font-medium text-brand-blue">THE JOURNEY</span>
                </div>
                <h2 class="text-4xl font-dm-sans-extrabold font-black text-gray-900 mb-6">
                    Journey On Coding
                </h2>
                <p class="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
                    Here's the real story of how I went from "I'll just make a simple landing page"
                    to "Holy crap, I actually built a full SaaS platform" through research, building, challenges, and breakthrough moments.
                </p>
            </div>

            <!-- Journey Card Container -->
            <div class="flex justify-center">
                <div class="journey-card-container relative flex items-center gap-8">
                    <!-- Main Card - Horizontal Layout -->
                    <div class="journey-card bg-white rounded-3xl p-8 max-w-2xl border border-gray-300 relative overflow-hidden" style="-webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; text-rendering: optimizeLegibility; font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;">
                        <!-- Background Gradient -->
                        <div class="absolute inset-0 bg-gradient-to-br from-blue-50 to-indigo-50 opacity-50"></div>

                        <!-- Content - No Icon Layout -->
                        <div class="relative z-10">
                            <!-- Title -->
                            <h3 class="journey-card-title text-2xl font-bold text-gray-900 mb-3 font-inter" style="-webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;">
                                Explore My Coding Journey
                            </h3>

                            <!-- Description -->
                            <p class="journey-card-description text-gray-600 mb-6 leading-relaxed font-inter" style="-webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;">
                                Dive deep into the interactive story of my development process, complete with animations, progress tracking, and behind-the-scenes insights.
                            </p>

                            <!-- Button with Preview Trigger -->
                            <a href="improved-journey.html" class="journey-card-button preview-trigger relative inline-flex items-center justify-center gap-3 text-white px-6 py-3 rounded-2xl font-semibold font-inter transition-all duration-500 group" style="background-color: #3674B5; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;" onmouseover="this.style.backgroundColor='#2563EB'" onmouseout="this.style.backgroundColor='#3674B5'">
                                <span class="button-text" style="order: 1;">View Interactive Journey</span>
                                <span class="button-text-new" style="order: 2;">Great! Please Enjoy!</span>
                                <svg class="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="order: 3;">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                                </svg>
                            </a>
                        </div>
                    </div>

                    <!-- External Preview - Right Side -->
                    <div class="journey-preview">
                        <!-- Real iframe preview -->
                        <iframe
                            class="preview-iframe"
                            src="improved-journey.html"
                            title="Interactive Coding Journey Preview"
                            loading="lazy">
                        </iframe>
                    </div>
                </div>
            </div>

        </div>
    </section>

    <!-- Design Philosophy & Inspirations Section -->
    <section id="design" class="story-section py-24 px-4 sm:px-6 lg:px-8 bg-white">
        <div class="max-w-6xl mx-auto">
            <!-- Design Philosophy Content -->
            <div class="text-center mb-20">
                <div class="inline-flex items-center px-4 py-2 rounded-full bg-brand-blue/10 border border-brand-blue/20 mb-6">
                    <span class="text-sm font-medium text-brand-blue">DESIGN PHILOSOPHY</span>
                </div>
                <h2 class="text-4xl font-dm-sans-extrabold font-bold text-gray-900 mb-6">
                    Design Philosophy
                </h2>
                <p class="text-lg text-gray-600 mb-12 leading-relaxed max-w-3xl mx-auto">
                    Inspired by the liveliness of React websites and the elegance of modern SaaS platforms,
                    I crafted a design system that feels both premium and approachable.
                </p>

                <div class="grid md:grid-cols-3 gap-8 lg:gap-12 max-w-4xl mx-auto mb-20">
                    <div class="text-center">
                        <button class="color-psychology-button mx-auto mb-6"></button>
                        <h4 class="font-semibold text-gray-900 mb-3">Color Psychology</h4>
                        <p class="text-gray-600 text-sm leading-relaxed">
                            Chose <span class="text-brand font-medium">#3674B5</span> - a sophisticated blue
                            that conveys trust, professionalism, and innovation
                        </p>
                    </div>

                    <div class="text-center">
                        <div class="typography-magnify-container mx-auto mb-6">
                            <span class="typography-text">See</span>
                        </div>
                        <h4 class="font-semibold text-gray-900 mb-3">Typography Hierarchy</h4>
                        <p class="text-gray-600 text-sm leading-relaxed">
                            Inter for readability, DM Sans for headings - inspired by leading SaaS platforms
                        </p>
                    </div>

                    <div class="text-center">
                        <div class="w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                            <!-- Interactive AI Button - Sentio Brand Colors -->
                            <button
                              class="group relative outline-0 bg-blue-100 [--sz-btn:68px] [--space:calc(var(--sz-btn)/5.5)] [--gen-sz:calc(var(--space)*2)] [--sz-text:calc(var(--sz-btn)-var(--gen-sz))] h-[var(--sz-btn)] w-[var(--sz-btn)] border border-solid border-transparent rounded-xl flex items-center justify-center aspect-square cursor-pointer transition-transform duration-200 active:scale-[0.95] bg-[linear-gradient(45deg,#3674B5,#4A90E2)] [box-shadow:#3c40434d_0_1px_2px_0,#3c404326_0_2px_6px_2px,#0000004d_0_30px_60px_-30px,#34343459_0_-2px_6px_0_inset]"
                            >
                              <svg
                                class="animate-pulse absolute z-10 overflow-visible transition-all duration-300 text-blue-200 group-hover:text-white top-[calc(var(--sz-text)/7)] left-[calc(var(--sz-text)/7)] h-[var(--gen-sz)] w-[var(--gen-sz)] group-hover:h-[var(--sz-text)] group-hover:w-[var(--sz-text)] group-hover:left-[calc(var(--sz-text)/4)] group-hover:top-[calc(calc(var(--gen-sz))/2)]"
                                stroke="none"
                                viewBox="0 0 24 24"
                                fill="currentColor"
                              >
                                <path
                                  fill-rule="evenodd"
                                  clip-rule="evenodd"
                                  d="M9 4.5a.75.75 0 01.721.544l.813 2.846a3.75 3.75 0 002.576 2.576l2.846.813a.75.75 0 010 1.442l-2.846.813a3.75 3.75 0 00-2.576 2.576l-.813 2.846a.75.75 0 01-1.442 0l-.813-2.846a3.75 3.75 0 00-2.576-2.576l-2.846-.813a.75.75 0 010-1.442l2.846-.813A3.75 3.75 0 007.466 7.89l.813-2.846A.75.75 0 019 4.5zM18 1.5a.75.75 0 01.728.568l.258 1.036c.236.94.97 1.674 1.91 1.91l1.036.258a.75.75 0 010 1.456l-1.036.258c-.94.236-1.674.97-1.91 1.91l-.258 1.036a.75.75 0 01-1.456 0l-.258-1.036a2.625 2.625 0 00-1.91-1.91l-1.036-.258a.75.75 0 010-1.456l1.036-.258a2.625 2.625 0 001.91-1.91l.258-1.036A.75.75 0 0118 1.5zM16.5 15a.75.75 0 01.712.513l.394 1.183c.15.447.5.799.948.948l1.183.395a.75.75 0 010 1.422l-1.183.395c-.447.15-.799.5-.948.948l-.395 1.183a.75.75 0 01-1.422 0l-.395-1.183a1.5 1.5 0 00-.948-.948l-1.183-.395a.75.75 0 010-1.422l1.183-.395c.447-.15.799-.5.948-.948l.395-1.183A.75.75 0 0116.5 15z"
                                ></path>
                              </svg>
                              <span
                                class="[font-size:var(--sz-text)] font-extrabold leading-none text-white transition-all duration-200 group-hover:opacity-0"
                                >AI</span
                              >
                            </button>
                        </div>
                        <h4 class="font-semibold text-gray-900 mb-3">Micro-interactions</h4>
                        <p class="text-gray-600 text-sm leading-relaxed">
                            Every hover, click, and scroll tells a story through carefully crafted animations
                        </p>
                    </div>
                </div>
            </div>

            <!-- Design Inspirations Slideshow -->
            <div class="max-w-5xl mx-auto">
                <div class="text-center mb-12">
                    <h3 class="text-3xl font-bold text-gray-900 mb-4">Design Inspirations</h3>
                    <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                        The visual influences that shaped Sentio's aesthetic and user experience approach.
                    </p>
                </div>

            <!-- Large Image-Focused Slideshow Container -->
            <div class="relative overflow-hidden rounded-2xl bg-white border border-gray-200 max-w-6xl mx-auto" style="box-shadow: 0 8px 25px -8px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.05);">
                <!-- Slides -->
                <div id="slideshow-container" class="relative h-[600px] md:h-[700px]" style="overflow: hidden;">
                    <!-- Slide 1: Google Material Design -->
                    <div class="slide active absolute inset-0 opacity-100 transition-all duration-500 ease-in-out">
                        <div class="grid grid-cols-1 lg:grid-cols-2 h-full">
                            <!-- Large Image Section -->
                            <div class="flex items-center justify-center p-8 lg:p-12">
                                <div class="slideshow-tooltip-container">
                                    <div class="slideshow-tooltip">Click to view full size</div>
                                    <div class="w-full max-w-xl bg-white rounded-2xl border border-gray-300 overflow-hidden cursor-pointer slideshow-image-container" onclick="openImageModal('Image/design-inspiration-01-material.png', 'Material Design inspiration')">
                                        <img src="Image/design-inspiration-01-material.png"
                                             alt="Material Design inspiration"
                                             class="w-full h-full object-contain border border-gray-200 rounded-2xl">
                                    </div>
                                </div>
                            </div>

                            <!-- Content Section -->
                            <div class="flex flex-col justify-center p-8 lg:p-12" style="background: linear-gradient(to right, white 0%, rgba(255, 255, 255, 0.9) 15%, rgba(59, 130, 246, 0.05) 30%, rgba(59, 130, 246, 0.12) 50%, rgba(59, 130, 246, 0.20) 70%, rgba(59, 130, 246, 0.30) 85%, rgba(59, 130, 246, 0.40) 100%)">
                                <div class="max-w-md">
                                    <div class="inline-flex items-center px-3 py-1 bg-blue-100 rounded-full mb-6">
                                        <span class="text-blue-700 text-sm font-medium">Google Design System</span>
                                    </div>
                                    <h4 class="text-3xl font-bold text-gray-900 mb-4">Material Design</h4>
                                    <p class="text-gray-600 leading-relaxed text-lg mb-6">
                                        Clean typography and thoughtful use of space that creates intuitive user experiences with purposeful motion and depth.
                                    </p>
                                    <div class="flex items-center space-x-4">
                                        <div class="px-4 py-2 bg-blue-500 text-white rounded-lg text-sm font-medium">
                                            Clean & Intuitive
                                        </div>
                                        <div class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg text-sm font-medium">
                                            Design System
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Slide 2: Magic UI Components -->
                    <div class="slide absolute inset-0 opacity-0 transition-all duration-500 ease-in-out" style="pointer-events: none;">
                        <div class="grid grid-cols-1 lg:grid-cols-2 h-full">
                            <!-- Large Image Section -->
                            <div class="flex items-center justify-center p-8 lg:p-12">
                                <div class="slideshow-tooltip-container">
                                    <div class="slideshow-tooltip">Click to view full size</div>
                                    <div class="w-full max-w-xl bg-white rounded-2xl border border-gray-300 overflow-hidden cursor-pointer slideshow-image-container" onclick="openImageModal('Image/design-inspiration-02-magic-ui.png', 'Magic UI components inspiration')">
                                        <img src="Image/design-inspiration-02-magic-ui.png"
                                             alt="Magic UI components inspiration"
                                             class="w-full h-full object-contain border border-gray-200 rounded-2xl">
                                    </div>
                                </div>
                            </div>

                            <!-- Content Section -->
                            <div class="flex flex-col justify-center p-8 lg:p-12" style="background: linear-gradient(to right, white 0%, rgba(255, 255, 255, 0.9) 15%, rgba(168, 85, 247, 0.05) 30%, rgba(168, 85, 247, 0.12) 50%, rgba(168, 85, 247, 0.20) 70%, rgba(168, 85, 247, 0.30) 85%, rgba(168, 85, 247, 0.40) 100%)">
                                <div class="max-w-md">
                                    <div class="flex items-center gap-3 mb-6">
                                        <div class="inline-flex items-center px-3 py-1 bg-purple-100 rounded-full">
                                            <span class="text-purple-700 text-sm font-medium">Animation Library</span>
                                        </div>
                                        <a href="https://magicui.design/" target="_blank" rel="noopener noreferrer"
                                           class="inline-flex items-center px-3 py-1 bg-purple-500 hover:bg-purple-600 text-white rounded-full text-sm font-medium transition-all duration-200 hover:scale-105">
                                            <span>Learn More</span>
                                            <svg class="w-3 h-3 ml-1.5 transition-transform duration-200 group-hover:translate-x-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                            </svg>
                                        </a>
                                    </div>
                                    <h4 class="text-3xl font-bold text-gray-900 mb-4">Magic UI</h4>
                                    <p class="text-gray-600 leading-relaxed text-lg mb-6">
                                        Subtle animations and micro-interactions that bring interfaces to life without overwhelming the user experience.
                                    </p>
                                    <div class="flex items-center space-x-4">
                                        <div class="px-4 py-2 bg-purple-500 text-white rounded-lg text-sm font-medium">
                                            Delightful Interactions
                                        </div>
                                        <div class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg text-sm font-medium">
                                            Micro-animations
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>


                    </div>

                    <!-- Slide 3: Tailwind UI Patterns -->
                    <div class="slide absolute inset-0 opacity-0 transition-all duration-500 ease-in-out" style="pointer-events: none;">
                        <div class="grid grid-cols-1 lg:grid-cols-2 h-full">
                            <!-- Large Image Section -->
                            <div class="flex items-center justify-center p-8 lg:p-12">
                                <div class="slideshow-tooltip-container">
                                    <div class="slideshow-tooltip">Click to view full size</div>
                                    <div class="w-full max-w-xl bg-white rounded-2xl border border-gray-300 overflow-hidden cursor-pointer slideshow-image-container" onclick="openImageModal('Image/design-inspiration-03-tailwind.png', 'Tailwind UI patterns inspiration')">
                                        <img src="Image/design-inspiration-03-tailwind.png"
                                             alt="Tailwind UI patterns inspiration"
                                             class="w-full h-full object-contain border border-gray-200 rounded-2xl">
                                    </div>
                                </div>
                            </div>

                            <!-- Content Section -->
                            <div class="flex flex-col justify-center p-8 lg:p-12" style="background: linear-gradient(to right, white 0%, rgba(255, 255, 255, 0.9) 15%, rgba(6, 182, 212, 0.05) 30%, rgba(6, 182, 212, 0.12) 50%, rgba(6, 182, 212, 0.20) 70%, rgba(6, 182, 212, 0.30) 85%, rgba(6, 182, 212, 0.40) 100%)">
                                <div class="max-w-md">
                                    <div class="flex items-center gap-3 mb-6">
                                        <div class="inline-flex items-center px-3 py-1 bg-cyan-100 rounded-full">
                                            <span class="text-cyan-700 text-sm font-medium">CSS Framework</span>
                                        </div>
                                        <a href="https://tailwindcss.com/plus" target="_blank" rel="noopener noreferrer"
                                           class="inline-flex items-center px-3 py-1 bg-cyan-500 hover:bg-cyan-600 text-white rounded-full text-sm font-medium transition-all duration-200 hover:scale-105">
                                            <span>Learn More</span>
                                            <svg class="w-3 h-3 ml-1.5 transition-transform duration-200 group-hover:translate-x-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                            </svg>
                                        </a>
                                    </div>
                                    <h4 class="text-3xl font-bold text-gray-900 mb-4">Tailwind UI</h4>
                                    <p class="text-gray-600 leading-relaxed text-lg mb-6">
                                        Utility-first approach enabling rapid prototyping while maintaining design consistency and scalability.
                                    </p>
                                    <div class="flex items-center space-x-4">
                                        <div class="px-4 py-2 bg-cyan-500 text-white rounded-lg text-sm font-medium">
                                            Utility-First Design
                                        </div>
                                        <div class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg text-sm font-medium">
                                            Component Library
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>


                    </div>
                </div>

                <!-- Navigation Dots -->
                <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                    <button class="dot active w-2 h-2 rounded-full bg-brand-blue transition-all duration-300" onclick="currentSlide(1)"></button>
                    <button class="dot w-2 h-2 rounded-full bg-gray-300 hover:bg-gray-400 transition-all duration-300" onclick="currentSlide(2)"></button>
                    <button class="dot w-2 h-2 rounded-full bg-gray-300 hover:bg-gray-400 transition-all duration-300" onclick="currentSlide(3)"></button>
                </div>


            </div>

                <!-- Philosophy Quote Section -->
                <div class="mt-16 max-w-4xl mx-auto">
                    <!-- Minimalistic Quote Container -->
                    <div class="quote-container text-center py-16">
                        <!-- Quote Icon - Minimalistic -->
                        <div class="flex justify-center mb-12">
                            <svg class="w-8 h-8 text-brand-blue opacity-60" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z"/>
                            </svg>
                        </div>

                        <!-- Quote Text -->
                        <blockquote class="max-w-3xl mx-auto">
                            <p class="quote-text text-2xl md:text-3xl font-light text-gray-700 leading-relaxed mb-12">
                                The goal was to make every interaction feel
                                <span class="quote-highlight font-medium text-brand-blue">intentional</span>,
                                every element
                                <span class="quote-highlight font-medium text-brand-blue">purposeful</span>,
                                and every animation
                                <span class="quote-highlight font-medium text-brand-blue">meaningful</span>.
                            </p>
                        </blockquote>

                        <!-- Attribution - Minimalistic -->
                        <div class="quote-attribution">
                            <span class="text-sm font-medium text-gray-400 uppercase tracking-widest">Design Philosophy</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- The Result - Minimalist Showcase -->
    <section id="current" class="story-section py-24 px-4 sm:px-6 lg:px-8">
        <div class="max-w-5xl mx-auto">

            <!-- Simple Header -->
            <div class="text-center mb-20">
                <h2 class="text-3xl font-bold text-gray-900 mb-8">
                    The Result
                </h2>
            </div>

            <!-- Floating Cards Layout -->
            <div class="relative">

                <!-- Main Demo Card -->
                <div class="demo-card bg-white rounded-2xl border border-gray-100 p-8 mb-8 relative z-10 overflow-hidden">


                    <div class="text-center relative z-10">
                        <h3 class="text-xl font-bold text-gray-900 mb-4">
                            Interactive E-commerce Experience
                        </h3>

                        <p class="text-gray-600 mb-6 leading-relaxed max-w-2xl mx-auto">
                            A fully functional SaaS landing page that demonstrates modern web development
                            capabilities through intentional design and meaningful interactions.
                        </p>

                        <a href="Testing.html" target="_blank" class="subscribe-btn">
                            Experience Demo
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" style="margin-left:8px;">
                                <path d="M12.5 18C12.5 18 18.5 13.5811 18.5 12C18.5 10.4188 12.5 6 12.5 6" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                <path d="M5.50005 18C5.50005 18 11.5 13.5811 11.5 12C11.5 10.4188 5.5 6 5.5 6" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            </svg>
                        </a>
                    </div>
                </div>






            </div>
        </div>
    </section>

    <!-- Future Vision Section -->
    <section id="future" class="story-section py-24 px-4 sm:px-6 lg:px-8 bg-white">
        <div class="max-w-6xl mx-auto">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold text-gray-900 mb-8">
                    What's Next for Sentio?
                </h2>
                <p class="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed mb-16">
                    I'll keep this as my project and will create a backend functionality as well next week.
                    For now, I want this as one of my best landing pages yet that I created.
                </p>
            </div>

            <!-- Horizontal Timeline -->
            <div class="relative">
                <!-- Timeline Items -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                    <!-- Today -->
                    <div class="text-center">
                        <div class="bg-white p-6 rounded-lg shadow-sm border h-full timeline-today relative overflow-hidden">
                            <!-- Progress Circle -->
                            <div class="flex justify-center mb-4">
                                <div class="relative w-12 h-12">
                                    <svg class="w-12 h-12 transform -rotate-90" viewBox="0 0 36 36">
                                        <path class="text-gray-200" stroke="currentColor" stroke-width="2" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                        <path class="text-black progress-circle-today" stroke="currentColor" stroke-width="2" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                    </svg>
                                    <div class="absolute inset-0 flex items-center justify-center">
                                        <span class="text-xs font-semibold text-black percentage-text">15%</span>
                                    </div>
                                </div>
                            </div>
                            <h3 class="font-semibold text-gray-900 mb-2">Today</h3>
                            <p class="text-sm text-gray-600 mb-1 font-medium">Landing page complete</p>
                            <p class="text-sm text-gray-600">Clean, minimalistic design with welcome story and project journey</p>
                        </div>
                    </div>

                    <!-- Phase 1: Backend -->
                    <div class="text-center">
                        <div class="bg-white p-6 rounded-lg shadow-sm border h-full timeline-future relative overflow-hidden">
                            <!-- Progress Circle -->
                            <div class="flex justify-center mb-4">
                                <div class="relative w-12 h-12">
                                    <svg class="w-12 h-12 transform -rotate-90" viewBox="0 0 36 36">
                                        <defs>
                                            <linearGradient id="phase1Gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                                <stop offset="0%" style="stop-color:#000000;stop-opacity:1" />
                                                <stop offset="60%" style="stop-color:#1a1a1a;stop-opacity:1" />
                                                <stop offset="100%" style="stop-color:#3674B5;stop-opacity:1" />
                                            </linearGradient>
                                        </defs>
                                        <path class="text-gray-200" stroke="currentColor" stroke-width="2" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                        <path class="progress-circle-phase1" stroke="url(#phase1Gradient)" stroke-width="2" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                    </svg>
                                    <div class="absolute inset-0 flex items-center justify-center">
                                        <span class="text-xs font-semibold percentage-text" style="background: linear-gradient(135deg, #000000 30%, #3674B5 70%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">25%</span>
                                    </div>
                                </div>
                            </div>
                            <h3 class="font-semibold text-gray-900 mb-2">Phase 1</h3>
                            <p class="text-sm mb-1 font-medium" style="background: linear-gradient(135deg, #000000 35%, #3674B5 65%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">Backend</p>
                            <p class="text-sm text-gray-600">Database integration, user authentication, and core functionality</p>
                        </div>
                    </div>

                    <!-- Phase 2: Polishing -->
                    <div class="text-center">
                        <div class="bg-white p-6 rounded-lg shadow-sm border h-full timeline-future relative overflow-hidden">
                            <!-- Progress Circle -->
                            <div class="flex justify-center mb-4">
                                <div class="relative w-12 h-12">
                                    <svg class="w-12 h-12 transform -rotate-90" viewBox="0 0 36 36">
                                        <defs>
                                            <linearGradient id="phase2Gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                                <stop offset="0%" style="stop-color:#000000;stop-opacity:1" />
                                                <stop offset="30%" style="stop-color:#1a3a5c;stop-opacity:1" />
                                                <stop offset="70%" style="stop-color:#2a5a8a;stop-opacity:1" />
                                                <stop offset="100%" style="stop-color:#3674B5;stop-opacity:1" />
                                            </linearGradient>
                                        </defs>
                                        <path class="text-gray-200" stroke="currentColor" stroke-width="2" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                        <path class="progress-circle-phase2" stroke="url(#phase2Gradient)" stroke-width="2" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                    </svg>
                                    <div class="absolute inset-0 flex items-center justify-center">
                                        <span class="text-xs font-semibold percentage-text" style="background: linear-gradient(135deg, #000000 20%, #3674B5 80%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">60%</span>
                                    </div>
                                </div>
                            </div>
                            <h3 class="font-semibold text-gray-900 mb-2">Phase 2</h3>
                            <p class="text-sm mb-1 font-medium" style="background: linear-gradient(135deg, #000000 25%, #3674B5 75%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">Polishing</p>
                            <p class="text-sm text-gray-600">UI/UX refinements, performance optimization, and testing</p>
                        </div>
                    </div>

                    <!-- Final Phase: Showcase -->
                    <div class="text-center">
                        <div class="bg-white p-6 rounded-lg shadow-sm border h-full timeline-future relative overflow-hidden">
                            <!-- Progress Circle -->
                            <div class="flex justify-center mb-4">
                                <div class="relative w-12 h-12">
                                    <svg class="w-12 h-12 transform -rotate-90" viewBox="0 0 36 36">
                                        <path class="text-gray-200" stroke="currentColor" stroke-width="2" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                        <path class="progress-circle-final" stroke="#3674B5" stroke-width="2" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                    </svg>
                                    <div class="absolute inset-0 flex items-center justify-center">
                                        <span class="text-xs font-semibold percentage-text" style="color: #3674B5;">100%</span>
                                    </div>
                                </div>
                            </div>
                            <h3 class="font-semibold text-gray-900 mb-2">Final Phase</h3>
                            <p class="text-sm mb-1 font-medium" style="color: #3674B5;">Showcase</p>
                            <p class="text-sm text-gray-600">Featured as a portfolio project demonstrating full-stack capabilities</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Explore Sentio - Minimalistic Section -->
    <section class="py-32 px-4 sm:px-6 lg:px-8 bg-white">
        <div class="max-w-3xl mx-auto">
            <!-- Main Content -->
            <div class="text-center mb-20">
                <h2 class="text-4xl font-bold text-gray-900 mb-6">
                    Explore Sentio
                </h2>
                <p class="text-lg text-gray-600 mb-16 max-w-2xl mx-auto leading-relaxed">
                    Experience the platform that makes e-commerce development effortless
                </p>


            </div>

            <!-- Contact Form with Left-Right Layout -->
            <div id="contact-form" class="max-w-6xl mx-auto">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-12 items-start relative">

                    <!-- Left Side - Contact Form (2/3 width) -->
                    <div class="lg:col-span-2">
                        <div class="mb-12">
                            <h3 class="text-2xl font-bold text-gray-900 mb-3">
                                Let's Connect
                            </h3>
                            <p class="text-gray-600">
                                Love what you see? Contact me and let's discuss what you have in mind - whether it's a project, collaboration, or anything else you'd like to explore together.
                            </p>
                        </div>

                        <form action="mailto:<EMAIL>" method="post" enctype="text/plain" class="space-y-8">
                            <div class="space-y-8">
                                <!-- Name Input with Animated Label -->
                                <div class="input-group">
                                    <input required type="text" id="name" name="name" autocomplete="off" class="input">
                                    <label class="user-label">Your Name</label>
                                </div>

                                <!-- Email Input with Animated Label -->
                                <div class="input-group">
                                    <input required type="email" id="email" name="email" autocomplete="off" class="input">
                                    <label class="user-label">Your Email</label>
                                </div>

                                <!-- Message Textarea with Animated Label -->
                                <div class="input-group">
                                    <textarea required id="message" name="message" autocomplete="off" class="input"></textarea>
                                    <label class="user-label">Tell me about your project...</label>
                                </div>
                            </div>

                            <div class="pt-8 text-center">
                                <button type="submit" class="subscribe-btn">
                                    <span>Send Message</span>
                                    <svg class="w-5 h-5 ml-2" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <g id="_01_align_center" data-name="01 align center">
                                            <path d="M1.444,6.669a2,2,0,0,0-.865,3.337l3.412,3.408V20h6.593l3.435,3.43a1.987,1.987,0,0,0,1.408.588,2.034,2.034,0,0,0,.51-.066,1.978,1.978,0,0,0,1.42-1.379L23.991.021ZM2,8.592l17.028-5.02L5.993,16.586v-4Zm13.44,13.424L11.413,18h-4L20.446,4.978Z"/>
                                        </g>
                                    </svg>
                                </button>
                            </div>
                        </form>

                        <div class="mt-8 pt-8 border-t border-gray-100">
                            <p class="text-sm text-gray-500">
                                Or reach out directly at
                                <a href="mailto:<EMAIL>" class="text-brand hover:text-blue-700 font-medium transition-colors duration-300">
                                    <EMAIL>
                                </a>
                            </p>
                        </div>
                    </div>

                    <!-- Vertical Divider Line (Desktop/Tablet Only) -->
                    <div class="hidden lg:block absolute left-2/3 top-0 bottom-0 w-px bg-gradient-to-b from-transparent via-gray-200 to-transparent transform -translate-x-1/2"></div>

                    <!-- Right Side - GitHub Button (1/3 width) -->
                    <div class="lg:col-span-1">
                        <div class="sticky top-8">
                            <div class="text-center lg:text-left mb-8">
                                <h4 class="text-lg font-semibold text-gray-900 mb-4">
                                    Check Out My Work
                                </h4>
                                <p class="text-gray-600 text-sm mb-6">
                                    It will be great if you can star this project of mine! thank you!
                                </p>
                            </div>

                            <!-- Animated GitHub Button -->
                            <div class="flex justify-center lg:justify-start">
                                <button
                                    onclick="window.open('https://github.com/yourusername', '_blank')"
                                    class="items-center justify-center whitespace-nowrap rounded-xl text-sm font-medium transition-transform duration-200 ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group relative animate-rainbow cursor-pointer border-0 bg-[linear-gradient(#fff,#fff),linear-gradient(#fff_50%,rgba(255,255,255,0.6)_80%,rgba(0,0,0,0)),linear-gradient(90deg,hsl(0,100%,63%),hsl(90,100%,63%),hsl(210,100%,63%),hsl(195,100%,63%),hsl(270,100%,63%))] bg-[length:200%] text-foreground [background-clip:padding-box,border-box,border-box] [background-origin:border-box] [border:calc(0.08*1rem)_solid_transparent] before:absolute before:bottom-[-20%] before:left-1/2 before:z-[0] before:h-[20%] before:w-[60%] before:-translate-x-1/2 before:animate-rainbow before:bg-[linear-gradient(90deg,hsl(0,100%,63%),hsl(90,100%,63%),hsl(210,100%,63%),hsl(195,100%,63%),hsl(270,100%,63%))] before:bg-[length:200%] before:[filter:blur(calc(0.8*1rem))] dark:bg-[linear-gradient(#121213,#121213),linear-gradient(#121213_50%,rgba(18,18,19,0.6)_80%,rgba(18,18,19,0)),linear-gradient(90deg,hsl(0,100%,63%),hsl(90,100%,63%),hsl(210,100%,63%),hsl(195,100%,63%),hsl(270,100%,63%))] hover:scale-105 active:scale-95 h-10 px-4 py-2 inline-flex"
                                >
                                    <div class="flex items-center">
                                        <svg class="size-4" viewBox="0 0 438.549 438.549">
                                            <path
                                                d="M409.132 114.573c-19.608-33.596-46.205-60.194-79.798-79.8-33.598-19.607-70.277-29.408-110.063-29.408-39.781 0-76.472 9.804-110.063 29.408-33.596 19.605-60.192 46.204-79.8 79.8C9.803 148.168 0 184.854 0 224.63c0 47.78 13.94 90.745 41.827 128.906 27.884 38.164 63.906 64.572 108.063 79.227 5.14.954 8.945.283 11.419-1.996 2.475-2.282 3.711-5.14 3.711-8.562 0-.571-.049-5.708-.144-15.417a2549.81 2549.81 0 01-.144-25.406l-6.567 1.136c-4.187.767-9.469 1.092-15.846 1-6.374-.089-12.991-.757-19.842-1.999-6.854-1.231-13.229-4.086-19.13-8.559-5.898-4.473-10.085-10.328-12.56-17.556l-2.855-6.57c-1.903-4.374-4.899-9.233-8.992-14.559-4.093-5.331-8.232-8.945-12.419-10.848l-1.999-1.431c-1.332-.951-2.568-2.098-3.711-3.429-1.142-1.331-1.997-2.663-2.568-3.997-.572-1.335-.098-2.43 1.427-3.289 1.525-.859 4.281-1.276 8.28-1.276l5.708.853c3.807.763 8.516 3.042 14.133 6.851 5.614 3.806 10.229 8.754 13.846 14.842 4.38 7.806 9.657 13.754 15.846 17.847 6.184 4.093 12.419 6.136 18.699 6.136 6.28 0 11.704-.476 16.274-1.423 4.565-.952 8.848-2.383 12.847-4.285 1.713-12.758 6.377-22.559 13.988-29.41-10.848-1.14-20.601-2.857-29.264-5.14-8.658-2.286-17.605-5.996-26.835-11.14-9.235-5.137-16.896-11.516-22.985-19.126-6.09-7.614-11.088-17.61-14.987-29.979-3.901-12.374-5.852-26.648-5.852-42.826 0-23.035 7.52-42.637 22.557-58.817-7.044-17.318-6.379-36.732 1.997-58.24 5.52-1.715 13.706-.428 24.554 3.853 10.85 4.283 18.794 7.952 23.84 10.994 5.046 3.041 9.089 5.618 12.135 7.708 17.705-4.947 35.976-7.421 54.818-7.421s37.117 2.474 54.823 7.421l10.849-6.849c7.419-4.57 16.18-8.758 26.262-12.565 10.088-3.805 17.802-4.853 23.134-3.138 8.562 21.509 9.325 40.922 2.279 58.24 15.036 16.18 22.559 35.787 22.559 58.817 0 16.178-1.958 30.497-5.853 42.966-3.9 12.471-8.941 22.457-15.125 29.979-6.191 7.521-13.901 13.85-23.131 18.986-9.232 5.14-18.182 8.85-26.84 11.136-8.662 2.286-18.415 4.004-29.263 5.146 9.894 8.562 14.842 22.077 14.842 40.539v60.237c0 3.422 1.19 6.279 3.572 8.562 2.379 2.279 6.136 2.95 11.276 1.995 44.163-14.653 80.185-41.062 108.068-79.226 27.88-38.161 41.825-81.126 41.825-128.906-.01-39.771-9.818-76.454-29.414-110.049z"
                                                fill="#fff"
                                            ></path>
                                        </svg>
                                        <span class="ml-1 text-white lg:inline p-1">Star on GitHub</span>
                                    </div>
                                    <div class="ml-2 flex items-center gap-1 text-sm md:flex">
                                        <svg
                                            class="size-4 text-gray-500 transition-all duration-200 group-hover:text-yellow-300"
                                            style="transform: translateY(-0.10px);"
                                            data-slot="icon"
                                            aria-hidden="true"
                                            fill="currentColor"
                                            viewBox="0 0 24 24"
                                            xmlns="http://www.w3.org/2000/svg"
                                        >
                                            <path
                                                clip-rule="evenodd"
                                                d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.006 5.404.434c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.434 2.082-5.005Z"
                                                fill-rule="evenodd"
                                            ></path>
                                        </svg>
                                        <span
                                            class="inline-block tabular-nums tracking-wider font-display font-medium text-black dark:text-white"
                                            style="transform: translateY(-0.10px);"
                                        >0</span>
                                    </div>
                                </button>
                            </div>

                            <!-- Ready to Explore Button -->
                            <div class="mt-8 pt-8 border-t border-gray-100">
                                <div class="text-center lg:text-left mb-6">
                                    <h4 class="text-lg font-semibold text-gray-900 mb-3">
                                        Ready to explore?
                                    </h4>
                                    <p class="text-gray-600 text-sm mb-6">
                                        Go to the main website to know more about it!
                                    </p>
                                </div>

                                <!-- Explore Website Button -->
                                <div class="flex justify-center lg:justify-start">
                                    <button
                                        onclick="window.location.href='Testing.html'"
                                        class="star-button"
                                    >
                                        Explore Website
                                        <div class="star-1">
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                xml:space="preserve"
                                                version="1.1"
                                                style="shape-rendering:geometricPrecision; text-rendering:geometricPrecision; image-rendering:optimizeQuality; fill-rule:evenodd; clip-rule:evenodd"
                                                viewBox="0 0 784.11 815.53"
                                                xmlns:xlink="http://www.w3.org/1999/xlink"
                                            >
                                                <defs></defs>
                                                <g id="Layer_x0020_1">
                                                    <metadata id="CorelCorpID_0Corel-Layer"></metadata>
                                                    <path
                                                        class="fil0"
                                                        d="M392.05 0c-20.9,210.08 -184.06,378.41 -392.05,407.78 207.96,29.37 371.12,197.68 392.05,407.74 20.93,-210.06 184.09,-378.37 392.05,-407.74 -207.98,-29.38 -371.16,-197.69 -392.06,-407.78z"
                                                    ></path>
                                                </g>
                                            </svg>
                                        </div>
                                        <div class="star-2">
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                xml:space="preserve"
                                                version="1.1"
                                                style="shape-rendering:geometricPrecision; text-rendering:geometricPrecision; image-rendering:optimizeQuality; fill-rule:evenodd; clip-rule:evenodd"
                                                viewBox="0 0 784.11 815.53"
                                                xmlns:xlink="http://www.w3.org/1999/xlink"
                                            >
                                                <defs></defs>
                                                <g id="Layer_x0020_1">
                                                    <metadata id="CorelCorpID_0Corel-Layer"></metadata>
                                                    <path
                                                        class="fil0"
                                                        d="M392.05 0c-20.9,210.08 -184.06,378.41 -392.05,407.78 207.96,29.37 371.12,197.68 392.05,407.74 20.93,-210.06 184.09,-378.37 392.05,-407.74 -207.98,-29.38 -371.16,-197.69 -392.06,-407.78z"
                                                    ></path>
                                                </g>
                                            </svg>
                                        </div>
                                        <div class="star-3">
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                xml:space="preserve"
                                                version="1.1"
                                                style="shape-rendering:geometricPrecision; text-rendering:geometricPrecision; image-rendering:optimizeQuality; fill-rule:evenodd; clip-rule:evenodd"
                                                viewBox="0 0 784.11 815.53"
                                                xmlns:xlink="http://www.w3.org/1999/xlink"
                                            >
                                                <defs></defs>
                                                <g id="Layer_x0020_1">
                                                    <metadata id="CorelCorpID_0Corel-Layer"></metadata>
                                                    <path
                                                        class="fil0"
                                                        d="M392.05 0c-20.9,210.08 -184.06,378.41 -392.05,407.78 207.96,29.37 371.12,197.68 392.05,407.74 20.93,-210.06 184.09,-378.37 392.05,-407.74 -207.98,-29.38 -371.16,-197.69 -392.06,-407.78z"
                                                    ></path>
                                                </g>
                                            </svg>
                                        </div>
                                        <div class="star-4">
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                xml:space="preserve"
                                                version="1.1"
                                                style="shape-rendering:geometricPrecision; text-rendering:geometricPrecision; image-rendering:optimizeQuality; fill-rule:evenodd; clip-rule:evenodd"
                                                viewBox="0 0 784.11 815.53"
                                                xmlns:xlink="http://www.w3.org/1999/xlink"
                                            >
                                                <defs></defs>
                                                <g id="Layer_x0020_1">
                                                    <metadata id="CorelCorpID_0Corel-Layer"></metadata>
                                                    <path
                                                        class="fil0"
                                                        d="M392.05 0c-20.9,210.08 -184.06,378.41 -392.05,407.78 207.96,29.37 371.12,197.68 392.05,407.74 20.93,-210.06 184.09,-378.37 392.05,-407.74 -207.98,-29.38 -371.16,-197.69 -392.06,-407.78z"
                                                    ></path>
                                                </g>
                                            </svg>
                                        </div>
                                        <div class="star-5">
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                xml:space="preserve"
                                                version="1.1"
                                                style="shape-rendering:geometricPrecision; text-rendering:geometricPrecision; image-rendering:optimizeQuality; fill-rule:evenodd; clip-rule:evenodd"
                                                viewBox="0 0 784.11 815.53"
                                                xmlns:xlink="http://www.w3.org/1999/xlink"
                                            >
                                                <defs></defs>
                                                <g id="Layer_x0020_1">
                                                    <metadata id="CorelCorpID_0Corel-Layer"></metadata>
                                                    <path
                                                        class="fil0"
                                                        d="M392.05 0c-20.9,210.08 -184.06,378.41 -392.05,407.78 207.96,29.37 371.12,197.68 392.05,407.74 20.93,-210.06 184.09,-378.37 392.05,-407.74 -207.98,-29.38 -371.16,-197.69 -392.06,-407.78z"
                                                    ></path>
                                                </g>
                                            </svg>
                                        </div>
                                        <div class="star-6">
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                xml:space="preserve"
                                                version="1.1"
                                                style="shape-rendering:geometricPrecision; text-rendering:geometricPrecision; image-rendering:optimizeQuality; fill-rule:evenodd; clip-rule:evenodd"
                                                viewBox="0 0 784.11 815.53"
                                                xmlns:xlink="http://www.w3.org/1999/xlink"
                                            >
                                                <defs></defs>
                                                <g id="Layer_x0020_1">
                                                    <metadata id="CorelCorpID_0Corel-Layer"></metadata>
                                                    <path
                                                        class="fil0"
                                                        d="M392.05 0c-20.9,210.08 -184.06,378.41 -392.05,407.78 207.96,29.37 371.12,197.68 392.05,407.74 20.93,-210.06 184.09,-378.37 392.05,-407.74 -207.98,-29.38 -371.16,-197.69 -392.06,-407.78z"
                                                    ></path>
                                                </g>
                                            </svg>
                                        </div>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </section>

    <script>
        // Smooth scroll functionality for story navigation
        function initializeStoryNavigation() {
            const navItems = document.querySelectorAll('.story-nav-item');
            const sections = document.querySelectorAll('.story-section, #hero');

            // Handle navigation clicks
            navItems.forEach((item, index) => {
                item.addEventListener('click', () => {
                    const targetSection = sections[index];
                    if (targetSection) {
                        targetSection.scrollIntoView({ behavior: 'smooth' });
                    }
                });
            });

            // Enhanced scroll-based navigation update
            function updateActiveNavigation() {
                const scrollPosition = window.scrollY;
                const windowHeight = window.innerHeight;
                const documentHeight = document.documentElement.scrollHeight;
                const triggerPoint = scrollPosition + windowHeight * 0.4; // Trigger at 40% of viewport

                // Check if we're at the bottom of the page (more generous buffer)
                const isAtBottom = scrollPosition + windowHeight >= documentHeight - 200; // 200px buffer

                // Check if we're in the last section (future section)
                const lastSection = sections[sections.length - 1];
                const lastSectionRect = lastSection.getBoundingClientRect();
                const lastSectionTop = lastSectionRect.top + scrollPosition;
                const isInLastSection = triggerPoint >= lastSectionTop - 100;

                let activeSection = null;
                let closestDistance = Infinity;

                sections.forEach((section, index) => {
                    const rect = section.getBoundingClientRect();
                    const sectionTop = rect.top + scrollPosition;
                    const sectionBottom = sectionTop + rect.height;
                    const sectionCenter = sectionTop + rect.height / 2;

                    // Calculate distance from trigger point to section center
                    const distance = Math.abs(triggerPoint - sectionCenter);

                    // Check if section is visible and closest to trigger point
                    if (triggerPoint >= sectionTop - 200 && triggerPoint <= sectionBottom + 200) {
                        if (distance < closestDistance) {
                            closestDistance = distance;
                            activeSection = { section, index };
                        }
                    }
                });

                // Force last section to be active if we're at bottom OR in last section
                if (isAtBottom || isInLastSection) {
                    activeSection = { section: sections[sections.length - 1], index: sections.length - 1 };
                }

                // Update navigation if we found an active section
                if (activeSection) {
                    navItems.forEach((item, index) => {
                        item.classList.toggle('active', index === activeSection.index);
                    });
                }
            }

            // Use scroll listener for real-time updates
            let scrollTimeout;
            window.addEventListener('scroll', () => {
                clearTimeout(scrollTimeout);
                scrollTimeout = setTimeout(updateActiveNavigation, 5); // Fast response
            });

            // Initial call to set correct active state
            updateActiveNavigation();
        }

        // Animate sections on scroll
        function initializeScrollAnimations() {
            const observerOptions = {
                root: null,
                rootMargin: '0px 0px -10% 0px',
                threshold: 0.1
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                    }
                });
            }, observerOptions);

            document.querySelectorAll('.story-section').forEach(section => {
                observer.observe(section);
            });
        }

        // Go back to welcome page
        function goBack() {
            window.location.href = 'welcome.html';
        }

        // Handle Copy-Paste Icon Sequential Animation
        function initializeCopyPasteAnimation() {
            const copyPasteIcon = document.getElementById('copyPasteIcon');
            if (!copyPasteIcon) return;

            // Create intersection observer to detect when the section is visible
            const observerOptions = {
                root: null,
                rootMargin: '0px 0px -20% 0px',
                threshold: 0.3
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        // Trigger initial "in" animation with delay
                        setTimeout(() => {
                            // Show the icon and prepare for "in" animation
                            copyPasteIcon.style.opacity = '1';
                            copyPasteIcon.setAttribute('trigger', 'in');
                            copyPasteIcon.setAttribute('state', 'in-assignment');

                            // Force the icon to play the "in" animation
                            if (copyPasteIcon.playerInstance) {
                                copyPasteIcon.playerInstance.playFromBeginning();
                            }

                            // After animation completes, switch back to hover
                            setTimeout(() => {
                                copyPasteIcon.setAttribute('trigger', 'hover');
                                copyPasteIcon.removeAttribute('state');
                            }, 2000); // Wait 2 seconds for animation to complete

                        }, 500); // 0.5 second delay

                        // Stop observing once triggered
                        observer.unobserve(entry.target);
                    }
                });
            }, observerOptions);

            // Observe the origin section (where the copy-paste icon is)
            const originSection = document.getElementById('origin');
            if (originSection) {
                observer.observe(originSection);
            }
        }

        // Handle Inspiration Overload Icon Sequential Animation
        function initializeInspirationAnimation() {
            const inspirationIcon = document.getElementById('inspirationIcon');
            if (!inspirationIcon) return;

            // Create intersection observer to detect when the section is visible
            const observerOptions = {
                root: null,
                rootMargin: '0px 0px -20% 0px',
                threshold: 0.3
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        // Trigger initial "in" animation with delay
                        setTimeout(() => {
                            // Show the icon and prepare for "in" animation
                            inspirationIcon.style.opacity = '1';
                            inspirationIcon.setAttribute('trigger', 'in');
                            inspirationIcon.setAttribute('state', 'in-search');

                            // Force the icon to play the "in" animation
                            if (inspirationIcon.playerInstance) {
                                inspirationIcon.playerInstance.playFromBeginning();
                            }

                            // After animation completes, switch back to hover
                            setTimeout(() => {
                                inspirationIcon.setAttribute('trigger', 'hover');
                                inspirationIcon.removeAttribute('state');
                            }, 2000); // Wait 2 seconds for animation to complete

                        }, 500); // 0.5 second delay

                        // Stop observing once triggered
                        observer.unobserve(entry.target);
                    }
                });
            }, observerOptions);

            // Observe the origin section (where the inspiration icon is)
            const originSection = document.getElementById('origin');
            if (originSection) {
                observer.observe(originSection);
            }
        }

        // Handle CSS Nightmares Icon Sequential Animation
        function initializeCSSNightmaresAnimation() {
            const cssNightmaresIcon = document.getElementById('cssNightmaresIcon');
            if (!cssNightmaresIcon) return;

            // Create intersection observer to detect when the section is visible
            const observerOptions = {
                root: null,
                rootMargin: '0px 0px -20% 0px',
                threshold: 0.3
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        // Trigger initial "in" animation with delay
                        setTimeout(() => {
                            // Show the icon and prepare for "in" animation
                            cssNightmaresIcon.style.opacity = '1';
                            cssNightmaresIcon.setAttribute('trigger', 'in');
                            cssNightmaresIcon.setAttribute('state', 'in-code');

                            // Force the icon to play the "in" animation
                            if (cssNightmaresIcon.playerInstance) {
                                cssNightmaresIcon.playerInstance.playFromBeginning();
                            }

                            // After animation completes, switch back to hover
                            setTimeout(() => {
                                cssNightmaresIcon.setAttribute('trigger', 'hover');
                                cssNightmaresIcon.removeAttribute('state');
                            }, 2000); // Wait 2 seconds for animation to complete

                        }, 500); // 0.5 second delay

                        // Stop observing once triggered
                        observer.unobserve(entry.target);
                    }
                });
            }, observerOptions);

            // Observe the origin section (where the CSS nightmares icon is)
            const originSection = document.getElementById('origin');
            if (originSection) {
                observer.observe(originSection);
            }
        }

        // Timeline Progress Animation
        function initializeTimelineAnimation() {
            const timelineProgress = document.getElementById('timeline-progress');
            const timelineContainer = document.querySelector('.timeline-container');

            if (!timelineProgress || !timelineContainer) return;

            function updateTimelineProgress() {
                const containerRect = timelineContainer.getBoundingClientRect();
                const windowHeight = window.innerHeight;

                // Calculate how much of the timeline container is visible
                const containerTop = containerRect.top;
                const containerBottom = containerRect.bottom;
                const containerHeight = containerRect.height;

                // Start animation when container enters viewport
                const startOffset = windowHeight * 0.8; // Start when 80% down the viewport
                const endOffset = windowHeight * 0.2;   // End when 20% from top

                let progress = 0;

                // Check if we're before the timeline starts
                if (containerTop > startOffset) {
                    progress = 0;
                }
                // Check if we're past the timeline completely
                else if (containerBottom < endOffset) {
                    progress = 1;
                }
                // We're within the timeline animation range
                else if (containerTop <= startOffset && containerBottom >= endOffset) {
                    // Calculate progress based on scroll position
                    const scrolled = startOffset - containerTop;
                    const totalScrollDistance = containerHeight + (startOffset - endOffset);
                    progress = Math.min(Math.max(scrolled / totalScrollDistance, 0), 1);
                }

                // Apply the progress to the timeline
                timelineProgress.style.height = `${progress * 100}%`;

                // Add visual effects based on progress
                if (progress > 0 && progress < 1) {
                    timelineProgress.style.boxShadow = '0 0 8px rgba(54, 116, 181, 0.6)';
                } else if (progress === 1) {
                    timelineProgress.style.boxShadow = '0 0 12px rgba(54, 116, 181, 0.8)';
                } else {
                    timelineProgress.style.boxShadow = 'none';
                }
            }

            // Listen for scroll events
            window.addEventListener('scroll', updateTimelineProgress);

            // Initial call
            updateTimelineProgress();
        }

        // Design Inspirations Slideshow
        let slideIndex = 1;
        let slideInterval;

        function showSlides(n) {
            const slides = document.querySelectorAll('.slide');
            const dots = document.querySelectorAll('.dot');

            // Check if slides exist
            if (slides.length === 0) return;

            if (n > slides.length) { slideIndex = 1; }
            if (n < 1) { slideIndex = slides.length; }

            // Find currently active slide
            const currentActiveSlide = document.querySelector('.slide[style*="opacity: 1"]') ||
                                     document.querySelector('.slide:not([style*="opacity: 0"])');
            const newActiveSlide = slides[slideIndex - 1];

            // Stage 1: Outgoing slide animation (blur fade out)
            if (currentActiveSlide && currentActiveSlide !== newActiveSlide) {
                currentActiveSlide.style.transition = 'opacity 0.4s ease-out, filter 0.4s ease-out';
                currentActiveSlide.style.opacity = '0';
                currentActiveSlide.style.filter = 'blur(8px)';
                currentActiveSlide.style.pointerEvents = 'none';
            }

            // Hide all other slides immediately
            slides.forEach((slide, index) => {
                if (slide !== currentActiveSlide && slide !== newActiveSlide) {
                    slide.style.opacity = '0';
                    slide.style.pointerEvents = 'none';
                    slide.style.filter = 'blur(0px)';
                    slide.style.transform = 'scale(1)';
                    slide.style.transition = 'none';
                }
            });

            // Remove active class from all dots
            dots.forEach(dot => {
                dot.classList.remove('active');
                dot.classList.remove('bg-brand-blue');
                dot.classList.add('bg-gray-300');
            });

            // Stage 2: Incoming slide animation (blur fade with smooth scale and subtle slide)
            if (newActiveSlide) {
                // Set initial state for incoming slide
                newActiveSlide.style.opacity = '0';
                newActiveSlide.style.filter = 'blur(8px)';
                newActiveSlide.style.transform = 'scale(0.98) translateY(10px)';
                newActiveSlide.style.pointerEvents = 'auto';
                newActiveSlide.style.transition = 'none';

                // Start the entrance animation after outgoing slide starts fading
                setTimeout(() => {
                    newActiveSlide.style.transition = 'opacity 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94), filter 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94), transform 0.8s cubic-bezier(0.16, 1, 0.3, 1)';
                    newActiveSlide.style.opacity = '1';
                    newActiveSlide.style.filter = 'blur(0px)';
                    newActiveSlide.style.transform = 'scale(1) translateY(0px)';
                }, currentActiveSlide ? 200 : 0);
            }

            // Activate current dot with smooth animation
            if (dots[slideIndex - 1]) {
                const currentDot = dots[slideIndex - 1];
                currentDot.classList.add('active');
                currentDot.classList.add('bg-brand-blue');
                currentDot.classList.remove('bg-gray-300');

                // Add a pulse effect to the active dot
                currentDot.style.transform = 'scale(1.2)';
                setTimeout(() => {
                    currentDot.style.transform = 'scale(1)';
                }, 200);
            }
        }



        function currentSlide(n) {
            const slideshowContainer = document.getElementById('slideshow-container');
            if (!slideshowContainer) return;

            clearInterval(slideInterval);
            slideIndex = n;
            showSlides(slideIndex);
            startAutoSlide();
        }

        function startAutoSlide() {
            slideInterval = setInterval(() => {
                slideIndex++;
                showSlides(slideIndex);
            }, 5000); // Change slide every 5 seconds
        }

        function initializeSlideshow() {
            // Initialize first slide properly
            const slides = document.querySelectorAll('.slide');
            if (slides.length > 0) {
                // Hide all slides initially
                slides.forEach((slide, index) => {
                    slide.style.opacity = '0';
                    slide.style.pointerEvents = 'none';
                    slide.style.filter = 'blur(0px)';
                    slide.style.transform = 'scale(1)';
                });

                // Show first slide with entrance animation
                const firstSlide = slides[0];
                firstSlide.style.opacity = '0';
                firstSlide.style.filter = 'blur(8px)';
                firstSlide.style.transform = 'scale(0.98) translateY(10px)';
                firstSlide.style.pointerEvents = 'auto';

                setTimeout(() => {
                    firstSlide.style.transition = 'opacity 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94), filter 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94), transform 0.8s cubic-bezier(0.16, 1, 0.3, 1)';
                    firstSlide.style.opacity = '1';
                    firstSlide.style.filter = 'blur(0px)';
                    firstSlide.style.transform = 'scale(1) translateY(0px)';
                }, 100);
            }

            startAutoSlide();

            // Pause auto-slide on hover
            const slideshowContainer = document.getElementById('slideshow-container');
            if (slideshowContainer) {
                slideshowContainer.addEventListener('mouseenter', () => {
                    clearInterval(slideInterval);
                });

                slideshowContainer.addEventListener('mouseleave', () => {
                    startAutoSlide();
                });
            }
        }

        // Week Modal Functions
        let escapeListenerActive = false; // Flag to track escape key listener state
        let scrollListenerActive = false; // Flag to track scroll listener state

        // Modal scroll progress functionality
        function updateModalProgress() {
            const modalContainer = document.getElementById('modalContentContainer');
            const progressBar = document.getElementById('modalProgressBar');

            if (!modalContainer || !progressBar) return;

            const scrollTop = modalContainer.scrollTop;
            const scrollHeight = modalContainer.scrollHeight;
            const clientHeight = modalContainer.clientHeight;

            // Calculate scroll percentage
            const maxScroll = scrollHeight - clientHeight;
            const scrollPercentage = maxScroll > 0 ? (scrollTop / maxScroll) * 100 : 0;

            // Update progress bar width with smooth animation
            progressBar.style.width = Math.min(scrollPercentage, 100) + '%';
        }

        function addModalScrollListener() {
            const modalContainer = document.getElementById('modalContentContainer');

            if (!modalContainer || scrollListenerActive) return;

            modalContainer.addEventListener('scroll', updateModalProgress);
            scrollListenerActive = true;
        }

        function removeModalScrollListener() {
            const modalContainer = document.getElementById('modalContentContainer');

            if (!modalContainer || !scrollListenerActive) return;

            modalContainer.removeEventListener('scroll', updateModalProgress);
            scrollListenerActive = false;
        }

        function resetModalProgress() {
            const progressBar = document.getElementById('modalProgressBar');
            if (progressBar) {
                progressBar.style.width = '0%';
            }
        }

        // Modal navigation scroll function
        function scrollToSection(sectionId, event) {
            event.preventDefault();

            const modalContainer = document.getElementById('modalContentContainer');
            const targetElement = document.getElementById(sectionId);

            if (!modalContainer || !targetElement) {
                console.warn(`Section with id '${sectionId}' not found`);
                return;
            }

            // Calculate the position relative to the modal container
            const modalRect = modalContainer.getBoundingClientRect();
            const targetRect = targetElement.getBoundingClientRect();
            const scrollTop = modalContainer.scrollTop;

            // Calculate target scroll position (with some offset for better visibility)
            const targetScrollTop = scrollTop + (targetRect.top - modalRect.top) - 80;

            // Smooth scroll to the target section
            modalContainer.scrollTo({
                top: Math.max(0, targetScrollTop),
                behavior: 'smooth'
            });
        }

        const weekData = {
            1: {
                title: "Discovery & Research",
                navigation: [
                    { id: "overview", label: "The Beginning" },
                    { id: "key-goals", label: "What I Needed" },
                    { id: "main-challenge", label: "The Rabbit Hole" },
                    { id: "breakthrough", label: "Reality Check" }
                ],
                image: {
                    gradient: "from-purple-50 to-blue-100",
                    icon: `<svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                    </svg>`,
                    description: "Discovery & Inspiration Gathering"
                },
                sections: {
                    overview: `Picture this: I'm sitting there thinking "How hard can building an e-commerce site be?" Famous last words, right? What started as a simple weekend project quickly turned into me realizing I had no clue what modern e-commerce actually looks like.`,

                    keyGoals: {
                        title: "What I Actually Needed to Figure Out",
                        items: [
                            "What makes an e-commerce site look professional (not like it's from 2010)",
                            "How real online stores organize their products and features",
                            "What tools and technologies I should actually use",
                            "How to make something that doesn't look like a student project"
                        ]
                    },

                    mainChallenge: {
                        title: "The Great Research Rabbit Hole of 2024",
                        content: `I went from "let me just check a few sites" to having 47 browser tabs open and three different design inspiration boards. My laptop started making weird noises.`,
                        details: [
                            "Spent entire days just browsing Dribbble and Behance instead of coding",
                            "Bookmarked 200+ designs I'd never be able to recreate",
                            "Got overwhelmed by all the different approaches and styles",
                            "Started questioning if I was even capable of building something good"
                        ]
                    },

                    breakthrough: {
                        title: "The Roommate Reality Check",
                        content: `My roommate walked by and asked "Dude, why do you have 47 tabs open?" That's when it hit me - I was procrastinating because I was scared to start. Solution: Pick the 3 best sites I found, steal their ideas (legally), and just start building something.`
                    }
                },
                quote: "I literally had 47 browser tabs open. My laptop was crying, my roommate was judging me, and I still hadn't written a single line of code.",
                metrics: {
                    duration: "5 days",
                    keyMetric: "47 tabs",
                    outcome: "Finally ready to code"
                }
            },
            2: {
                title: "Building the Foundation",
                navigation: [
                    { id: "overview", label: "First Code" },
                    { id: "key-goals", label: "Core Features" },
                    { id: "main-challenge", label: "CSS Battles" },
                    { id: "breakthrough", label: "The Framework" }
                ],
                image: {
                    gradient: "from-emerald-50 to-green-100",
                    icon: `<svg class="w-8 h-8 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
                    </svg>`,
                    description: "Core Development & Structure Building"
                },
                sections: {
                    overview: `Time to turn all that research into actual code! I opened VS Code, took a deep breath, and started building what would become Sentio. This is where I learned the difference between "knowing" how to code and actually building something real.`,

                    keyGoals: {
                        title: "What I Actually Built",
                        items: [
                            "A working e-commerce dashboard that looks professional",
                            "Interactive product cards with smooth hover animations",
                            "A responsive navigation system that works on all devices",
                            "Real shopping cart functionality with add/remove features"
                        ]
                    },

                    mainChallenge: {
                        title: "The Great CSS Wrestling Championship",
                        content: `I thought I knew CSS. I was very, very wrong. Every time I fixed one thing, three other things would break. Centering a div became my personal nemesis.`,
                        details: [
                            "Spent an entire afternoon trying to center one button (yes, one button)",
                            "Mobile layout looked like it went through a blender",
                            "Animations were either too fast, too slow, or just plain broken",
                            "Colors that looked great on my screen were terrible on my phone"
                        ]
                    },

                    breakthrough: {
                        title: "Discovering Tailwind CSS",
                        content: `Best decision I made: switching to Tailwind CSS. Suddenly my components looked professional, responsive design actually worked, and I could focus on building features instead of fighting with stylesheets. Game changer!`
                    }
                },
                quote: "I went from 'Why won't this center?' to 'Holy crap, this actually looks professional!' in about 6 hours.",
                metrics: {
                    duration: "8 days",
                    keyMetric: "15 components",
                    outcome: "Working foundation"
                }
            },
            3: {
                title: "The Challenge Phase",
                navigation: [
                    { id: "overview", label: "The Wall" },
                    { id: "key-goals", label: "Complex Features" },
                    { id: "main-challenge", label: "Breaking Point" },
                    { id: "breakthrough", label: "The Breakthrough" }
                ],
                image: {
                    gradient: "from-orange-50 to-red-100",
                    icon: `<svg class="w-8 h-8 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>`,
                    description: "Complex Features & Problem Solving"
                },
                sections: {
                    overview: `This is where things got real. I had a basic site, but now I needed to make it actually work like a real e-commerce platform. Interactive features, dynamic content, smooth animations - basically everything that separates a student project from something professional.`,

                    keyGoals: {
                        title: "The Complex Stuff I Tackled",
                        items: [
                            "Interactive shopping cart with real-time updates and animations",
                            "Dynamic product filtering and search functionality",
                            "Smooth page transitions and micro-interactions throughout",
                            "Mobile-responsive dashboard that actually works on all devices"
                        ]
                    },

                    mainChallenge: {
                        title: "The Week I Almost Gave Up",
                        content: `Everything that could go wrong, did. JavaScript errors everywhere, animations breaking on mobile, and the cart functionality that took me 3 days to build stopped working after one small change.`,
                        details: [
                            "Spent 14 hours straight debugging why the cart wasn't updating",
                            "Mobile animations looked like a glitchy mess",
                            "One small CSS change broke the entire layout",
                            "Started questioning if I was cut out for this whole coding thing"
                        ]
                    },

                    breakthrough: {
                        title: "The 2 AM Miracle",
                        content: `At 2 AM on day 9, after my roommate brought me food for the third time, everything suddenly clicked. The cart worked, animations were smooth, and mobile looked perfect. I literally did a victory dance in my room.`
                    }
                },
                quote: "I went from 'I'm never going to figure this out' to 'I can't believe I actually built this' in one magical debugging session.",
                metrics: {
                    duration: "12 days",
                    keyMetric: "14 hours",
                    outcome: "Everything works!"
                }
            },
            4: {
                title: "Launch & Victory",
                navigation: [
                    { id: "overview", label: "Final Push" },
                    { id: "key-goals", label: "Polish & Perfect" },
                    { id: "main-challenge", label: "Production Panic" },
                    { id: "breakthrough", label: "Launch Day!" }
                ],
                image: {
                    gradient: "from-yellow-50 to-orange-100",
                    icon: `<svg class="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                    </svg>`,
                    description: "Final Polish & Launch Success"
                },
                sections: {
                    overview: `The final stretch! Everything was working, but now I needed to make it perfect. This is where I learned that the last 10% of a project takes 90% of your time, and that "it works on my machine" is the most dangerous phrase in programming.`,

                    keyGoals: {
                        title: "Making It Launch-Ready",
                        items: [
                            "Test everything on different browsers and devices (spoiler: Safari hates me)",
                            "Optimize loading speeds and performance for real users",
                            "Polish all the little details that make it feel professional",
                            "Deploy to the web without breaking everything I've built"
                        ]
                    },

                    mainChallenge: {
                        title: "The Production Nightmare",
                        content: `I thought I was done. I was so wrong. What worked perfectly on my laptop turned into a disaster when real people tried to use it. Safari made my site look like it was from 1995.`,
                        details: [
                            "Loading time went from 2 seconds to 23 seconds (yes, really)",
                            "Safari decided my beautiful layout should look like abstract art",
                            "Forgot to set up environment variables - everything showed 'undefined'",
                            "Stayed up until 6 AM fixing deployment issues"
                        ]
                    },

                    breakthrough: {
                        title: "The 6:30 AM Victory",
                        content: `At 6:30 AM, after the longest debugging session of my life, everything finally clicked. Fast loading, perfect on all devices, and real people could actually use it. I called my mom to show her my "professional e-commerce platform." Best feeling ever.`
                    }
                },
                quote: "I stared at the final result for 20 minutes thinking 'I actually built this. ME. A person who still Googles how to center a div.'",
                metrics: {
                    duration: "9 days",
                    keyMetric: "1.8 seconds",
                    outcome: "Successfully launched!"
                }
            }
        };

        function openWeekModal(weekNumber) {
            const modal = document.getElementById('weekModal');
            const modalContent = document.getElementById('modalContent');

            if (!modal || !modalContent) {
                console.error('Modal elements not found');
                return;
            }

            const data = weekData[weekNumber];
            if (!data) return;

            // Set phase-specific data attribute
            const phaseNames = ['research', 'building', 'challenge', 'launch'];
            const phaseName = phaseNames[weekNumber - 1];
            modalContent.setAttribute('data-phase', phaseName);

            modalContent.innerHTML = `
                <!-- Header with phase-specific gradient -->
                <div class="modal-header h-48 bg-gradient-to-br ${data.image.gradient} flex items-center justify-center relative rounded-t-lg overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent animate-pulse"></div>
                    <div class="text-center relative z-10 transform transition-all duration-500 hover:scale-105">
                        <div class="w-16 h-16 bg-white/20 rounded-lg flex items-center justify-center mb-3 backdrop-blur-sm border border-white/30">
                            ${data.image.icon}
                        </div>
                        <p class="text-sm text-white/90 font-medium">${data.image.description}</p>
                    </div>
                    <div class="absolute top-4 left-4 w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white font-bold shadow-lg border border-white/30">
                        ${weekNumber === 1 ? 'R' : weekNumber === 2 ? 'B' : weekNumber === 3 ? 'C' : 'L'}
                    </div>
                </div>

                <div class="p-8 md:p-12">
                    <!-- Navigation -->
                    <div class="modal-nav-header mb-8">
                        <div class="modal-nav-links">
                            ${data.navigation.map(nav => `
                                <a href="#${nav.id}" class="modal-nav-link transform transition-all duration-200 hover:scale-105" onclick="scrollToSection('${nav.id}', event)">
                                    ${nav.label}
                                </a>
                            `).join('')}
                        </div>
                    </div>

                    <!-- Title -->
                    <h3 class="text-3xl font-bold text-gray-900 mb-8 text-center">${data.title}</h3>

                    <!-- Overview -->
                    <div class="mb-12" id="overview">
                        <div class="phase-section text-gray-700 leading-relaxed text-lg text-center max-w-4xl mx-auto">
                            ${data.sections.overview}
                        </div>
                    </div>

                    <!-- Goals Section -->
                    <div class="mb-12" id="key-goals">
                        <div class="phase-section">
                            <h4 class="text-2xl font-semibold text-gray-900 mb-6 flex items-center justify-center">
                                <span class="w-4 h-4 bg-blue-600 rounded-full mr-3"></span>
                                ${data.sections.keyGoals.title}
                            </h4>
                            <div class="grid md:grid-cols-2 gap-4">
                                ${data.sections.keyGoals.items.map((item, index) => `
                                    <div class="bg-white/80 border border-gray-200 p-4 rounded-lg hover:shadow-md transition-all duration-300 transform hover:-translate-y-1 group">
                                        <div class="flex items-start">
                                            <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center mr-3 flex-shrink-0 group-hover:scale-110 transition-transform duration-200">
                                                <span class="text-white font-bold text-sm">${index + 1}</span>
                                            </div>
                                            <p class="text-gray-700 leading-relaxed">${item}</p>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>

                    <!-- Challenge Section -->
                    <div class="mb-12" id="main-challenge">
                        <div class="phase-section">
                            <h4 class="text-2xl font-semibold text-gray-900 mb-6 flex items-center justify-center">
                                <span class="w-4 h-4 bg-blue-600 rounded-full mr-3"></span>
                                ${data.sections.mainChallenge.title}
                            </h4>
                            <div class="bg-white/80 border border-gray-200 p-6 rounded-xl mb-6">
                                <p class="text-gray-700 text-lg leading-relaxed text-center">${data.sections.mainChallenge.content}</p>
                            </div>
                            <div class="grid md:grid-cols-2 gap-4">
                                ${data.sections.mainChallenge.details.map((detail, index) => `
                                    <div class="bg-white/80 border border-gray-200 p-4 rounded-lg hover:bg-white transition-colors duration-200">
                                        <div class="flex items-start">
                                            <div class="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0 mt-1">
                                                <span class="text-red-600 font-bold text-xs">!</span>
                                            </div>
                                            <p class="text-gray-700 leading-relaxed">${detail}</p>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>

                    <!-- Breakthrough Section -->
                    <div class="mb-12" id="breakthrough">
                        <div class="phase-section">
                            <h4 class="text-2xl font-semibold text-gray-900 mb-6 flex items-center justify-center">
                                <span class="w-4 h-4 bg-blue-600 rounded-full mr-3"></span>
                                ${data.sections.breakthrough.title}
                            </h4>
                            <div class="bg-green-50/80 border border-green-200 p-6 rounded-xl text-center">
                                <div class="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <span class="text-white font-bold text-lg">✓</span>
                                </div>
                                <p class="text-gray-700 text-lg leading-relaxed">${data.sections.breakthrough.content}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Quote Section -->
                    <div class="phase-section bg-gradient-to-r from-blue-600 to-purple-600 text-white text-center mb-8">
                        <div class="text-4xl mb-4 opacity-50">"</div>
                        <p class="text-lg italic leading-relaxed">${data.quote}</p>
                    </div>

                    <!-- Metrics -->
                    <div class="phase-section">
                        <div class="grid grid-cols-3 gap-4 text-center">
                            <div class="bg-white/80 p-4 rounded-lg border border-gray-200 hover:bg-white transition-colors duration-200">
                                <div class="text-2xl font-bold text-blue-600">${data.metrics.duration}</div>
                                <div class="text-sm text-gray-600">Duration</div>
                            </div>
                            <div class="bg-white/80 p-4 rounded-lg border border-gray-200 hover:bg-white transition-colors duration-200">
                                <div class="text-2xl font-bold text-blue-600">${data.metrics.keyMetric}</div>
                                <div class="text-sm text-gray-600">Key Metric</div>
                            </div>
                            <div class="bg-white/80 p-4 rounded-lg border border-gray-200 hover:bg-white transition-colors duration-200">
                                <div class="text-2xl font-bold text-green-600">✓</div>
                                <div class="text-sm text-gray-600">${data.metrics.outcome}</div>
                            </div>
                        </div>
                    </div>
                </div>`;


            // Show modal with animations
            modal.classList.remove('hidden');
            document.body.classList.add('modal-open');

            requestAnimationFrame(() => {
                requestAnimationFrame(() => {
                    modal.classList.add('show');
                    resetModalProgress();
                    addModalScrollListener();
                });
            });

            if (!escapeListenerActive) {
                document.addEventListener('keydown', handleEscapeKey);
                escapeListenerActive = true;
            }
        }

        function closeWeekModal() {
            const modal = document.getElementById('weekModal');

            // Add null check for DOM element
            if (!modal) {
                console.error('Modal element not found');
                return;
            }

            modal.classList.remove('show');

            // Clean up scroll progress listener
            removeModalScrollListener();
            resetModalProgress();

            // Wait for animation to complete before hiding
            setTimeout(() => {
                modal.classList.add('hidden');
                document.body.classList.remove('modal-open');
            }, 400); // Match the CSS transition duration

            // Remove escape key listener and update flag
            if (escapeListenerActive) {
                document.removeEventListener('keydown', handleEscapeKey);
                escapeListenerActive = false;
            }
        }

        function handleEscapeKey(event) {
            if (event.key === 'Escape') {
                closeWeekModal();
            }
        }

        // Close modal when clicking on backdrop
        document.addEventListener('click', function(event) {
            const modal = document.getElementById('weekModal');
            const modalContainer = modal.querySelector('.modal-container');

            // Close if clicking on the modal backdrop or modal container (but not the content)
            if (event.target === modal || event.target === modalContainer) {
                closeWeekModal();
            }
        });

        // Image Modal Functions
        function openImageModal(imageSrc, imageAlt) {
            const modal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');
            const modalCaption = document.getElementById('modalImageCaption');

            if (!modal || !modalImage || !modalCaption) {
                console.error('Modal elements not found');
                return;
            }

            // Set image source and alt text
            modalImage.src = imageSrc;
            modalImage.alt = imageAlt;
            modalCaption.textContent = imageAlt;

            // Show modal and lock body scroll
            modal.classList.remove('hidden');
            document.body.classList.add('image-modal-open');

            // Use requestAnimationFrame for smooth entrance animation
            requestAnimationFrame(() => {
                requestAnimationFrame(() => {
                    modal.classList.add('show');
                });
            });
        }

        function closeImageModal() {
            const modal = document.getElementById('imageModal');

            if (!modal) {
                console.error('Modal element not found');
                return;
            }

            // Start exit animation
            modal.classList.remove('show');

            // Wait for animation to complete before hiding modal
            setTimeout(() => {
                modal.classList.add('hidden');
                document.body.classList.remove('image-modal-open');
            }, 900); // Match the longest animation duration (0.9s)
        }

        // Close modal when clicking outside the image
        document.addEventListener('click', function(event) {
            const modal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');

            if (event.target === modal && !modalImage.contains(event.target)) {
                closeImageModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const modal = document.getElementById('imageModal');
                if (!modal.classList.contains('hidden')) {
                    closeImageModal();
                }
            }
        });

        // ===== QUOTE ANIMATION =====
        function initializeQuoteAnimation() {
            const quoteContainer = document.querySelector('.quote-container');
            if (!quoteContainer) return;

            const quoteText = quoteContainer.querySelector('.quote-text');
            const quoteHighlights = quoteContainer.querySelectorAll('.quote-highlight');
            const quoteAttribution = quoteContainer.querySelector('.quote-attribution');

            const observerOptions = {
                root: null,
                rootMargin: '0px 0px -20% 0px',
                threshold: 0.3
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        // Trigger container animation first
                        setTimeout(() => {
                            quoteContainer.classList.add('animate');
                        }, 100);

                        // Trigger text animation
                        setTimeout(() => {
                            if (quoteText) quoteText.classList.add('animate');
                        }, 300);

                        // Trigger highlight animations with stagger
                        quoteHighlights.forEach((highlight, index) => {
                            setTimeout(() => {
                                highlight.classList.add('animate');
                            }, 500 + (index * 200));
                        });

                        // Trigger attribution animation last
                        setTimeout(() => {
                            if (quoteAttribution) quoteAttribution.classList.add('animate');
                        }, 1100);

                        // Stop observing after animation is triggered
                        observer.unobserve(entry.target);
                    }
                });
            }, observerOptions);

            observer.observe(quoteContainer);
        }

        // Typing Animation System
        function initializeTypingAnimations() {
            const inputs = document.querySelectorAll('#contact-form input, #contact-form textarea');

            inputs.forEach(input => {
                let previousValue = '';
                let typingTimeout;

                // Add typing class to input
                input.classList.add('typing-input');

                input.addEventListener('input', function(e) {
                    const currentValue = e.target.value;
                    const previousLength = previousValue.length;
                    const currentLength = currentValue.length;

                    // Clear previous timeout
                    clearTimeout(typingTimeout);

                    // Add typing indicator
                    this.classList.add('typing');

                    // Create character animation effect
                    if (currentLength > previousLength) {
                        // Character added - animate in
                        animateCharacterIn(this, currentLength - 1);
                    } else if (currentLength < previousLength) {
                        // Character removed - animate out
                        animateCharacterOut(this, currentLength);
                    }

                    // Remove typing indicator after delay
                    typingTimeout = setTimeout(() => {
                        this.classList.remove('typing');
                    }, 1000);

                    previousValue = currentValue;
                });

                // Remove typing indicator when input loses focus
                input.addEventListener('blur', function() {
                    this.classList.remove('typing');
                });
            });
        }

        function animateCharacterIn(input, charIndex) {
            // Create a subtle pulse effect for the input
            input.style.transform = 'scale(1.01)';
            setTimeout(() => {
                input.style.transform = 'scale(1)';
            }, 150);
        }

        function animateCharacterOut(input, charIndex) {
            // Create a subtle shrink effect for the input
            input.style.transform = 'scale(0.99)';
            setTimeout(() => {
                input.style.transform = 'scale(1)';
            }, 100);
        }

        // Dynamic Textarea with Spring Physics
        function initializeDynamicTextarea() {
            const textarea = document.getElementById('message');
            if (!textarea) return;

            let isExpanded = false;
            let expandTimeout;
            let collapseTimeout;

            // Calculate content height
            function getContentHeight(element) {
                const clone = element.cloneNode(true);
                clone.style.height = 'auto';
                clone.style.position = 'absolute';
                clone.style.visibility = 'hidden';
                clone.style.top = '-9999px';
                document.body.appendChild(clone);
                const height = Math.max(120, clone.scrollHeight + 20); // Min 120px
                document.body.removeChild(clone);
                return height;
            }

            // Expand animation
            function expandTextarea() {
                if (isExpanded) return;

                clearTimeout(collapseTimeout);
                isExpanded = true;

                const targetHeight = getContentHeight(textarea);
                textarea.style.setProperty('--target-height', `${targetHeight}px`);

                // Remove any existing animation classes
                textarea.classList.remove('textarea-collapsing', 'textarea-jiggle');

                // Add expanding animation
                textarea.classList.add('textarea-expanding');

                // Add subtle jiggle for delight
                setTimeout(() => {
                    textarea.classList.add('textarea-jiggle');
                }, 200);

                // Clean up animation classes
                setTimeout(() => {
                    textarea.classList.remove('textarea-expanding', 'textarea-jiggle');
                    textarea.style.height = `${targetHeight}px`;
                    textarea.style.overflow = 'auto';
                }, 500);
            }

            // Collapse animation
            function collapseTextarea() {
                if (!isExpanded) return;

                clearTimeout(expandTimeout);
                isExpanded = false;

                const currentHeight = textarea.offsetHeight;
                textarea.style.setProperty('--current-height', `${currentHeight}px`);
                textarea.style.overflow = 'hidden';

                // Remove any existing animation classes
                textarea.classList.remove('textarea-expanding', 'textarea-jiggle');

                // Add collapsing animation
                textarea.classList.add('textarea-collapsing');

                // Clean up animation classes
                setTimeout(() => {
                    textarea.classList.remove('textarea-collapsing');
                    textarea.style.height = '60px';
                }, 600);
            }

            // Check if should expand based on content
            function checkExpansionTriggers() {
                const content = textarea.value;
                const shouldExpand = content.length > 50 || textarea.scrollHeight > 60;

                if (shouldExpand && !isExpanded) {
                    expandTextarea();
                } else if (!shouldExpand && isExpanded && !textarea.matches(':focus')) {
                    collapseTextarea();
                }
            }

            // Event listeners
            textarea.addEventListener('focus', () => {
                clearTimeout(collapseTimeout);
                expandTimeout = setTimeout(expandTextarea, 100);
            });

            textarea.addEventListener('blur', () => {
                clearTimeout(expandTimeout);
                collapseTimeout = setTimeout(() => {
                    if (textarea.value.length <= 50) {
                        collapseTextarea();
                    }
                }, 200);
            });

            textarea.addEventListener('input', () => {
                if (isExpanded) {
                    // Adjust height dynamically when expanded
                    const targetHeight = getContentHeight(textarea);
                    textarea.style.height = `${targetHeight}px`;
                }
                checkExpansionTriggers();
            });

            // Handle clicks outside
            document.addEventListener('click', (e) => {
                if (!textarea.contains(e.target) && !textarea.matches(':focus')) {
                    if (textarea.value.length <= 50) {
                        collapseTimeout = setTimeout(collapseTextarea, 100);
                    }
                }
            });
        }

        // Initialize everything when page loads
        document.addEventListener('DOMContentLoaded', () => {
            initializeStoryNavigation();
            initializeScrollAnimations();
            initializeCSSNightmaresAnimation();
            initializeInspirationAnimation();
            initializeCopyPasteAnimation();
            initializeTimelineAnimation();
            initializeSlideshow();
            initializeQuoteAnimation();
            initializeSentioTodayAnimations();
            initializeTypingAnimations();
            initializeDynamicTextarea();
        });

        // Sentio Today Section - Simple Animations
        function initializeSentioTodayAnimations() {
            // Simple intersection observer for fade-in animation
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-fade-in-up');
                    }
                });
            }, { threshold: 0.2 });

            // Observe the Sentio Today section
            const sentioSection = document.querySelector('#current');
            if (sentioSection) {
                observer.observe(sentioSection);
            }
        }
    </script>

    <!-- Week Modal - Global Overlay -->
    <div id="weekModal" class="fixed inset-0 bg-black bg-opacity-50 z-[9999] hidden">
        <!-- Modal Scroll Progress Indicator -->
        <div class="modal-progress-container">
            <div class="modal-progress-bar" id="modalProgressBar"></div>
        </div>

        <div class="modal-container">
            <div class="modal-content bg-white rounded-lg max-w-2xl w-full max-h-[85vh] overflow-y-auto relative shadow-2xl" id="modalContentContainer">
                <!-- Close Button -->
                <button onclick="closeWeekModal()" class="absolute top-4 right-4 w-10 h-10 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center transition-all duration-200 z-10 hover:scale-110">
                    <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>

                <!-- Modal Content -->
                <div id="modalContent">
                    <!-- Content will be dynamically inserted here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Image Modal for Full View -->
    <div id="imageModal" class="fixed inset-0 z-[10000] hidden flex items-center justify-center p-4">
        <!-- Close Button -->
        <button onclick="closeImageModal()" class="modal-close-btn absolute top-4 right-4 w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center z-10">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>

        <!-- Image Container -->
        <div class="relative max-w-7xl max-h-full w-full h-full flex items-center justify-center">
            <img id="modalImage" src="" alt="" class="max-w-full max-h-full object-contain rounded-lg shadow-2xl">
        </div>

        <!-- Image Caption - Centered -->
        <div class="modal-caption absolute bottom-4 bg-black bg-opacity-60 text-white px-6 py-3 rounded-full backdrop-blur-sm">
            <p id="modalImageCaption" class="text-sm font-medium text-center"></p>
        </div>
    </div>

    <script>
        // Logo Animation JavaScript
        const logoContainer = document.getElementById('logo-container');
        const logoText = document.getElementById('logo-text');
        const fadeInElements = document.querySelectorAll('.fade-in-element');

        function playLogoAnimation() {
            // Hide all elements initially except logo
            document.body.classList.add('animation-loading', 'animation-active');

            // Ensure back button is hidden initially
            const backBtn = document.querySelector('.back-btn');
            if (backBtn) {
                backBtn.style.opacity = '0';
                backBtn.style.visibility = 'hidden';
                backBtn.classList.remove('reveal-element', 'visible');
            }

            // Make logo container visible immediately and start the animation
            logoContainer.style.opacity = '1';
            logoContainer.style.visibility = 'visible';
            logoContainer.classList.add('animate-logo-reveal');

            // Show logo text after logo animation starts
            setTimeout(() => {
                logoText.classList.add('animate-logo-text');
                document.body.classList.remove('animation-loading');
            }, 1000);

            // Reveal other elements after logo animation completes
            setTimeout(() => {
                revealPageElements();
            }, 2500);

            // Re-enable interactions after everything is revealed
            setTimeout(() => {
                document.body.classList.remove('animation-active');
            }, 4000); // Extended time for smooth reveals

            // Mark animation as played for this session
            sessionStorage.setItem('storyLogoAnimationPlayed', 'true');
        }

        function revealPageElements() {
            // Reveal back button
            const backBtn = document.querySelector('.back-btn');
            if (backBtn) {
                backBtn.classList.add('reveal-element');
                setTimeout(() => backBtn.classList.add('visible'), 100);
            }

            // Reveal fade-in elements with stagger
            fadeInElements.forEach((el, index) => {
                el.classList.add('reveal-element');
                setTimeout(() => {
                    el.classList.add('visible');
                    el.style.opacity = '1';
                    el.style.transform = 'translateY(0)';
                }, 200 + (index * 150));
            });

            // Reveal navigation dots with stagger
            const navItems = document.querySelectorAll('.story-nav-item');
            navItems.forEach((item, index) => {
                setTimeout(() => {
                    item.classList.add('reveal');
                }, 600 + (index * 100));
            });
        }

        function skipToFinalState() {
            // Remove any loading states
            document.body.classList.remove('animation-loading', 'animation-active');

            // Skip animation and show final state immediately
            logoContainer.style.transform = 'translate(0, 0) scale(1)';
            logoContainer.style.opacity = '1';
            logoContainer.style.visibility = 'visible';
            logoText.style.opacity = '1';

            // Show all fade-in elements immediately
            fadeInElements.forEach((el) => {
                el.style.opacity = '1';
                el.style.transform = 'translateY(0)';
                el.classList.add('animate-fade-in', 'reveal-element', 'visible');
            });

            // Show back button immediately
            const backBtn = document.querySelector('.back-btn');
            if (backBtn) {
                backBtn.style.opacity = '1';
                backBtn.style.visibility = 'visible';
                backBtn.classList.add('reveal-element', 'visible');
            }

            // Show navigation dots immediately
            const navItems = document.querySelectorAll('.story-nav-item');
            navItems.forEach((item) => {
                item.classList.add('reveal');
                item.style.opacity = '1';
                item.style.visibility = 'visible';
            });
        }

        // Logo animation flag persists throughout the session
        // No cleanup needed - animation should only play once per session

        // Check if animation should play when page loads
        document.addEventListener('DOMContentLoaded', () => {
            const hasPlayedAnimation = sessionStorage.getItem('storyLogoAnimationPlayed');

            if (hasPlayedAnimation === 'true') {
                // Animation already played this session, skip to final state
                skipToFinalState();
            } else {
                // First visit this session, play the animation
                playLogoAnimation();
            }
        });

        // Week Card Scroll Animations
        document.addEventListener('DOMContentLoaded', () => {
            const weekCards = document.querySelectorAll('.week-card');

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.remove('week-card-hidden');
                        entry.target.classList.add('week-card-visible');
                        observer.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.3,
                rootMargin: '0px 0px -50px 0px'
            });

            weekCards.forEach(card => {
                observer.observe(card);
            });
        });

        // Journey Card Intersection Observer
        function initializeJourneyCardObserver() {
            const journeyCard = document.querySelector('.journey-card');

            if (!journeyCard) return;

            const observerOptions = {
                threshold: 0.2,
                rootMargin: '0px 0px -50px 0px'
            };

            const cardObserver = new IntersectionObserver((entries) => {
                entries.forEach((entry) => {
                    if (entry.isIntersecting) {
                        animateJourneyCard(entry.target);
                        cardObserver.unobserve(entry.target);
                    }
                });
            }, observerOptions);

            cardObserver.observe(journeyCard);
        }

        // Animate journey card with sophisticated timing
        function animateJourneyCard(card) {
            // Phase 1: Card entry animation (0ms)
            card.classList.add('animate-in');

            // Phase 2: Icon animation (600ms after card)
            setTimeout(() => {
                const icon = card.querySelector('.journey-card-icon');
                if (icon) {
                    icon.classList.add('animate-in');
                }
            }, 600);

            // Phase 3: Title animation (1000ms after card)
            setTimeout(() => {
                const title = card.querySelector('.journey-card-title');
                if (title) {
                    title.classList.add('animate-in');
                }
            }, 1000);

            // Phase 4: Description animation (1400ms after card)
            setTimeout(() => {
                const description = card.querySelector('.journey-card-description');
                if (description) {
                    description.classList.add('animate-in');
                }
            }, 1400);

            // Phase 5: Button animation (1800ms after card)
            setTimeout(() => {
                const button = card.querySelector('.journey-card-button');
                if (button) {
                    button.classList.add('animate-in');
                }
            }, 1800);
        }

        // Initialize journey card observer when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            initializeJourneyCardObserver();
            initializeJourneyPreview();
        });

        // Journey Preview Interactions
        function initializeJourneyPreview() {
            const button = document.querySelector('.preview-trigger');
            const container = document.querySelector('.journey-card-container');
            const iframe = document.querySelector('.preview-iframe');

            if (!button || !container || !iframe) return;

            let isActivated = false;

            // Handle click event for preview activation and navigation
            button.addEventListener('click', (e) => {
                e.preventDefault(); // Always prevent default navigation

                if (!isActivated) {
                    // First click - show preview and change text
                    isActivated = true;

                    // Start text animation - slide out original text
                    button.classList.add('text-changing');

                    // After original text slides out, show new text
                    setTimeout(() => {
                        button.classList.remove('text-changing');
                        button.classList.add('text-changed');

                        // Show preview after new text appears
                        setTimeout(() => {
                            container.classList.add('preview-active');
                            console.log('Preview activated via button click');
                        }, 200);

                    }, 400);
                }

                // Set sessionStorage to trigger intro on next page
                sessionStorage.setItem('playIntroSequence', 'true');

                // Wait 3.2 seconds before starting animation (works for both first and subsequent clicks)
                setTimeout(() => {
                    // Create minimalistic overlay element
                    const overlay = document.createElement('div');
                    overlay.className = 'page-transition-overlay';
                    document.body.appendChild(overlay);

                    // Trigger slide animation after a brief moment
                    setTimeout(() => {
                        overlay.classList.add('active');
                    }, 50);

                    // Navigate after overlay slide completes
                    setTimeout(() => {
                        window.location.href = 'improved-journey.html';
                    }, 1300); // 1.2s slide + 100ms buffer
                }, 3200); // 3.2 second delay before animation starts
            });

            // Ensure iframe loads properly
            iframe.addEventListener('load', () => {
                console.log('Journey preview loaded successfully');
            });

            iframe.addEventListener('error', () => {
                console.log('Journey preview failed to load');
                // Fallback: could show a placeholder or error message
            });
        }


    </script>
</body>
</html>
